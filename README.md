# data-cockpit

## Project setup
```
npm install --global yarn 
yarn  
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).


#### Request interface
const res = this.$scmp_api.home.getTopInfo()