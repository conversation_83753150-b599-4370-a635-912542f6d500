btn_grounp
<template>
  <div class="basicInformation-page">
    <div class="basicInformation-main_left">
      <div class="demo-form-inline">
        <el-input v-model="formInline.user" placeholder="请输入联络站编号进行查询"></el-input>
        <el-button type="primary" @click="onSubmit">查询</el-button>
      </div>

      <el-collapse v-model="activeNames" accordion>
        <el-collapse-item style="margin-top: 25px" :name="item.level" v-for="(item, index) in list" :key="index">
          <template slot="title">
            <div class="collapse_title">
              <div>{{ item.name }}</div>
              <div class="icon_warp">
                <div class="icon_text">
                  {{ judgeActive("1") !== -1 ? "展开" : "收起" }}
                </div>
                <div :class="judgeActive('1') !== -1 ? 'downArrow2' : 'downArrow1'"></div>
              </div>
            </div>
          </template>
          <div class="select-content">
            <div class="select-content_warp" :class="{ isSelect: ele.status === 1 }" v-for="(ele, ex) in item.onList"
              :key="ex">
              <div>{{ ele.title }}</div>
              <div v-if="ele.status === 1">视频中</div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="basicInformation-main_right">
      <div class="top" v-if="voiceStatus == 1">
        <div>等待连线</div>
        <div @click="send(1)">点击屏幕连线通话</div>
      </div>
      <div class="top" v-if="voiceStatus == 2">
        <div>来自“广州市人大代表工委”的连线邀请</div>
        <div @click="send(1)">点击开始连线通话</div>
      </div>
      <div class="top1" v-if="voiceStatus == 3"></div>
      <div class="btn_grounp">
        <img class="title" :src="require('@/assets/icon/icon1.png')" alt="" />
        <img class="title" :src="require('@/assets/icon/icon2.png')" alt="" />
        <img v-if="voiceStatus == 1" class="title-center" :src="require('@/assets/icon/icon3.png')" @click="send(1)"
          alt="" />
        <img v-if="voiceStatus == 2" class="title-center" :src="require('@/assets/icon/icon6.png')" @click="send(2)"
          alt="" />
        <img v-if="voiceStatus == 3" class="title-center" :src="require('@/assets/icon/icon6.png')" @click="send(3)"
          alt="" />
        <img class="title" :src="require('@/assets/icon/icon4.png')" alt="" />
        <img class="title" :src="require('@/assets/icon/icon5.png')" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "basicInformation-index",
  components: {},
  data() {
    return {
      voiceStatus: "1",
      formInline: {
        user: "",
      },
      activeNames: ["0"],

      list: [
        {
          name: "越秀区",
          level: "0",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 1,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },
        {
          name: "海珠区",
          level: "2",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },
        {
          name: "荔湾区",
          level: "1",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },

        {
          name: "天河区",
          level: "3",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },
        {
          name: "白云区",
          level: "4",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },
        {
          name: "黄埔区",
          level: "5",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },
        {
          name: "花都区",
          level: "7",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },
        {
          name: "番禺区",
          level: "6",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },

        {
          name: "南沙区",
          level: "8",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },
        {
          name: "从化区",
          level: "10",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },
        {
          name: "增城区",
          level: "9",
          onList: [
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
            {
              title: "第159号：广州市从化区吕用镇人大代表中心联络站",
              status: 0,
            },
          ],
        },

      ],
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() { },
  mounted() { },
  watch: {},
  methods: {
    onSubmit() {
      console.log("submit!");
    },
    //判断是否打开
    judgeActive(data) {
      return this.activeNames.indexOf(data);
    },
    checkedItem(obj) {
      console.log("选中", obj);
    },
    send(key) {
      if (key == 1) {
        this.voiceStatus = 2;
      } else if (key == 2) {
        this.voiceStatus = 3;
      } else if (key == 3) {
        this.voiceStatus = 1;
      }
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
.basicInformation-page {
  width: calc(100%);
  height: calc(100vh - 500px);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  // padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .basicInformation-main_left {
    position: relative;
    z-index: 9;
    width: 1049px;
    height: 1637px;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.03) 0%,
        rgba(255, 255, 255, 0) 100%);
    border-radius: 4px 4px 4px 4px;
    border: 2px solid rgba(5, 92, 255, 0.42);
    overflow-y: auto;

    .collapse_title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 1010px;
      height: 75px;
      background: linear-gradient(270deg, #001947 0%, #3877f2 100%),
        rgba(31, 198, 255, 0.2);
      border-radius: 0px 0px 0px 0px;
      border: 1px solid #a7a7a7;

      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: bold;
      font-size: 30px;
      color: #ffffff;
      line-height: 75px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding-left: 41px;
      padding-right: 41px;

      .icon_warp {
        display: flex;
        flex-direction: row;

        .icon_text {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 24px;
          color: #ffffff;
          line-height: 81px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-right: 12px;
        }
      }
    }
  }

  .basicInformation-main_right {
    flex: 1;
    padding: 95px 73px 172px 83px;

    .top {
      width: 2367px;
      height: 1331.44px;
      background: url("@/assets/image/voice_lj.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      // align-items: center;
      justify-content: center;

      :first-child {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 80px;
        color: #ffffff;
        line-height: 94px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      :last-child {
        display: block;
        background: #00e3a5;
        border-radius: 5px 5px 5px 5px;
        padding: 50px 133px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 40px;
        color: #ffffff;
        line-height: 32px;
        width: max-content;
        font-style: normal;
        text-transform: none;
        margin: 65px auto 0;
      }
    }

    .top1 {
      width: 2367px;
      height: 1331.44px;
      background: url("@/assets/image/voice_loading.png") no-repeat;
      background-size: 100% 100%;
    }

    .btn_grounp {
      height: 133px;
      width: 516px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;

      img {
        width: 56px;
        height: 56px;
      }

      .title-center {
        width: 72px;
        height: 72px;
      }
    }
  }

  ::v-deep .el-collapse-item__arrow {
    display: none;
  }

  //下方两个是打开和关闭的图标
  .downArrow1 {
    margin-top: 36px;
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-bottom: 14px solid #ffffff;
  }

  .downArrow2 {
    margin-top: 36px;
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 14px solid #ffffff;
  }

  //头部穿透，让头部内容在两端，本身固有属性是flex
  ::v-deep .el-collapse-item__header {
    justify-content: space-between;
  }

  ::v-deep .el-collapse-item__header {
    height: 75px;
    line-height: 75px;
  }

  ::v-deep .el-collapse-item__wrap {
    background: transparent;
    padding: 25px;
  }

  ::v-deep .el-collapse-item__content {
    padding-bottom: 0;
  }

  .select-content {
    background: transparent;

    .select-content_warp {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 30px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding: 22px 46px 16px 20px;
    }

    .isSelect {
      background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
      border-radius: 3px 3px 3px 3px;
    }
  }

  .demo-form-inline {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background: transparent;
    border: 0;
    height: 57px;

    ::v-deep .el-input {
      width: 885px;
      height: 57px;
      border-radius: 0px 0px 0px 0px;
      border: 1px solid #e3e3e3;
      background: transparent;
    }

    ::v-deep .el-input__inner {
      height: 57px;
      background: transparent;
    }

    ::v-deep .el-button--primary {
      width: 115px;
      height: 57px;
      background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
      border-radius: 4px 4px 4px 4px;

      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 28px;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
