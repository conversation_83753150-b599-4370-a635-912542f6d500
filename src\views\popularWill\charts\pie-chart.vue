<template>
  <div class="pie-chart" style="width: 100%; height: 100%">
    <div ref="chart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "Pie<PERSON><PERSON>",
  props: {
    pieData: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
          textStyle: {
            fontSize: 32,
            fontWeight: "bold",
          },
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          itemGap: 30,  // 增加图例项之间的间距
          textStyle: {
            color: '#fff',
            fontSize: 36
          },
          data: this.pieData.map(item => item.name)
        },
        series: [
          {
            name: '数据占比',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 48,  // 增大字号
                fontWeight: 'bolder',
                formatter: '{b}: {d}%',
                color: '#fff',
                textShadow: '0px 0px 15px rgba(41, 241, 250, 0.9)',  // 增强发光效果
                padding: [10, 15],  // 增加内边距
                borderRadius: 10  // 圆角边框
              }
            },
            labelLine: {
              show: true  // 显示引导线
            },
            data: this.pieData
          }
        ]
      };
      this.chart.setOption(option);
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  }
};
</script>

<style scoped lang="less">
.pie-chart {
  width: 100%;
  height: 100%;
}
</style>
