<template>
  <div class="title-right-con">
    <div class="data-picker-box">
      <el-date-picker
        class="date-picker"
        prefix-icon="prefix-icon"
        :clearable="false"
        v-model="localValue"
        type="daterange"
        range-separator=" —— "
        start-placeholder="统计开始时间"
        end-placeholder="结束时间"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        @change="handleDateChange"
      />
    
    </div>
    
  </div>
</template>

<script>
export default {
  name: 'DateRangePickerComponent',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      localValue: this.value,
    };
  },
  watch: { 
    value(newVal) { 
      this.localValue = newVal;
    },
    localValue(newVal) { 
      this.$emit('input', newVal);
    },
  },
  methods: {
    // 处理日期变化事件
    handleDateChange(newVal) {
      console.log('日期范围变化:', newVal);
      this.$emit('change-Year', newVal);
    },
    onHenldNext() {
      this.$emit('henld-next');
    },
  },
};
</script>

<style lang="less" scoped>
.title-right-con {
  display: flex;
  align-items: center;
  .data-picker-box {
    position: relative;
    width: 280px;
    height: 36px;
    .prefix-icon {
      display: none;
    }
    ::v-deep .date-picker {
      margin-left: 18px;
      width: 100%;
      height: 100%;
      position: absolute;
      background-color: transparent;
        .el-range-input{
          background-color: transparent;
          color: #fff;
          margin-left: -10px;
          font-size: 16px;
          // 字间距
          letter-spacing: 1px;
        }
        .el-input__inner {
          background-color: transparent;
          color: #fff;
          font-size: 16px;
          padding: 0 38px 0 8px;
          border: 1px solid #b5b5b5;
          border-radius: 4px;
        }
        .el-date-range-picker {
          background-color: transparent;
          .el-date-range-picker__content {
            background-color: rgba(0, 0, 0, 0.7);
          }
          .el-date-table th,
          .el-date-table td {
            background-color: transparent;
          }
          .el-date-table td.available:hover {
            background-color: rgba(64, 158, 255, 0.1);
          }
          .el-date-table td.current:not(.disabled) {
            background-color: #409eff;
          }
          .el-input__inner{
            background-color: transparent;
          }
          .el-range-input{
            background-color: transparent;
          }
        }
      
    }
    .el-icon-arrow-down {
      display: none;
    }
  }
  .name-box {
    display: flex;
    align-items: center;
    cursor: pointer;
    .name {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #28f1fa;
      margin-left: 20px;
    }
    .icon {
      width: 28px;
    }
  }
}
</style>
