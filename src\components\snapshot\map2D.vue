<template>
  <div ref="loginTimes"></div>
</template>
<script>
import geoJson from "../geoData.json";
import * as echarts from "echarts";
export default {
  name: "wordCloud",
  props: {
    // 标题
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    // 初始化视图
    this.getIint();
  },
  methods: {
    getIint() {
      // 获取到ref绑定为loginTimes的DOM节点，以canvas的形式展现在视图层
      let myChart = this.$echarts.init(this.$refs.loginTimes);

      let data = geoJson.features.map((item) => {
        return {
          name: item.properties.name,
        };
      });

      const resData = {
        status: [
          { name: "越秀区", level: "0" },
          { name: "海珠区", level: "0" },
          { name: "荔湾区", level: "0" },
          { name: "天河区", level: "0" },
          { name: "白云区", level: "0" },
          { name: "黄埔区", level: "0" },
          { name: "番禺区", level: "0" },
          { name: "花都区", level: "0" },
          { name: "南沙区", level: "0" },
          { name: "增城区", level: "0" },
          { name: "从化区", level: "0" },
        ],
      };

      var convertData = function (data) {
        var res = [];
        for (var i = 0; i < data.length; i++) {
          var geoCoord = geoCoordMap[data[i].name];
          if (geoCoord) {
            res.push({
              name: data[i].name,
              value: geoCoord.concat(data[i].value),
            });
          }
        }
        return res;
      };
      const regions = resData.status.map((element) => {
        const { name } = element;
        return {
          name,
          itemStyle: {
            normal: {
              areaColor: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  { offset: 0, color: "rgba(1, 102, 204, 1)" },
                  { offset: 1, color: "rgba(21, 173, 250, 1)" },
                ],
                global: false,
              },
            },
          },
        };
      });
      var toolTipData = [
        { name: "越秀区" },
        { name: "海珠区" },
        { name: "荔湾区" },
        { name: "天河区" },
        { name: "白云区" },
        { name: "黄埔区" },
        { name: "番禺区" },
        { name: "花都区" },
        { name: "南沙区" },
        { name: "从化区" },
        { name: "增城区" },
      ];

      var geoCoordMap = {
        越秀区: [113.280714, 23.125624],
        海珠区: [113.326676, 23.082002],
        荔湾区: [113.212955, 23.064264],
        // 荔湾区: [113.212955, 23.044264],
        天河区: [113.402278, 23.180254],
        白云区: [113.323943, 23.289328],
        黄埔区: [113.50827, 23.216905],
        番禺区: [113.406928, 22.973597],
        花都区: [113.214795, 23.442737],
        南沙区: [113.53738, 22.794531],
        从化区: [113.587386, 23.545283],
        增城区: [113.764273, 23.341055],
      };
      // 柱状体的顶部
      function scatterData() {
        return toolTipData.map((item) => {
          return [geoCoordMap[item.name][0], geoCoordMap[item.name][1], item];
        });
      }
      let option = {
        select: {
          // areaColor: 'rgba(255, 0, 0, 0.7)', // 点击红色
          // borderColor: '#FFF',
          // borderWidth: 2
          disabled: true ,
        },
        backgroundColor: "transparent",
        geo: [
          {
            map: "centerMap",
            aspectScale: 1,
            zoom: 0.53,
            layoutCenter: ["50%", "50%"],
            layoutSize: "180%",
            show: true,
            roam: false,
            label: {
              normal: {
                show: false,
                textStyle: { color: "#fff", fontSize: "120%" },
              },
              emphasis: {
                show: false,
              },

            },

            itemStyle: {
              normal: {
                borderWidth: 4,
                borderColor: "#fff",
                shadowColor: "rgba(2, 24, 228, 0.6)",
                shadowOffsetY: 15,
                shadowBlur: 15,
                areaColor: "rgba(5,21,35,0.1)",
              },
            },
            regions: regions,
          },
          {
            type: "map",
            map: "centerMap",
            zlevel: -2,
            aspectScale: 1,
            zoom: 0.53,
            layoutCenter: ["50%", "52%"],
            layoutSize: "180%",
            roam: false,
            silent: true,
            selectedMode: false,
            itemStyle: {
              normal: {
                borderWidth: 4,
                borderColor: "rgba(2, 24, 228, 1)",
                shadowColor: "rgba(2, 24, 228, 0.6)",
                shadowOffsetY: 0,
                shadowBlur: 0,
                // 底层区域颜色
                areaColor: {
                  type: "radial",
                  x: 0,
                  y: 0.5,
                  r: 0.5,
                  // 底层线性渐变颜色
                  colorStops: [
                    { offset: 0, color: "rgba(2, 24, 228, 1)" },
                    { offset: 1, color: "rgba(9, 176, 254, 1)" },
                  ],
                  global: false,
                },
              },
            },
          },
        ],

        series: [
          {
            name: "centerMap",
            type: "map",
            zlevel: 99,
            z: 99,
            map: "centerMap",
            aspectScale: 1,
            zoom: 0.53,
            showLegendSymbol: true,
            selectedMode: 'single',
            label: {
              normal: {
                show: false,
                textStyle: { color: "#fff", fontSize: "120%" },
              },
              emphasis: {
                show: false,
              },
            },
            itemStyle: {
              normal: {
                areaColor: {
                  type: "linear",
                  x: 1200,
                  y: 0,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 1,
                      color: "rgba(0,0,0,0)",
                    },
                  ],
                  global: false,
                },
                borderColor: "#fff",
                borderWidth: 0.2,
              },
              emphasis: {
                show: true,
                // color: "#fff",
                areaColor: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  // 鼠标移入的线性渐变颜色
                  colorStops: [
                    { offset: 1, color: "rgba(204, 165, 0, 0.70)" },
                    { offset: 0, color: "rgba(153, 124, 0, 0)" },
                  ],
                  global: false,
                },
              },
              select: {
                areaColor: 'rgba(255, 0, 0, 0.7)', // 点击红色
                borderColor: '#FFF',
                borderWidth: 2
              }
            },
            layoutCenter: ["50%", "50%"],
            layoutSize: "180%",
            markPoint: {
              symbol: "none",
            },
            data: data,
          },
          {
            type: "scatter",
            coordinateSystem: "geo",
            geoIndex: 0,
            zlevel: 999,
            label: {
              normal: {
                marginLeft: 50,
                show: true,
                position: 'right',
                formatter: function (params) {
                  return params.data[2].name;
                },
                fontSize: 32,
                color: '#fff',
                backgroundColor: '#cf4419',
                padding: [5, 10],
                borderRadius: 4,
                distance: 150,
              },
              // emphasis: {
              //   show: false
              // }
            },
            itemStyle: {
              opacity: 1,
              color: '#CF4419',
              borderColor: '#fff',
              borderWidth: 2
            },
            symbol: 'circle',
            symbolSize: 20,
            data: scatterData(),
            markLine: {
              symbol: ['none', 'none'],
              lineStyle: {
                color: '#fff',
                width: 2,
                type: 'solid'
              },
              data: scatterData().map(item => {
                return [{
                  coord: [item[0], item[1]],
                  symbol: 'none'
                }, {
                  coord: [item[0] + 0.2, item[1]], // 调整引线长度
                  symbol: 'none'
                }]
              })
            }
          },
        ],
      };

      myChart.showLoading();
      console.log(myChart.registerMap);
      //   $.getJSON(mapJson, function (geoJson) {
      this.$echarts.registerMap("centerMap", geoJson);
      myChart.hideLoading();

      myChart.setOption(option);

      let _this = this;
      myChart.on("click", function (params) {
        console.log(params)
        console.log(geoJson)
        // 仅触发事件，不设置任何高亮
        // if (params.componentType === 'series' && params.seriesType === 'scatter') {
        // 触发事件并传递参数
        console.log(params)
        console.log(geoJson)
        // 取消之前的高亮
        myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0
        });
        // 重置所有区域颜色
        // const updatedData = data.map(region => ({
        //   name: region.name,
        //   itemStyle: {
        //     areaColor: (region.name === params.name)
        //       ? 'rgba(2, 24, 228, 1)' // 点击区域红色
        //       : 'rgba(0, 0, 255, 0.5)'  // 其他区域默认色
        //   }
        // }));

        // myChart.setOption({
        //   series: [{
        //     data: updatedData
        //   }]
        // });
        // 强制点击高亮（可选）
        // myChart.dispatchAction({ type: 'unselect' });
        // myChart.dispatchAction({
        //   type: 'select',
        //   seriesIndex: 0,
        //   name: params.name
        // });
        _this.$emit("getMapInfo", params, geoJson);
        // }
        // _this.$emit("getMapInfo", params, geoJson);
      });

      //   });

      // echarts参数设置
      myChart.setOption(option);
    },
  },
};
</script>
