import { mock } from 'mockjs';

export default [{
url: '/goodsDetail/getList',
type: 'post',
response: (config) => {
return {
code: 200,
msg: 'success',
totalCount: 999,
data: mock({
'data|10': [{
id: '@id',
}]
}).data
};
}
},
{
url: '/goodsDetail/doEdit',
type: 'post',
response: (config) => {
return {
code: 200,
msg: '模拟保存成功',
};
}
},
{
url: '/goodsDetail/doDelete',
type: 'post',
response: (config) => {
return {
code: 200,
msg: '模拟删除成功',
};
}
}];