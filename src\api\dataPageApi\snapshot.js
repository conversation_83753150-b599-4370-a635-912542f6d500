import request from "@/utils/requestTemp";
import qs from "qs";

// ⼈⼤代表随⼿拍⼯作视窗-全年随⼿拍统计信息
export async function getMemberCommentHubCount(data, token) {
  return request({
    url: "cockpit/getMemberCommentHubCount",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// ⼈⼤代表随⼿拍⼯作视窗-意⻅分类统计
export async function getMemberCommentCommentCategory(data, token) {
  return request({
    url: "cockpit/getMemberCommentCommentCategory",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
  });
}

// ⼈⼤代表随⼿拍⼯作视窗-2024年每⽉办理情况
export async function getMemberCommentCompletedForYear(data, token) {
  return request({
    url: "cockpit/getMemberCommentCompletedForYear",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
  });
}

// ⼈⼤代表随⼿拍⼯作视窗-2024年代表团意⻅（反馈事项）分布情况
export async function getMemberCommentCountForDistrict(data, token) {
  return request({
    url: "cockpit/getMemberCommentCountForDistrict",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}

// ⼈⼤代表随⼿拍⼯作视窗-今⽇随⼿拍意⻅列表
export async function getDailyMemberCommentList(data, token) {
  return request({
    url: "cockpit/getDailyMemberCommentList",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
  });
}

// ⼈⼤代表随⼿拍⼯作视窗-代表随⼿拍满意度评价
export async function getMemberCommentSatisfaction(data, token) {
  return request({
    url: "cockpit/getMemberCommentSatisfaction",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
  });
}

// ⼈⼤代表随⼿拍⼯作视窗-区级满意度浮窗
export async function getMemberCommentSatisfactionForDistrict(data, token) {
  return request({
    url: "cockpit/getMemberCommentSatisfactionForDistrict",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
  });
}

// ⼈⼤代表⼯作视窗-各区联络站信息统计浮窗
export async function getLiaisonInfoForDistrict(data, token) {
  return request({
    url: "cockpit/getLiaisonInfoForDistrict",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}
