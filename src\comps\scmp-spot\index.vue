<template>
  <div class="scmp-spot">
    <div class="title" v-if="currentAreaName != ''">
      <span class="highlight">{{ spotName.substring(0, 3) }}</span>
      <span>{{ spotName.substring(3) }}</span>
    </div>
    <div class="title" v-else>
      <span>{{ spotName }}</span>
    </div>
    <el-dropdown v-if="isPullDown" trigger="click">
      <span class="el-dropdown-link">
        {{ dropDownTitle }}<i class="el-icon-arrow-down el-icon--right"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="(item, index) in dropDownList_test" :key="index"
          @click.native="checkedItem(item.path)">{{
            item.name }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <slot name="sub"></slot>
  </div>
</template>

<script>
export default {
  name: "scmp-spot",
  props: {
    areaName: {
      type: String,
      default: ""
    },
    // 标题
    spotName: {
      type: String,
      default: "",
    },
    isPullDown: {
      type: Boolean,
      default: true,
    },
    dropDownTitle: {
      type: String,
      default: "下拉菜单",
    },
    path: {
      type: String,
      default: "/",
    },
    // dropDownList: {
    //   type: Array,
    //   default: () => [],
    // },
    // 用对象以便于后期衍生
    column: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      dropDownList_test: [
        { name: "暂无切换跳转", path: "/" }
      ],
      dropDownList: [
        { name: "人大代表工作视窗", path: "/region" },
        // { name: "区域代表工作视窗", path: "/junior" },
        { name: "随手拍工作视窗", path: "/snapshot" },
        { name: "议案工作视窗", path: "/motion" },
        // { name: "联络站工作视窗", path: "/liaisonStation" },
        // { name: "联络详情工作视窗", path: "/liaisonDetail/basicInformation" },
        { name: "代表信息工作视窗", path: "/reprInfo" },
        { name: "履职工作视窗", path: "/perform" },
        { name: "人事任免工作视窗", path: "/personnel" },
        { name: "民意收集情况工作视窗", path: "/popularWill" },
        // { name: "人大工作全景视窗", path: "/rdWorkPanorama" },
      ],
    }
  },
  watch: {
    areaName(newVal) {
      this.currentAreaName = newVal;
    }
  },
  methods: {
    checkedItem(item) {
      console.log(item, "item"); //下拉菜单里面的值
      if (this.path != item) {
        console.log(this.path);
        this.$router.push(item);
      } else {
        console.error("已经在对应的路由");
      }
      // this.$emit("checkedItem", item);
    },
  },
};
</script>

<style scoped lang="less">
.scmp-spot {
  width: 1166px;
  margin-top: -65px;
  background: url("@/assets/header/scmp_spot.png") no-repeat;
  background-size: 100% 100%;
  height: 193px;
  position: relative;
  z-index: 99;

  .title {
    position: absolute;
    bottom: 34px;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 50px;
    color: #ffffff;
    line-height: 40px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .el-dropdown-link {
    cursor: pointer;
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;

    font-weight: 500;
    font-size: 28px;
    color: #ffffff;
    line-height: 60px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    // position: absolute;
    // right: 0;
  }

  .el-icon-arrow-down {
    font-size: 24px;
  }

  .demonstration {
    display: block;
    color: #8492a6;
    font-size: 14px;
    margin-bottom: 20px;
  }

  .el-dropdown {
    position: absolute;
    right: 140px;
    bottom: 38px;
  }
}

.highlight {
  color: #FFD700;
  font-size: 60px;
  text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}
</style>
