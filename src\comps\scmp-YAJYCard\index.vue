<template>
  <div
    class="scmp-card"
    :style="{
      backgroundImage: `url(${require(`@/assets/image/${backgroundImage}.png`)})`,
    }"
  >
    <div class="card-warp">
      <div class="card-title">
        <img
          class="left_bg"
          :src="require(`@/assets/image/title_bg${pictureMode}.png`)"
          alt=""
        />
        <div class="text">{{ cardName }}</div>
        <div class="title-right">
          <slot name="titleRight"></slot>
        </div>
        <img
          v-if="rightPicture"
          class="img"
          :src="require('@/assets/image/card_tip.png')"
          alt=""
        />
        <slot name="cardRight"></slot>
      </div>
    </div>
    <slot name="top"></slot>
    <slot name="main"></slot>
  </div>
</template>

<script>
export default {
  name: "scmp-card",
  props: {
    // 标题
    cardName: {
      type: String,
      default: "",
    },
    // 是否展示右边图片
    rightPicture: {
      type: Boolean,
      default: true,
    },
    // 标题背景 1或者2
    pictureMode: {
      type: String,
      default: "1",
    },
    // 背景图片
    backgroundImage: {
      type: String,
      default: "card_bg",
    },
  },
};
</script>

<style scoped lang="less">
.scmp-card {
  background-size: 100% 100%;
  position: relative;
  padding-top: 100px;
  .card-warp {
    position: absolute;
    top: -44px;
    left: -48px;
    width: 104%;
  }
  .card-title {
    overflow: hidden;
    height: 224px;
    position: relative;
    font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
    font-weight: bold;
    font-size: 34px;
    color: #ffffff;
    line-height: 40px;
    text-shadow: 0px 0px 7px rgba(75, 180, 229, 0.25),
      0px 2px 8px rgba(5, 28, 55, 0.42);
    text-align: left;
    font-style: normal;
    text-transform: none;
    .left_bg {
      min-width: 227px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
    }
    .text {
      position: absolute;
      left: 145px;
      top: 80px;
      /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/
      font-size: 48px;
    }
    .title-right {
      position: absolute;
      right: 15px;
      top: 80px;
    }
    .img {
      position: absolute;
      right: 0;
      top: 103px;
      height: 30px;
    }
  }
}
</style>
