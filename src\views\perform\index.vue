<template>
  <div class="motion-page">
    <div class="motion-main_left">
      <ScmpCard cardName="今日活动情况" backgroundImage="card_bg3">
        <div slot="main" class="left-box1">
          <div class="table-con table-header">
            <span class="td1">序号</span>
            <span class="td2">主题活动</span>
            <span class="td3">组织单位</span>
            <span class="td4">参与人数</span>
          </div>
          <div class="table-con table-item" v-for="(item, index) in leftTopList.slice(0, 4)" :key="index">
            <img src="../../assets/image/perform-left-icon1.png" class="icon" alt="" />
            <span class="td1 dot">{{ index + 1 }}</span>
            <span class="td2 clickable" @click="viewActivityDetail(item.id)">{{ item.activityName }}</span>
            <span class="td3">{{ item.orgName }}</span>
            <span class="td4">{{ item.num }}</span>
          </div>
        </div>
      </ScmpCard>
      <ScmpCard cardName="活动统计概览" backgroundImage="card_bg3">
        <div slot="main" class="left-box2">
          <div class="radio-box">
            <div class="radio-item" :class="{ active: activeMenu === 'month' }" @click="switchMenu('month')">每月活动排名
            </div>
            <div class="radio-item" :class="{ active: activeMenu === 'year' }" @click="switchMenu('year')">今年排名情况</div>
          </div>
          <div class="bar-box">
            <div class="right-bottom-item clickable-item" v-for="(item, index) in currentProgressBarList" :key="index"
              @click="viewDistrictDetail(item)">
              <div class="item-title">
                <div>
                  <span :class="{ icon: true, icon1: index > 2 }">{{
                    index + 1
                  }}</span>
                  <span class="title">{{ item.districtName }} </span>
                </div>
                <span class="value">{{ item.num }}</span>
              </div>
              <div class="right-progress-bar">
                <div class="bar" :style="`width:${(item.num / 1000) * 100}%`"></div>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
    </div>

    <div class="motion-main_center">
      <div class="center-top">
        <ScmpSpot :spotName="areaName + '代表履职情况工作视窗'" dropDownTitle="切换专题" :column="spotDownColumn"
          :dropDownList="dropDownList" @checkedItem="checkedItem">
        </ScmpSpot>
      </div>
      <div class="con-bottom">
        <div class="tongji-box">
          <div class="tongji-item">
            <div class="name">全年活动次数</div>
            <div class="num">{{ dutyInfoNum.yearActivityNum }} <span>次</span></div>
          </div>
          <div class="tongji-item">
            <div class="name">本月活动次数</div>
            <div class="num">{{ dutyInfoNum.monthActivityNum }} <span>次</span></div>
          </div>
          <div class="tongji-item item-bg2">
            <div class="name">全年代表参与人数</div>
            <div class="num">{{ dutyInfoNum.yearActivityPeopleNum }} <span>人</span></div>
          </div>
          <div class="tongji-item item-bg2">
            <div class="name">本月代表参与人数</div>
            <div class="num">{{ dutyInfoNum.monthActivityPeopleNum }} <span>人</span></div>
          </div>
        </div>
        <ScmpCard cardName="代表履职情况" backgroundImage="card_bg2" :rightPicture="false">
          <div slot="main" class="con-bottom-box">
            <!-- 搜索模块 -->
            <div class="search-box">
              <div class="box-title">{{ activeTab === 'score' ? '代表履职积分情况' : '履职电子档案' }}</div>
              <div class="search-form-container">
                <el-form :inline="true" :model="seachForm" ref="seachForm" class="demo-form-inline">
                  <!-- 代表履职积分情况的筛选项 -->
                  <template v-if="activeTab == 'score'">
                    <el-form-item label="代表姓名" prop="name">
                      <el-input style="width:330px" v-model="userName1" placeholder="请输入代表姓名进行查询"
                        @submit.native.prevent></el-input>
                    </el-form-item>
                  </template>

                  <!-- 履职电子档案的筛选项 -->
                  <template v-if="activeTab == 'archive'">
                    <el-form-item label="代表姓名" prop="userName2">
                      <el-input style="width:280px" v-model="userName2" placeholder="请输入代表姓名进行查询"
                        @submit.native.prevent></el-input>
                    </el-form-item>
                    <el-form-item label="代表团" prop="region">
                      <el-select v-model="region" style="width:250px" placeholder="请选择代表团" clearable>
                        <el-option v-for="item in delegationOptions" :key="item.value" :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </template>

                  <el-form-item>
                    <el-button type="primary" @click="onSubmit">查询</el-button>
                    <el-button type="success" @click="resetForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <div class="table-box">
              <div class="tabs-box">
                <div class="tab-item" :class="{ active: activeTab === 'score' }" @click="switchTab('score')">代表履职积分情况
                </div>
                <div class="tab-item" :class="{ active: activeTab === 'archive' }" @click="switchTab('archive')">履职电子档案
                </div>
              </div>
              <div class="table">
                <el-table :data="activeTab === 'score' ? scoreData : archiveData" style="width: 100%" border
                  class="no-resize-table" :header-cell-style="{ resize: 'none' }"
                  :default-sort="{ prop: 'date', order: 'descending' }">
                  <template v-if="activeTab === 'score'">
                    <el-table-column header-align="center" align="center" prop="dbName" width="200" label="代表姓名">
                    </el-table-column>
                    <el-table-column header-align="center" align="center" prop="foTotalScore" width="300" label="履职积分">
                    </el-table-column>
                    <el-table-column header-align="center" align="center" prop="jgmc" width="500" label="所属代表团">
                    </el-table-column>
                    <el-table-column header-align="center" align="center" prop="grank" width="300" label="所属代表团排名"
                      sortable>
                    </el-table-column>
                    <el-table-column header-align="center" align="center" prop="rank" width="300" label="总排名" sortable>
                    </el-table-column>
                    <el-table-column header-align="center" align="center" prop="createTime" label="更新时间">
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column header-align="center" align="center" prop="userName" label="代表姓名" width="300">
                    </el-table-column>
                    <el-table-column header-align="center" align="center" prop="activityTypeName" label="履职类型">
                    </el-table-column>
                    <el-table-column header-align="center" align="center" prop="activityName" label="履职内容">
                    </el-table-column>
                    <!-- <el-table-column header-align="center" align="center" prop="points" label="获得积分">
                    </el-table-column> -->
                    <el-table-column header-align="center" align="center" prop="startTime" label="履职时间">
                    </el-table-column>
                  </template>
                </el-table>
                <ScmpPagination :total="activeTab === 'score' ? appraiseListTotal : archivesListTotal"
                  :current-page.sync="currentPage" :page-size.sync="pageSize" @current-change="handleCurrentChange"
                  @size-change="handleSizeChange" />
              </div>
            </div>
          </div>
        </ScmpCard>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getDutyInfoNumForMonthAndYear,
  getMonthDutyForDistrict,
  getYearDutyForDistrict,
  getDailyDutyActivityInfo,
  getDutyAppraiseList,
  getDutyElectronicArchivesList,
  getDutyActiveById,
} from "@/api/dataPageApi/perform";
export default {
  name: "motion-index",
  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    ScmpSpot: () => import("@/comps/scmp-spot"),
    ScmpPagination: () => import("@/components/pagination/index.vue"),
  },
  data() {
    return {
      yearValue1: String(new Date().getFullYear()),
      monthValue1: String(new Date().getMonth()),
      // 当前激活的菜单
      activeMenu: 'month',
      // activeMenu: 'year',
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],
      leftTopList: [],
      seachForm: {}, // 添加搜索表单对象
      // 月度数据
      monthProgressBarList: [
        {
          districtName: "天河区",
          num: 777,
        },
        {
          districtName: "黄埔区",
          num: 597,
        },
        {
          districtName: "海珠区",
          num: 77,
        },
        {
          districtName: "荔湾区",
          num: 637,
        },
        {
          districtName: "越秀区",
          num: 680,
        },
        {
          districtName: "白云区",
          num: 457,
        },
        {
          districtName: "从化区",
          num: 457,
        },
      ],
      // 年度数据
      yearProgressBarList: [],
      input1: '',
      input2: '',
      scoreData: [],
      activeTab: 'score', // 当前激活的tab
      archiveData: [
        // {
        //   name: "孙娴茹",
        //   type: "集中视察",
        //   content: "参与环保设施视察",
        //   score: 15,
        //   date: "2024-01-06",
        // }
      ],
      currentPage: 1,
      pageSize: 10,
      dutyInfoNum: [],
      administrativeAreaId: '',
      appraiseListTotal: 0,
      archivesListTotal: 0,
      year: 2025,
      userName1: '',
      userName2: '',
      areaName: '',
      region: '',
      // 代表团选项列表
      delegationOptions: [
        { value: '', label: '全部' },
        { value: '1028', label: '越秀代表团' },
        { value: '1140', label: '海珠代表团' },
        { value: '435', label: '荔湾代表团' },
        { value: '329', label: '天河代表团' },
        { value: '1128', label: '白云代表团' },
        { value: '968', label: '黄埔代表团' },
        { value: '139', label: '花都代表团' },
        { value: '862', label: '番禺代表团' },
        { value: '306', label: '南沙代表团' },
        { value: '260', label: '从化代表团' },
        { value: '816', label: '增城代表团' },
      ],
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
    // 根据当前激活菜单返回对应数据
    currentProgressBarList() {
      return this.activeMenu === 'month' ? this.monthProgressBarList : this.yearProgressBarList;
    },
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.activeTab === 'score' ? this.scoreData.length.slice(start, end) : this.archiveData.length.slice(start, end)
    }
  },
  created() {
    this.areaName = this.$route.query.areaName ? this.$route.query.areaName.slice(0, 3) : '';
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.year = this.$route.query.year;
    this.getDutyInfoNumForMonthAndYear(this.year)
    this.getMonthDutyForDistrict(this.mouthValue1, this.year)
    this.getYearDutyForDistrict(this.year)
    this.getDailyDutyActivityInfo(this.year)
    this.getDutyAppraiseList(this.year)
  },
  mounted() {
    this.getDutyInfoNumForMonthAndYear(this.year)
    this.getMonthDutyForDistrict(this.mouthValue1, this.year)
    this.getYearDutyForDistrict(this.year)
    this.getDailyDutyActivityInfo(this.year)
    this.getDutyAppraiseList(this.year)
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && e.target.tagName === 'INPUT') {
        e.preventDefault(); // 阻止默认行为
        // this.onSubmit();   // 手动触发查询
      }
    });
  },
  watch: {},
  methods: {
    // 重置查询
    resetForm() {
      this.$refs.seachForm.resetFields(); // 重置表单
      this.currentPage = 1; // 重置分页为第一页
      // 根据当前标签清空对应数据并加载
      if (this.activeTab === 'score') {
        this.userName1 = ''; // 重置查询条件
        this.scoreData = [];
        this.getDutyAppraiseList();
      } else {
        this.userName2 = ''; // 重置查询条件
        this.region = '';
        this.archiveData = [];
        this.getDutyElectronicArchivesList();
      }
    },
    // 页码改变事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.archiveData = []
      this.scoreData = []
      if (this.activeTab == 'score') {
        this.getDutyAppraiseList(this.administrativeAreaId)
      } else {
        this.getDutyElectronicArchivesList(this.administrativeAreaId)
      }
    },

    // 每页条数改变事件
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1 // 条数改变时重置到第一页
      this.archiveData = []
      this.scoreData = []
      if (this.activeTab == 'score') {
        this.getDutyAppraiseList(this.administrativeAreaId)
      } else {
        this.getDutyElectronicArchivesList(this.administrativeAreaId)
      }
    },

    getDutyInfoNumForMonthAndYear(year) {
      const params = {
        administrativeAreaId: this.administrativeAreaId,
        year: this.year
      }
      getDutyInfoNumForMonthAndYear(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.dutyInfoNum = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getMonthDutyForDistrict(month, year) {
      const params = {
        month: month,
        year: this.year,
        administrativeAreaId: this.administrativeAreaId

      }
      getMonthDutyForDistrict(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.monthProgressBarList = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },


    getYearDutyForDistrict(year) {
      const params = {
        year: this.year,
        administrativeAreaId: this.administrativeAreaId

      }
      getYearDutyForDistrict(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.yearProgressBarList = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getDailyDutyActivityInfo(year) {
      const params = {
        administrativeAreaId: this.administrativeAreaId,
        year: this.year
      }
      getDailyDutyActivityInfo(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.leftTopList = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getDutyAppraiseList(year) {
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      }
      const data1 = {
        administrativeAreaId: this.administrativeAreaId,
        year: this.year,
        userName: this.userName1
      }
      getDutyAppraiseList(params, data1)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // debugger
          this.scoreData = res.rows
          this.appraiseListTotal = res.total
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getDutyElectronicArchivesList(year) {
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      }
      const data1 = {
        administrativeAreaId: this.administrativeAreaId,
        year: this.year,
        userName: this.userName2,
        region: this.region
      }
      getDutyElectronicArchivesList(params, data1)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.archiveData = res.rows
          this.archivesListTotal = res.total
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    // 切换菜单
    switchMenu(menu) {
      this.activeMenu = menu;
    },
    checkedItem(obj) {
      console.log("选中", obj);
    },
    onSubmit() {
      this.currentPage = 1;
      this.scoreData = [];
      this.archiveData = [];
      if (this.activeTab === 'score') {
        this.getDutyAppraiseList();
      } else {
        this.getDutyElectronicArchivesList();
      }
    },
    // 切换tab
    switchTab(tab) {
      console.log('tab')
      console.log(tab)
      // if (this.activeTab === tab) {
      //   return;
      // }
      // 切换tab时重置分页
      if (this.activeTab === tab) {
        return;
      }
      this.activeTab = tab;
      this.currentPage = 1;
      if (this.activeTab === 'archive') {
        this.getDutyElectronicArchivesList()
      } else {
        this.getDutyAppraiseList()
      }
      return
    },

    // 查看活动详情
    viewActivityDetail(id) {
      if (!id) {
        this.$message.warning('活动ID不存在');
        return;
      }

      // 跳转到活动详情页面
      this.$router.push({
        path: '/dutyActiveDetail',
        query: {
          id: id,
          year: this.year,
          administrativeAreaId: this.administrativeAreaId
        }
      });
    },

    // 查看地区年度排名详情
    viewDistrictDetail(districtItem) {
      let type = '';
      if (this.activeMenu === 'month') {
        type = 'month';
      } else {
        type = 'year';
      }
      this.$router.push({
        path: '/yearRankingList',
        query: {
          administrativeAreaName: districtItem.districtName,
          // districtId: districtItem.districtId || districtItem.id,
          year: this.year,
          administrativeAreaId: this.administrativeAreaId,
          type: type,
          // activityCount: districtItem.num,
          // rankingType: this.activeMenu // 传递当前是月度还是年度排名
        }
      });
    },

    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false;
      this.activityDetail = {};
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
::v-deep .scmp-spot {
  // width: 1290px !important;

  .title {
    left: 45% !important;
    font-size: inherit !important;
    white-space: normal !important;
    word-break: break-word !important;
  }
}

::v-deep .no-resize-table .el-table__header-wrapper .el-table__header th {
  resize: none !important;
  user-select: none !important;
  // pointer-events: none;
}

@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/fonts/YouSheBiaoTiHei.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'DIN';
  src: url('@/assets/fonts/DIN-Regular.otf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'DIN';
  src: url('@/assets/fonts/DIN-Bold.otf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'DIN-MediumItalic';
  src: url('@/assets/fonts/DIN-MediumItalic.otf') format('truetype');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'DIN-BoldItalic';
  src: url('@/assets/fonts/DIN-BoldItalic.otf') format('truetype');
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

/*@font-face {
  font-family: 'Alibaba PuHuiTi';
  src: url('@/assets/fonts/Alibaba-PuHuiTi-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi';
  src: url('@/assets/fonts/Alibaba-PuHuiTi-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}*/

// PingFang SC 是系统字体，不需要额外引入

.motion-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .motion-main_left {
    width: 1222px;
    margin-top: 54px;

    .left-box1 {
      height: 756px;
      padding: 40px 60px;
      box-sizing: border-box;
      margin-bottom: 25px;

      .table-con {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          display: inline-block;
          width: 25%;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #ffffff;
          line-height: 22px;
          text-align: center;
        }

        .td1 {
          width: 10%;
          position: relative;
        }

        .dot {
          &::after {
            content: "";
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            width: 60px;
            height: 40%;
            background: linear-gradient(90deg,
                transparent,
                #ceb830,
                transparent);
            filter: blur(4px);
            opacity: 0.8;
          }
        }

        .td2 {
          width: 40%;
          overflow: hidden;
          line-height: 48px;
          white-space: nowrap;
          text-overflow: ellipsis;

          &.clickable {
            cursor: pointer !important;
            color: #39cdff !important;
            transition: all 0.3s ease;
            font-weight: 500;
            text-shadow: 0px 0px 8px rgba(57, 205, 255, 0.6);

            &:hover {
              color: #33fefe !important;
              text-shadow: 0px 0px 12px rgba(51, 254, 254, 0.8);
              transform: translateY(-1px);
            }
          }
        }

        .td3 {
          width: 35%;
          overflow: hidden;
          line-height: 48px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .td4 {
          width: 15%;
        }
      }

      .table-item {
        padding: 40px 0;
        margin-top: 20px;
        background: rgba(8, 49, 81, 0.31);
        box-shadow: inset 0px 0px 9px 0px rgba(97, 135, 228, 0.28);
        border-radius: 2px 2px 2px 2px;
        border: 1px solid rgba(113, 161, 255, 0.11);
        position: relative;

        .icon {
          position: absolute;
          top: 10px;
          left: 10px;
          font-size: 30px;
          width: 20px;
          height: 20px;
        }
      }
    }

    .left-box2 {
      height: 960px;
      padding: 40px 20px;
      box-sizing: border-box;
      margin-bottom: 40px;

      .radio-box {
        display: flex;
        align-items: center;
        padding: 0 40px;
        margin-left: 15px;

        .radio-item {
          width: 227px;
          height: 55px;
          line-height: 55px;
          background: url("@/assets/image/perform-left-img2.png") no-repeat;
          background-size: 100% 100%;
          margin-right: 20px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #ffffff;
          text-align: left;
          font-style: italic;

          cursor: pointer;
          user-select: none;
          position: relative;
          z-index: 1;
          padding-top: 0;
          padding-left: 40px;
          padding-bottom: 10px;

          &:hover {
            opacity: 0.9;
          }

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
          }
        }

        .active {
          background: url("@/assets/image/perform-left-img1.png") no-repeat;
          background-size: 100% 100%;
        }

        .year-ranking {
          position: relative;

          &::after {
            content: "点击查看详情";
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 20px;
            color: #39cdff;
            opacity: 0.8;
            white-space: nowrap;
          }

          &:hover::after {
            opacity: 1;
            color: #33fefe;
          }
        }
      }

      .bar-box {
        height: 90%;
        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 10px !important;
          background-color: rgba(0, 25, 63, 0.3);
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 147, 255, 0.8);
          border-radius: 5px;
          border: 2px solid rgba(0, 25, 63, 0.3);
        }

        &::-webkit-scrollbar-track {
          background: rgba(0, 25, 63, 0.3);
          border-radius: 5px;
        }

        &::-webkit-scrollbar-corner {
          background: rgba(0, 25, 63, 0.3);
        }
      }

      .right-bottom-item {
        padding: 0 60px;

        .item-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 15px;

          .icon {
            display: inline-block;
            margin-right: 30px;
            width: 40px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            background: url("@/assets/image/perform-left-b-icon1.png") no-repeat;
            background-size: 100% 100%;
            font-family: DIN, DIN;
            font-weight: 500;
            font-size: 30px;
            color: #ffffff;
          }

          .icon1 {
            background: url("@/assets/image/perform-left-b-icon2.png") no-repeat;
            background-size: 100% 100%;
          }

          .title {
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: 400;
            font-size: 36px;
            color: #ffffff;

            .action {
              color: #25e2f4;
              font-size: 20px;
              cursor: pointer;
              margin-left: 10px;
            }
          }

          .value {
            margin-top: 30px;
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: 900;
            font-size: 36px;
            color: #ffcf00;
            // text-shadow: -1px 0px 18px #ffcf00;
          }
        }

        .right-progress-bar {
          width: 100%;
          height: 30px;
          background: linear-gradient(-90deg,
              rgba(0, 121, 255, 0.22) 0%,
              rgba(2, 77, 169, 0.14) 55%,
              rgba(4, 4, 26, 0) 100%);
          border-radius: 0px 0px 0px 0px;
          position: relative;
          border-radius: 0 8px 8px 0;

          .bar {
            max-width: 100%;
            height: 100%;
            width: 0;
            background: linear-gradient(180deg, #5a3fff 0%, #1ed6ff 100%);
            border-radius: 0 8px 8px 0;
            position: relative;

            &::after {
              content: "";
              position: absolute;
              right: 0;
              top: 0;
              width: 10px;
              height: 30px;
              background: #fff;
              border-radius: 50%;
            }
          }

          &::after {
            content: "";
            position: absolute;
            right: 0;
            top: 0;
            width: 15px;
            height: 30px;
            background: #155497;
            border-radius: 50%;
          }
        }
      }
    }
  }

  .motion-main_center {
    width: calc(100% - 1222px);
    position: relative;
    padding-left: 80px;
    box-sizing: border-box;

    .center-top {
      display: flex;
      flex-direction: row;
      justify-content: center;
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      line-height: 25px;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      position: absolute;
      left: 0;

      .customTab {
        margin-top: 20px;
        padding: 40px 103px 60px 111px;
        position: absolute;
      }

      .customTab1 {
        background: url("@/assets/image/customTab1.png") no-repeat;
        background-size: contain;
        left: 45px;
      }

      .customTab2 {
        background: url("@/assets/image/customTab2.png") no-repeat;
        background-size: contain;
        left: 45px;
      }

      .customTabact1 {
        background: url("@/assets/image/customTabact1.png") no-repeat;
        background-size: contain;
        right: 45px;
      }

      .customTabact2 {
        background: url("@/assets/image/customTabact2.png") no-repeat;
        background-size: contain;
        right: 45px;
      }
    }

    .con-bottom {
      position: absolute;
      top: 160px;
      width: 100%;
      padding-right: 80px;
      box-sizing: border-box;

      .tongji-box {
        display: flex;
        justify-content: space-between;

        .tongji-item {
          height: 280px;
          width: 25%;
          background: url("@/assets/image/perform-tongji1.png") no-repeat;
          background-size: 100% 100%;
          position: relative;

          .name {
            position: absolute;
            left: 15%;
            top: 20%;
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: bold;
            font-size: 36px;
            color: #ffffff;
            line-height: 48px;
            text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
            -webkit-text-stroke: 0px rgba(255, 255, 255, 0.11);
            text-align: left;
            font-style: normal;
            text-transform: none;
            -webkit-text-stroke: 0px #ffffff;
            -webkit-background-clip: text;
            /* WebKit 浏览器专用属性，使背景剪切至文本 */
            background-clip: text;
            /* 标准属性 */
          }

          .num {
            position: absolute;
            left: 15%;
            top: 40%;
            font-family: DIN-MediumItalic, DIN-MediumItalic;
            font-weight: 400;
            font-size: 50px;
            color: #29f1fa;
            line-height: 59px;
            text-shadow: 0px 0px 7px rgba(255, 179, 179, 0.71);
            -webkit-text-stroke: 0px #ffffff;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #ca320b 100%),
              linear-gradient(90deg, #f6ffff 0%, #29f1fa 100%),
              linear-gradient(270.00000009864743deg,
                rgba(255, 144, 62, 0) 0%,
                #fbe947 56%);
            -webkit-text-stroke: 0px rgba(255, 255, 255, 0.11);
            -webkit-text-stroke: 0px #ffffff;
            -webkit-background-clip: text;
            /* WebKit 浏览器专用属性，使背景剪切至文本 */
            background-clip: text;

            /* 标准属性 */
            span {
              font-family: DIN, DIN;
              font-weight: bold;
              font-size: 30px;
              color: #ffffff;
              line-height: 35px;
            }
          }
        }

        .item-bg2 {
          background: url("@/assets/image/perform-tongji2.png") no-repeat;
          background-size: 100% 100%;
        }
      }

      .con-bottom-box {
        height: 1460px;
        padding: 40px 80px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        .search-box {
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
          justify-content: space-between;

          .box-title {
            width: 600px;
            height: 120px;
            line-height: 100px;
            text-align: center;
            background: url("@/assets/image/perform-con-bottom1.png") no-repeat;
            background-size: 100% 100%;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 36px;
            color: #ffffff;
            letter-spacing: 2px;
            text-shadow: 0px 2px 3px rgba(51, 158, 188, 0.79);
            padding-top: 20px;
            flex-shrink: 0;
          }

          .search-form-container {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            padding-top: 50px;
          }

          .demo-form-inline {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            flex-wrap: wrap;
            gap: 10px;
            min-height: 60px;

            // 履职电子档案标签的特殊布局调整
            &:has(el-form-item[prop="region"]) {
              flex-wrap: wrap;

              .el-form-item {
                margin-bottom: 15px;
              }
            }
          }

          .search-item {
            font-size: 24px;
            margin-right: 40px;
            display: flex;
            align-items: center;

            span {
              display: inline-block;
              width: 150px;
            }

            .name {
              width: 190px;
            }

            ::v-deep .el-input__inner {
              background: transparent;
              color: #fff;
              font-size: 20px;
            }
          }

          ::v-deep .el-form-item__label {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 36px;
            color: #ffffff;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-right: 10px;
          }

          ::v-deep .el-form--inline .el-form-item {
            margin-right: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
          }

          ::v-deep .el-form--inline .el-form-item__content {
            vertical-align: initial;
          }

          ::v-deep .el-input__inner {
            background: rgba(57, 205, 255, 0.12);
            background-image: none;
            border-radius: 4px;
            border: 1px solid #7edcfb;
            box-sizing: border-box;
            display: inline-block;
            height: 52px;
            line-height: 52px;
            outline: 0;
            padding: 0 15px;
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 36px;
            color: #ffffff;
            font-style: normal;
            text-transform: none;
          }

          // .demo-form-inline {
          //   width: calc(100% + 400px);
          //   margin-left: -420px;
          //   margin-top: 85px;
          // }

          .el-button--primary {
            background: rgba(10, 255, 255, 0.21);
            border-radius: 2px 2px 2px 2px;
            border: 1px solid #33fefe;
            margin-left: 20px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 36px;
            color: #33fefe;
            font-style: normal;
            text-transform: none;
          }

          .el-button--success {
            background: rgba(14, 200, 88, 0.22);
            border-radius: 2px 2px 2px 2px;
            border: 1px solid #0ec858;
            margin-left: 15px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 36px;
            color: #0ec858;
            font-style: normal;
            text-transform: none;
          }
        }

        .table-box {
          flex: 1;
          display: flex;
          height: calc(100% - 120px);

          .tabs-box {
            .tab-item {
              width: 95px;
              box-sizing: border-box;
              padding: 54px 20px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
              font-size: 36px;
              color: #ffffff;
              line-height: 36px;
              letter-spacing: 1px;
              text-shadow: 2px 2px 3px rgba(11, 135, 215, 0.24);
              background: url("@/assets/image/perform-tab-bg2.png") no-repeat;
              background-size: 100% 100%;
              margin-bottom: 10px;
              cursor: pointer;
            }

            .active {
              background: url("@/assets/image/perform-tab-bg1.png") no-repeat;
              background-size: 100% 100%;
            }
          }

          .table {
            flex: 1;
            height: 98%;
            overflow: hidden;
            display: flex;
            flex-direction: column;


            ::v-deep .el-table {
              flex: 1;
              background-color: transparent;
              // height: 800px !important; // 根据实际显示高度调整
              height: calc(100% - 100px);
              font-size: 40px !important;
              overflow-y: auto !important;

              &::-webkit-scrollbar {
                width: 10px !important;
                background: rgba(8, 49, 81, 0.3) !important;
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(113, 161, 255, 0.6) !important;
                border-radius: 5px !important;
              }

              &::-webkit-scrollbar-track {
                background: rgba(0, 25, 63, 0.3) !important;
                border-radius: 5px !important;
              }

              &::-webkit-scrollbar-corner {
                background: rgba(0, 25, 63, 0.3) !important;
              }

              .el-table__body-wrapper .el-table__body {
                height: 100px !important;
              }

              .cell {
                box-sizing: border-box;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                word-break: break-all;
                line-height: 50px;
                padding-left: 10px;
                padding-right: 10px;
              }

              &::before {
                display: none;
              }

              thead th {
                font-size: 36px !important;
                // padding: 30px 0 !important;
                background: #0251b2;
                color: #fff;
                height: 83px;
                line-height: 103px;
              }

              tbody td {
                font-size: 36px !important;
                padding: 25px 0 !important;
                line-height: 1.8 !important;
                background: #333333;
                color: #fff;
                height: 106px;

                &:nth-of-type(1) {
                  background: #072C59;
                }
              }

              .el-pagination {
                font-size: 34px !important;
                padding: 30px 0;

                button,
                .number,
                .el-icon {
                  font-size: 34px !important;
                }
              }
            }

            .scmp-pagination {
              margin: 20px 0;
            }
          }
        }
      }
    }
  }
}

.motion-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
  /* 设置透明度 */
}

/* 下拉选择框样式 */
::v-deep .el-select {
  .el-input__inner {
    background: rgba(57, 205, 255, 0.12);
    background-image: none;
    border-radius: 4px;
    border: 1px solid #7edcfb;
    box-sizing: border-box;
    display: inline-block;
    height: 52px;
    line-height: 52px;
    outline: 0;
    padding: 0 15px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 36px;
    color: #ffffff;
    font-style: normal;
    text-transform: none;
  }

  .el-input__suffix {
    .el-select__caret {
      color: #7edcfb;
      font-size: 16px;
    }
  }

  &.is-focus .el-input__inner {
    border-color: #409eff;
  }
}

/* 下拉选项弹窗样式 */
::v-deep .el-select-dropdown {
  background-color: rgba(8, 49, 81, 0.95);
  border: 1px solid #7edcfb;
  border-radius: 4px;

  .el-select-dropdown__item {
    color: #ffffff;
    font-family: PingFang SC, PingFang SC;
    font-size: 32px;
    padding: 12px 20px;

    &:hover {
      background-color: rgba(57, 205, 255, 0.2);
    }

    &.selected {
      background-color: rgba(57, 205, 255, 0.3);
      color: #ffffff;
      font-weight: bold;
    }
  }
}

// 活动详情弹窗样式
::v-deep .activity-detail-dialog {
  background: linear-gradient(135deg, #0a1929 0%, #1a2832 100%);
  border: 1px solid rgba(113, 161, 255, 0.3);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  margin-top: 15vh !important;
  z-index: 2001 !important;
}

::v-deep .activity-detail-dialog .el-dialog__header {
  background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
  border-bottom: 1px solid rgba(113, 161, 255, 0.3);
  padding: 20px 30px;
  border-radius: 12px 12px 0 0;
}

::v-deep .activity-detail-dialog .el-dialog__title {
  font-family: 'YouSheBiaoTiHei', sans-serif;
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

::v-deep .activity-detail-dialog .el-dialog__close {
  color: #ffffff;
  font-size: 22px;
}

::v-deep .activity-detail-dialog .el-dialog__close:hover {
  color: #39cdff;
  transform: scale(1.1);
}

::v-deep .activity-detail-dialog .el-dialog__body {
  padding: 30px;
  background: rgba(8, 49, 81, 0.1);
  max-height: 70vh;
  overflow-y: auto;
}

::v-deep .activity-detail-dialog .el-dialog__body::-webkit-scrollbar {
  width: 8px;
}

::v-deep .activity-detail-dialog .el-dialog__body::-webkit-scrollbar-thumb {
  background: rgba(113, 161, 255, 0.6);
  border-radius: 4px;
}

::v-deep .activity-detail-dialog .el-dialog__body::-webkit-scrollbar-track {
  background: rgba(0, 25, 63, 0.3);
  border-radius: 4px;
}

::v-deep .activity-detail-dialog .el-dialog__footer {
  padding: 20px 30px;
  border-top: 1px solid rgba(113, 161, 255, 0.3);
  background: rgba(8, 49, 81, 0.2);
  border-radius: 0 0 12px 12px;
  text-align: center;
}

::v-deep .activity-detail-dialog .el-dialog__footer .el-button--primary {
  background: rgba(10, 255, 255, 0.21);
  border-color: #33fefe;
  color: #33fefe;
  font-size: 28px;
  padding: 12px 30px;
  transition: all 0.3s ease;
}

::v-deep .activity-detail-dialog .el-dialog__footer .el-button--primary:hover {
  background: rgba(10, 255, 255, 0.35);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(57, 205, 255, 0.4);
}

.dialog-content {
  min-height: 400px;
  color: #ffffff;

  .activity-container {
    .activity-header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid rgba(126, 220, 251, 0.3);

      h2 {
        font-family: 'YouSheBiaoTiHei', sans-serif;
        font-size: 32px;
        font-weight: 600;
        color: #39cdff;
        margin: 0;
        text-shadow: 0 2px 4px rgba(57, 205, 255, 0.3);
      }
    }

    .basic-info,
    .invite-section {
      margin-bottom: 30px;

      h3 {
        font-family: 'YouSheBiaoTiHei', sans-serif;
        font-size: 24px;
        color: #39cdff;
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(57, 205, 255, 0.3);
      }

      ::v-deep .el-descriptions {
        .el-descriptions-item__label {
          background: rgba(90, 211, 251, 0.2) !important;
          color: #7edcfb !important;
          font-weight: bold !important;
          text-align: center !important;
          font-size: 16px !important;
          padding: 12px 15px !important;
        }

        .el-descriptions-item__content {
          background: rgba(16, 42, 66, 0.4) !important;
          color: #fff !important;
          padding: 12px 15px !important;
          font-size: 16px !important;
        }

        .el-descriptions__body {
          background: rgba(16, 42, 66, 0.6);
        }

        .el-descriptions__table {
          border: 1px solid rgba(126, 220, 251, 0.5) !important;
        }

        td,
        th {
          border-color: rgba(126, 220, 251, 0.3) !important;
        }
      }
    }

    .staff-section,
    .content-section {
      margin-bottom: 30px;

      h3 {
        font-family: 'YouSheBiaoTiHei', sans-serif;
        font-size: 24px;
        color: #39cdff;
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(57, 205, 255, 0.3);
      }

      .staff-content,
      .content-detail {
        background: rgba(16, 42, 66, 0.4);
        border: 1px solid rgba(126, 220, 251, 0.3);
        border-radius: 6px;
        padding: 20px;
        font-size: 16px;
        line-height: 1.8;
        color: #ffffff;
        min-height: 60px;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }
}
</style>
