import axios from "axios";
import {
  invalidCode,
  messageDuration,
  noPermissionCode,
  requestTimeout,
  successCode,
  tokenName,
  contentType,
} from "@/config/settings";
import { Loading, Message } from "element-ui";
import store from "../stores";
import qs from "qs";
import router from "../routers";
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: requestTimeout,
  headers: {
    "Content-Type": contentType,
    flag: false,
    fromCode: "front",
  },
});
let loadingInstance;
export function checkToken() {
  // 首先尝试从 localStorage 获取 pro__token
  const proToken = localStorage.getItem('pro__token');
  // console.log(localStorage.getItem('pro__token'))
  console.log(proToken)
  if (proToken) {
    const rdtoken = JSON.parse(proToken);
    return rdtoken.value; // 返回 token
  } else {
    // 如果没有，从 Vuex store 中获取 accessToken
    return store.getters.accessToken;  // 假设你使用 Vuex 管理状态
  }
}
service.interceptors.request.use(
  (config) => {
    // 设置token
    // const token = checkToken()
    const token ='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWFsTmFtZSI6IuS7oyrlkZgiLCJmb3JjZVVwZGF0ZVB3IjoiMCIsInJvbGVJZHMiOiIiLCJ1c2VyX25hbWUiOiJsaGMiLCJzY29wZSI6W10sImRlcElkIjoiOTAwIiwibmV0V29yayI6bnVsbCwidXNlcklkIjoiNDM3NSIsImRlcE5hbWUiOiI5MDAiLCJqdGkiOiI4OWI4OGQzMy1lNzNkLTQ1MzQtYTg5Zi05MDE2MzZjODZkMWEiLCJjbGllbnRfaWQiOiJwYWFzIn0.tDxrp6K1dxNSLCypZkqhN_mnyfir8VtA4vfF8OCnWrE';
    if (token) {
      config.headers["menuId"] = store.getters.navigation.menuData.menuId;
      config.headers["systemId"] = store.getters.navigation.menuData.systemId;
      config.headers["hidden"] = store.getters.navigation.menuData.hidden;
      config.headers[tokenName] = token;
      // 完全不接平台的话改完false
      config.headers["flag"] = true;
    }
    if (process.env.NODE_ENV !== "test") {
      if (contentType === "application/x-www-form-urlencoded;charset=UTF-8") {
        if (config.data && !config.data.param) {
          config.data = qs.stringify(config.data);
        }
      }
    }

    if (
      config.url.includes("add") ||
      config.url.includes("edit") ||
      config.url.includes("set") ||
      config.url.includes("update") ||
      config.url.includes("import") ||
      config.url.includes("export") ||
      config.url.includes("save")
    ) {
      loadingInstance = Loading.service();
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// const errorMsg = (message) => {
//   return Message({
//     message: message,
//     type: "error",
//     duration: messageDuration,
//   });
// };

service.interceptors.response.use(
  (response) => {
    if (loadingInstance) {
      loadingInstance.close();
    }
    const { status, data } = response;
    const { code, message } = data;

    if (
      code !== successCode &&
      code !== 0 &&
      code !== 400 &&
      status !== successCode
    ) {
      if (code == 401 || code == 402) {
        // token 过期
        window.top.postMessage(
          {
            event: "logout", // 约定的消息事件
            args: {}, // 参数
          },
          "*"
        );
      }
      switch (code) {
        case invalidCode:
          // errorMsg(message || `后端接口${code}异常`);
          store.dispatch("user/resetAccessToken");
          break;
        case noPermissionCode:
          // errorMsg("请重新登录");
          // 登录超时
          if (process.env.VUE_APP_DEFAULT_AUTHENTICATION_METHOD === "cas") {
            // 单点登录
            window.location.href = process.env.VUE_APP_API_CAS_URL;
          } else {
            // 返回登录页
            console.log('token过期测试',localStorage.getItem('userId'))
            console.log(localStorage.getItem('pro__token'))
            if(localStorage.getItem('pro__token')) {
              store.dispatch("user/logout").then(() => {
                // router.push("/login");
                // 不跳转
                console.log('有pro__token')
              });
            } else {
              store.dispatch("user/logout").then(() => {
                // 非平台内部 跳转登录页面
                router.push("/login");
                console.log('无pro__token')
              });
            }
          }
          // router.push({
          //   path: "/401",
          // });
          break;
        default:
          // errorMsg(message || `后端接口${code}异常`);
          break;
      }
      return Promise.reject({ code, message } || "Error");
    } else {
      return data;
    }
  },
  (error) => {
    if (loadingInstance) {
      loadingInstance.close();
    }
    /*网络连接过程异常处理*/
    let { message } = error;
    switch (message) {
      case "Network Error":
        message = "后端接口连接异常";
        break;
      case "timeout":
        message = "后端接口请求超时";
        break;
      case "Request failed with status code":
        message = "后端接口" + message.substr(message.length - 3) + "异常";
        break;
    }
    // errorMsg(message || "后端接口未知异常");
    return Promise.reject(error);
  }
);
export default service;
