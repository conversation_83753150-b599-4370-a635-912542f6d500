<template>
  <div class="liaisonStation-page">
    <div class="liaisonStation-main_center">
      <div class="center-calendar">
        <ScmpCard :cardName="listTitle" backgroundImage="card_bg2" :rightPicture="false">
          <div slot="main" class="con-bottom-box">
            <div class="header">
              <div class="btn" @click="$router.go(-1)">返回</div>
            </div>
            <ScmpTable :tableData="dataList" :tableColumn="dataColum" :rowNum="11" :areaName="areaName"
              :indicesGrouping="true" :isMeeting="isMeeting">
            </ScmpTable>
            <ScmpPagination :total="listTotal" :current-page.sync="currentPage" :page-size.sync="pageSize"
              @current-change="handleCurrentChange" @size-change="handleSizeChange" />
          </div>
        </ScmpCard>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  findPage,
  findQzByPage
} from "@/api/dataPageApi/motion";
export default {
  components: {
    ScmpCard: () => import("@/comps/scmp-YAJYCard"),
    ScmpTable: () => import("@/comps/scmp-YAJYTable"),
    ScmpPagination: () => import("@/components/pagination/index.vue"),
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      dataList: [],
      dataColum: [],
      reParams: '',
      listTitle: '',
      listTotal: 0,
      orgCode: '',
      areaName: '',
      proposalType: '',
      isMeeting: undefined,
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
    // 计算当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.dataList.slice(start, end)
    }
  },
  created() {
    if (this.$route.query.title) {
      this.listTitle =  (this.$route.query.tabActive === '1' ? '大会建议' : '闭会建议')+'-'+this.$route.query.title;
    }
    if (this.$route.query.item) {
      this.dataList = JSON.parse(this.$route.query.item);
    }
    if (this.$route.query.params) {
      this.reParams = JSON.parse(this.$route.query.params);
    }
    if (this.$route.query.colum) {
      this.dataColum = JSON.parse(this.$route.query.colum);
    }
    if (this.$route.query.total) {
      this.listTotal = Number(this.$route.query.total)
    }
    if (this.$route.query.orgCode) {
      this.orgCode = Number(this.$route.query.orgCode)
    }
    if (this.$route.query.areaName) {
      this.areaName = Number(this.$route.query.areaName)
    }
    if (this.$route.query.proposalType) {
      this.proposalType = (this.$route.query.proposalType)
    }
    if (this.$route.query.isMeeting) {
      this.isMeeting = this.$route.query.isMeeting
    }
    console.log(this.$route.query.total)
  },
  mounted() {
  },
  methods: {
    handleCurrentChange(val) {
      this.currentPage = val
      this.dataList = []
      console.log('当前页:', val);
      if (this.areaName) {
        this.getQzFindPageList(this.currentPage, this.pageSize)

      } else {
        this.getFindPageList(this.currentPage, this.pageSize)

      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.dataList = []
      this.currentPage = 1 // 条数改变时重置到第一页
      console.log('每页条数:', val);
      if (this.areaName) {
        this.getQzFindPageList(this.currentPage, this.pageSize)

      } else {
        this.getFindPageList(this.currentPage, this.pageSize)
      }
    },
    getFindPageList(currentPage, pageSize) {
      console.log(this.reParams)
      this.reParams.page = currentPage
      this.reParams.rows = pageSize
      findPage(this.reParams)
        .then(res => {
          const meetingMap = {
            "144": "十六届五次",
            "143": "十六届四次",
            "142": "十六届三次",
            "141": "十六届一次",
            "140": "十五届六次",
            "137": "十五届五次",
            "136": "十五届四次",
            "135": "十五届三次",
            "132": "十四届一次",
            "133": "十五届一次",
            "93": "十二届四次",
            "68": "八届一次",
            "67": "七届一次",
            "66": "六届一次",
            "65": "五届一次",
            "73": "九届一次",
            "78": "十届一次",
            "92": "十二届三次",
            "91": "十二届二次",
            "90": "十二届一次",
            "87": "十一届五次",
            "86": "十一届四次",
            "85": "十一届三次",
            "84": "十一届二次",
            "83": "十一届一次",
            "82": "十届五次",
            "81": "十届四次",
            "80": "十届三次",
            "79": "十届二次",
            "77": "九届五次",
            "76": "九届四次",
            "75": "九届三次",
            "74": "九届二次",
            "72": "八届五次",
            "71": "八届四次",
            "70": "八届三次",
            "69": "八届二次",
            "64": "四届一次",
            "63": "三届一次",
            "62": "二届一次",
            "61": "一届一次"
          }
          const proposalTypeMap = {
            "1": "大会议案",
            "2": "大会建议",
            "3": "闭会建议",
            "4": "供参考建议",
          }

          const statusMap = {
            10: { title: "提交", step: 0 },
            20: { title: "校核", step: 1 },
            21: { title: "预分类", step: 2 },
            22: { title: "初审", step: 3 },
            25: { title: "复审", step: 3 },
            30: { title: "复审", step: 4 },
            40: { title: "分办", step: 5 },
            41: { title: "不予立案审核", step: 5 }, // 41归为分办
            42: { title: "分办审核中", step: 5 },
            43: { title: "分办复核中", step: 5 }, // 41归为分办
            45: { title: "不予立案确认", step: 5 }, // 45归为分办
            50: { title: "签收", step: 6 },
            60: { title: "答复", step: 7 },
            70: { title: "评价", step: 8 },
            90: { title: "已办结", step: 9 }
          };

          this.listTotal = res.data.total;
          // this.findPageListTotal = res.data.total
          this.dataList = res.data.records.map(item => ({
            ...item,
            meeting: meetingMap[item.meeting] || item.meeting,
            // meeting: this.lastMeetingId,
            status: statusMap[item.status]?.title || item.status,
            proposalType: proposalTypeMap[item.proposalType] || item.proposalType
          }));
          console.log(this.dataList)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getQZFindPageList(currentPage, pageSize) {
      console.log(this.reParams)
      this.reParams.page = currentPage
      this.reParams.rows = pageSize
      findQzByPage(this.reParams)
        .then(res => {
          const meetingMap = {
            "144": "十六届五次",
            "143": "十六届四次",
            "142": "十六届三次",
            "141": "十六届一次",
            "140": "十五届六次",
            "137": "十五届五次",
            "136": "十五届四次",
            "135": "十五届三次",
            "132": "十四届一次",
            "133": "十五届一次",
            "93": "十二届四次",
            "68": "八届一次",
            "67": "七届一次",
            "66": "六届一次",
            "65": "五届一次",
            "73": "九届一次",
            "78": "十届一次",
            "92": "十二届三次",
            "91": "十二届二次",
            "90": "十二届一次",
            "87": "十一届五次",
            "86": "十一届四次",
            "85": "十一届三次",
            "84": "十一届二次",
            "83": "十一届一次",
            "82": "十届五次",
            "81": "十届四次",
            "80": "十届三次",
            "79": "十届二次",
            "77": "九届五次",
            "76": "九届四次",
            "75": "九届三次",
            "74": "九届二次",
            "72": "八届五次",
            "71": "八届四次",
            "70": "八届三次",
            "69": "八届二次",
            "64": "四届一次",
            "63": "三届一次",
            "62": "二届一次",
            "61": "一届一次"
          }
          const proposalTypeMap = {
            "1": "大会议案",
            "2": "大会建议",
            "3": "闭会建议",
            "4": "供参考建议",
          }

          const statusMap = {
            10: { title: "提交", step: 0 },
            20: { title: "校核", step: 1 },
            21: { title: "预分类", step: 2 },
            22: { title: "初审", step: 3 },
            25: { title: "复审", step: 3 },
            30: { title: "复审", step: 4 },
            40: { title: "分办", step: 5 },
            41: { title: "不予立案审核", step: 5 }, // 41归为分办
            42: { title: "分办审核中", step: 5 },
            43: { title: "分办复核中", step: 5 }, // 41归为分办
            45: { title: "不予立案确认", step: 5 }, // 45归为分办
            50: { title: "签收", step: 6 },
            60: { title: "答复", step: 7 },
            70: { title: "评价", step: 8 },
            90: { title: "已办结", step: 9 }
          };

          this.listTotal = res.data.total;
          // this.findPageListTotal = res.data.total
          this.dataList = res.data.records.map(item => ({
            ...item,
            meeting: meetingMap[item.meeting] || item.meeting,
            // meeting: this.lastMeetingId,
            status: statusMap[item.status]?.title || item.status,
            proposalType: proposalTypeMap[item.proposalType] || item.proposalType
          }));
          console.log(this.dataList)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

  },
};
</script>

<style lang="less" scoped>
.liaisonStation-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .header {
    position: absolute;
    right: 60px;
    top: 40px;

    .btn {
      padding: 10px 38px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 40px;
      color: #ffffff;
      line-height: 47px;
      letter-spacing: 4px;
      background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
      border-radius: 6px 6px 6px 6px;
      height: 56px;
      cursor: pointer;
    }
  }

  .liaisonStation-main_center {
    flex: 1;
    margin-left: -200px;

    .center-calendar {
      height: 1430px;
      margin-top: 25px;
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      padding-left: 178px;
    }
  }
}

.liaisonStation-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
}

.con-bottom-box {
  height: 1300px;
  padding: 40px 80px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
