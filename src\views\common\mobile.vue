<template>
  <div>
    <a-modal :title="title"
             ellipsis:
             true,:
             ellipsis:
             true,visible
             .sync="dialogFormVisible"
             width="1000px"
             @cancel="close">
      <a-form-model :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 16 }"
                    v-if="flag == 1"
                    label-width="80px"
                    ref="mobildForm"
                    :model="mobildForm"
                    :rules="rules">
        <a-form-model-item prop="code"
                           class="login-form-admin wdLabel"
                           label="验证码：">
          <a-input v-model.trim="mobildForm.code"
                   autocomplete="off"
                   autocapitalize="off"
                   placeholder="请输入验证码"
                   tabindex="3"
                   type="text"
                   style="width: 50%; float: left;"
                   @keyup.enter.native="handleLogin"
                   class="new-btn new-fgbtn" />
          <div class="captcha_code"
               style="width: 80px; margin: 6px 5px; float: left; margin-left: 6%;">
            <img :src="imgData"
                 @click="changeCode" />
          </div>
        </a-form-model-item>

        <a-form-model-item prop="mobile"
                           class="login-form-admin wdLabel"
                           label="手机号：">
          <a-input v-model.trim="mobildForm.mobile"
                   autocomplete="off"
                   autocapitalize="off"
                   placeholder="请输入手机号"
                   tabindex="3"
                   type="text"
                   style="width: 50%; float: left;"
                   class="new-btn new-fgbtn" />

          <div class="captcha_code"
               style="width: 80px; margin: 6px 5px; float: left; margin-left: 6%;">
            <a-button class="smsButton"
                      style="border-radius: 10px;"
                      @click="sendcodeMsg"
                      :disabled="disab">{{ getCode }}</a-button>
          </div>
        </a-form-model-item>
        <a-form-model-item class="wdLabel"
                           prop="smscode"
                           label="短信验证码：">
          <a-input placeholder="请输入短信验证码"
                   class="new-btn new-fgbtn"
                   v-model="mobildForm.smscode"
                   number
                   maxlength="6"></a-input>
        </a-form-model-item>
      </a-form-model>

      <template slot="footer">
        <a-button type="default"
                  style="margin-right: 15px;"
                  @click="close">取消</a-button>
        <a-button type="primary"
                  @click="handleMobileSubmit">提交</a-button>
      </template>

      <a-modal width="60%"
               title="请选择账号登录"
               ellipsis:
               true,
               :visible.sync="innerVisible"
               @cancel="close1"
               append-to-body>
        <a-table v-if="isShow"
                 bordered
                 ref="meetingTable"
                 :columns="columns"
                 :data-source="list"></a-table>

        <!-- 
      <el-table
        ref="meetingTable"
        :data="list"
        border
        v-if="isShow"
      >
        <el-table-column label="帐号" prop="userAccount" width="200"></el-table-column>
        <el-table-column label="姓名" prop="userName" ></el-table-column>
        <el-table-column label="手机号"  prop="mobile"></el-table-column>
        <el-table-column
          prop="id"
          label="操作"
          width="120px"
          fixed="right"
        >
          <template slot-scope="scope">
            <p >
              <a-button type="text" style="color:#E6A23C" @click="Details(scope.row.userAccount,mobileToken)">选择该账号</a-button>
            </p>
          </template>

        </el-table-column>
      </el-table> -->
      </a-modal>
    </a-modal>
    <!--    <uList-fram ref="uListFram"></uList-fram>>-->
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getCaptcha } from "@/api/captcha";
import { doUpdata, validateAccount, sendMsg } from "@/api/mobile";
import uListFram from "../common/mobleUserList";
import request from "@/utils/request";
import Vue from "vue";
import axios from "axios";
export const instance_1 = axios.create({
  baseURL: process.env.VUE_APP_BASE_API + "/api/v1",
  timeout: 30000,
  headers: {
    "Content-type": "application/json;charset=UTF-8",
  },
});
let ANTmobileToken = "";
export default {
  components: {
    uListFram,
  },
  name: "TableEdit",
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  data () {
    return {
      TBloading: false,
      imgData: "",
      isShow: false,
      outerVisible: false,
      innerVisible: false,
      mobileToken: "",
      sendcode: "发送验证码",
      redirect: undefined,
      disab: false,
      mobildForm: {
        userAccount: "",
        mobile: "",
        userPassword: "",
        repeatpassword: "",
        code: "",
        key: "",
        generateValue: "",
        smscode: "",
      },
      // 列表
      columns: [
        {
          title: "账号",
          ellipsis: true,
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            return record.userAccount || "/";
          },
        },
        {
          title: "姓名",
          ellipsis: true,
          align: "center",
          width: 250,
          customRender: (text, record, index) => {
            return record.userName || "/";
          },
        },
        {
          title: "手机号",
          ellipsis: true,
          align: "center",
          width: 250,
          customRender: (text, record, index) => {
            return record.mobile || "/";
          },
        },

        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 120,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.Details(record.userAccount, ANTmobileToken);
                    },
                  },
                },
                "选择该账号"
              ),
            ]);
          },
        },
      ],

      getCode: "获取验证码",
      isGeting: false,
      count: 120,

      rules: {
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
        ],

        code: [
          {
            required: true,
            message: "图形验证码不能为空",
            trigger: "blur",
          },
        ],
        smscode: [
          {
            required: true,
            message: "短信验证码不能为空",
            trigger: "blur",
          },
        ],
      },

      calendId: "",
      tempImg: "",
      name: "imgUpload",
      upLoadUrl:
        process.env.VUE_APP_BASE_API + "/api/v1/meetingSeating/newimpseatexcle",
      action: process.env.VUE_APP_BASE_API + "/api/v1/meetingFile/fileUpload",
      photoContent: {
        meetingId: "",
        relId: "",
        type: "0",
        encrypted: "false",
      },
      list: [],
      title: "手机密码登录",
      ellipsis: true,
      dialogFormVisible: false,
      imgLimit: 10,
      productImgs: [],
      isMultiple: false,
      dialogImageUrl: "",
      dialogVisible: false,
      seatingListShow: false,
      scrollHeight: "0px",
      elementLoadingText: "正在加载...",
      flag: 1,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created () {
    this.isUpdate = 1;
    this.scrollHeight = window.innerHeight + "px";
  },
  mounted () {
    if ("production" !== process.env.NODE_ENV) {
    }
    setTimeout(() => {
      this.animateShow = true;
    });
    // 得到验证码图片
    // this.changeCode();
  },
  methods: {
    Details (userAccount, mobileToken) {
      console.log("ddd", mobileToken, "--->", userAccount);
      instance_1({
        method: "post",
        url: "/wechat/wechatsmallapp/userMobileLogin", //
        params: {
          token: mobileToken,
          account: userAccount,
        },
      }).then((data) => {
        this.loading = true;
        if (data.data.data.token != "" && data.data.data.token != null) {
          this.$store
            .dispatch("user/onelogin", data)
            .then((res) => {
              this.innerVisible = false;
              this.isShow = false;
              this.$router.push({ path: "/" }).catch(() => { });
              this.loading = false;
            })
            .catch((error) => {
              console.log(error);
              if (error.message != "验证码不匹配") {
                this.changeCode();
              }
            });
        }
      });
    },
    handleShow (row) {
      this.mobildForm.code = "";
      this.mobildForm.key = "";
      this.mobildForm.mobile = "";
      this.mobildForm.smscode = "";
      this.mobildForm.repeatpassword = "";
      this.changeCode();
      this.dialogFormVisible = true;
      console.log("row", row);
    },
    close () {
      this.dialogFormVisible = false;
    },
    close1 () {
      this.innerVisible = false;
    },
    validateAcc () {
      var that = this;
      var code = that.mobildForm.code;
      var key = that.mobildForm.key;
      const data2 = {
        code: that.mobildForm.code,
        key: that.mobildForm.key,
      };

      if (code.length != 4) {
        this.$message.error("请输入正确的图形验证码");
        // this.$message({
        //   message: "请输入正确的图形验证码",
        //   type: "error",
        // });
        return;
      }
      validateAccount(data2).then(() => {
        console.log("验证成功");
        this.flag = 2;
      });
    },
    handleMobileSubmit () {
      const data2 = {
        mobile: this.mobildForm.mobile,
        smscode: this.mobildForm.smscode,
      };
      this.$refs["mobildForm"].validate((valid) => {
        if (valid) {
          if (this.mobildForm.smscode == "") {
            this.$message.error("手机验证码不能为空");
            return false;
          }

          // doUpdata(data2).then(() => {

          //     this.$router.push({ path: "/site/message" }).catch(() => {});
          // });

          this.$store
            .dispatch("user/mobileLogin", data2)
            .then((res) => {
              console.log(JSON.stringify(res) + "--------" + res);
              if (
                res.data.mobileToken == "" ||
                res.data.mobileToken == undefined
              ) {
                //判断是否为空，不为空就就证明只有一个账号
                const routerPath =
                  this.redirect === "/404" ? "/" : this.redirect;
                this.$router.push({ path: "/" }).catch(() => { });
                this.loading = false;
              } else {
                //存在多个账号
                this.list = res.data.userList;
                this.dialogFormVisible = false;
                this.innerVisible = true;
                this.isShow = true;
                this.mobileToken = res.data.mobileToken;
                // 新赋值mobileToken
                ANTmobileToken = res.data.mobileToken;
              }
            })
            .catch((error) => {
              if (error.message != "验证码不匹配") {
                this.changeCode();
              }
            });
        }
      });
    },

    changeCode () {
      let that = this;
      getCaptcha().then((res) => {
        if (res.code == 200) {
          that.mobildForm.key = res.data.key;
          that.mobildForm.generateValue = res.data.generateValue;
          let file = res.data.image; // 把整个base64给file
          var type = "image/png"; // 定义图片类型（canvas转的图片一般都是png，也可以指定其他类型）
          let conversions = that.base64ToBlob(file, type); // 调用base64转图片方法----vue写法
          // window.URL = window.URL || window.webkitURL; //blob对象转换为blob-url
          var url = window.URL.createObjectURL(conversions);
          that.imgData = url;
        }
      });
    },
    /**
     * base64转blob
     * 原理：利用URL.createObjectURL为blob对象创建临时的URL
     */
    base64ToBlob (urlData, type) {
      let arr = urlData.split(",");
      let mime = arr[0].match(/:(.*?);/)[1] || type;
      // 去掉url的头，并转化为byte
      let bytes = window.atob(arr[1]);
      // 处理异常,将ascii码小于0的转换为大于0
      let ab = new ArrayBuffer(bytes.length);
      // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
      let ia = new Uint8Array(ab);
      for (let i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ab], {
        type: mime,
      });
    },
    sendcodeMsg () {
      var that = this;
      var code = that.mobildForm.code;
      var mobile = that.mobildForm.mobile;

      const data2 = {
        key: that.mobildForm.key,
        mobile: that.mobildForm.mobile,
        code: that.mobildForm.code,
      };
      if (code == "") {
        this.$message.error("请输入图形验证码");

        return false;
      }
      if (mobile.length != 11) {
        this.$message.error("请输入正确的手机号");
        return false;
      }

      var code = that.mobildForm.code;
      var generateValue = that.mobildForm.generateValue;
      if (code != generateValue) {
        this.$message.error("动态验证码不正确");
        return false;
      }

      var countDown = setInterval(() => {
        if (this.count < 1) {
          this.isGeting = false;
          this.disab = false;
          this.getCode = "获取验证码";
          this.count = 120;
          clearInterval(countDown);
        } else {
          this.isGeting = true;
          this.disab = true;
          this.getCode = this.count-- + "s后重发";
        }
      }, 1000);

      sendMsg(data2).then(() => { });
    },
  },
};
</script>
<style scoped>
.el-dialog__header {
  padding: 38px;
  padding-bottom: 10px;
}

.wdLabel {
  width: 80%;
  margin-right: 20px;
}

.smsButton {
  height: 43px;
  border-radius: 5px;
  background-color: #1890ff;
  color: white;
  width: 120px;
}

.el-form-item__label {
  width: 200px !important;
}

.new-fgbtn {
  width: 50% !important;
}

.wdLabel .el-form-item__error {
  left: 129px;
}

.codeGeting {
  background: #cdcdcd;
  border-color: #cdcdcd;
}
.new-btn {
  height: 40px;
}
</style>
