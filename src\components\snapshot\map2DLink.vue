<template>
  <div ref="loginTimes"></div>
</template>
<script>
import geoJson from "../geoData.json";
import * as echarts from "echarts";
export default {
  name: "wordCloud",
  props: {
    // 标题
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    
    // 初始化视图
    this.getIint();
    // this.getPosition();
  },
  methods: {
    getPosition() {
      console.log('data1')
      console.log(data)
      // let name = this.data[2].name;
      // let isLeft = ['越秀区', '白云区', '花都区', '荔湾区', '海珠区', '番禺区', '南沙区'].includes(name);
      // 动态计算位置，与引线长度匹配
      // console.log('isLeft')
      // console.log(isLeft)
    },
    getIint() {
      // this.getPosition();
      // 获取到ref绑定为loginTimes的DOM节点，以canvas的形式展现在视图层
      let myChart = this.$echarts.init(this.$refs.loginTimes);

      let data = geoJson.features.map((item) => {
        return {
          name: item.properties.name,
        };
      });

      const resData = {
        status: [
          { name: "越秀区", level: "0" },
          { name: "海珠区", level: "0" },
          { name: "荔湾区", level: "0" },
          { name: "天河区", level: "0" },
          { name: "白云区", level: "0" },
          { name: "黄埔区", level: "0" },
          { name: "番禺区", level: "0" },
          { name: "花都区", level: "0" },
          { name: "南沙区", level: "0" },
          { name: "从化区", level: "0" },
          { name: "增城区", level: "0" },
        ],
      };

      var convertData = function (data) {
        var res = [];
        for (var i = 0; i < data.length; i++) {
          var geoCoord = geoCoordMap[data[i].name];
          if (geoCoord) {
            res.push({
              name: data[i].name,
              value: geoCoord.concat(data[i].value),
            });
          }
        }
        return res;
      };
      const regions = resData.status.map((element) => {
        const { name } = element;
        return {
          name,
          itemStyle: {
            normal: {
              areaColor: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  { offset: 0, color: "rgba(1, 102, 204, 1)" },
                  { offset: 1, color: "rgba(21, 173, 250, 1)" },
                ],
                global: false,
              },
            },
          },
        };
      });
      var toolTipData = [
        { name: "越秀区" },
        { name: "海珠区" },
        { name: "荔湾区" },
        { name: "天河区" },
        { name: "白云区" },
        { name: "黄埔区" },
        { name: "番禺区" },
        { name: "花都区" },
        { name: "南沙区" },
        { name: "从化区" },
        { name: "增城区" },
      ];

      var geoCoordMap = {
        越秀区: [113.280714, 23.125624],
        海珠区: [113.326676, 23.082002],
        荔湾区: [113.212955, 23.064264],
        // 荔湾区: [113.212955, 23.044264],
        天河区: [113.402278, 23.180254],
        白云区: [113.323943, 23.289328],
        黄埔区: [113.50827, 23.216905],
        番禺区: [113.406928, 22.973597],
        花都区: [113.214795, 23.442737],
        南沙区: [113.53738, 22.794531],
        从化区: [113.587386, 23.545283],
        增城区: [113.764273, 23.341055],
      };
      // 柱状体的顶部
      function scatterData() {
        return toolTipData.map((item) => {
          return [geoCoordMap[item.name][0], geoCoordMap[item.name][1], item];
        });
      }
      console.log(scatterData())
      let option = {
        backgroundColor: "transparent",
        geo: [
          {
            map: "centerMap",
            aspectScale: 1,
            zoom: 0.53,
            layoutCenter: ["50%", "50%"],
            layoutSize: "180%",
            show: true,
            roam: false,
            label: {
              normal: {
                show: false,
                textStyle: { color: "#fff", fontSize: "120%" },
              },
              emphasis: {
                show: false,
              },

            },

            itemStyle: {
              normal: {
                borderWidth: 4,
                borderColor: "#fff",
                shadowColor: "rgba(2, 24, 228, 0.6)",
                shadowOffsetY: 15,
                shadowBlur: 15,
                areaColor: "rgba(5,21,35,0.1)",
              },
            },
            regions: regions,
          },
          {
            type: "map",
            map: "centerMap",
            zlevel: -2,
            aspectScale: 1,
            zoom: 0.53,
            layoutCenter: ["50%", "52%"],
            layoutSize: "180%",
            roam: false,
            silent: true,
            // 底部阴影颜色
            itemStyle: {
              normal: {
                borderWidth: 4,
                borderColor: "rgba(2, 24, 228, 1)",
                shadowColor: "rgba(2, 24, 228, 0.6)",
                shadowOffsetY: 0,
                shadowBlur: 0,
                // 底层区域颜色
                areaColor: {
                  type: "radial",
                  x: 0,
                  y: 0.5,
                  r: 0.5,
                  // 底层线性渐变颜色
                  colorStops: [
                    { offset: 0, color: "rgba(2, 24, 228, 1)" },
                    { offset: 1, color: "rgba(9, 176, 254, 1)" },
                  ],
                  global: false,
                },
              },
            },
          },
        ],

        series: [
          {
            name: "centerMap",
            type: "map",
            zlevel: 99,
            z: 99,
            map: "centerMap", // 自定义扩展图表类型
            aspectScale: 1,
            zoom: 0.53, // 缩放
            showLegendSymbol: true,
            // 在地图显示文字 设置为 false 默认不展示
            label: {
              normal: {
                show: false,
                textStyle: { color: "#fff", fontSize: "120%" },
              },
              emphasis: {
                show: false,
              },
            },
            // 控制最顶层地图颜色
            itemStyle: {
              normal: {
                areaColor: {
                  type: "linear",
                  x: 1200,
                  y: 0,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 1,
                      color: "rgba(0,0,0,0)", // 50% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
                borderColor: "#fff",
                borderWidth: 0.2,
              },
              emphasis: {
                show: true,
                // color: "#fff",
                areaColor: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  // 鼠标移入的线性渐变颜色
                  colorStops: [
                    { offset: 1, color: "rgba(204, 165, 0, 0.70)" },
                    { offset: 0, color: "rgba(153, 124, 0, 0)" },
                  ],
                  global: false,
                },
              },
            },
            layoutCenter: ["50%", "50%"],
            layoutSize: "180%",
            markPoint: {
              symbol: "none",
            },
            data: data,
          },
          {
            type: "scatter",
            coordinateSystem: "geo",
            geoIndex: 0,
            zlevel: 999,
            label: {
              normal: {
                marginLeft: 50,
                show: true,
                position: 'right',
                // position: 'right',
                // position: ['越秀区', '白云区', '花都区', '荔湾区', '海珠区', '番禺区', '南沙区'].includes(params.data[2].name) ? 'left' : 'right',
                // position: function (params) {
                //   const name = params.data[2].name;
                //   const isLeft = ['越秀区', '白云区', '花都区', '荔湾区', '海珠区', '番禺区', '南沙区'].includes(name);
                //   // 动态计算位置，与引线长度匹配
                //   console.log('isLeft')
                //   console.log(isLeft)
                //   return isLeft ? 'left' : 'right';
                // }, 

                formatter: function (params) {
                  console.log('params')
                  console.log(params)
                  return params.data[2].name;
                },
                fontSize: 32,
                color: '#fff',
                backgroundColor: '#cf4419',
                padding: [5, 10],
                borderRadius: 4,
                distance: 150, // 标签与标记点的距离
              }
            },
            // 标记点坐标标点
            itemStyle: {
              opacity: 1,
              color: '#CF4419',
              borderColor: '#fff',
              borderWidth: 2
            },
            symbol: 'circle',
            symbolSize: 20, // 缩小标记点大小
            data: scatterData(),
            // 引线样式
            markLine: { // 添加引线
              symbol: ['none', 'none'],
              lineStyle: {
                color: '#fff',
                width: 2,
                type: 'solid'
              },
              data: scatterData().map(item => {
                return [{
                  coord: [item[0], item[1]],
                  symbol: 'none'
                }, {
                  coord: [item[0] + 0.3, item[1]], // 调整引线长度
                  symbol: 'none'
                }]
              })
            }
          },
          // {
          //   name: "Top 5",
          //   type: "scatter",
          //   coordinateSystem: "geo",
          //   data: convertData(toolTipData),
          //   showEffectOn: "render",
          //   rippleEffect: {
          //     scale: 5,
          //     brushType: "stroke",
          //   },
          //   label: {
          //     normal: {
          //       formatter: "{b}",
          //       position: "bottom",
          //       show: false,
          //       color: "#fff",
          //       distance: 10,
          //       textStyle: { color: "#af3024", fontSize: "120%" },

          //     },
          //   },
          //   symbol: "triangle",
          //   symbolSize: [89, 53],
          //   symbolRotate: 180,
          //   symbolOffset: [0, -34],
          //   itemStyle: {
          //     normal: {
          //       color: function () {
          //         // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，分别表示右,下,左,上。例如（0，0，0，1）表示从正上开始向下渐变；如果是（1，0，0，0），则是从正右开始向左渐变。
          //         // 相当于在图形包围盒中的百分比，如果最后一个参数传 true，则该四个值是绝对的像素位置
          //         return new echarts.graphic.LinearGradient(
          //           0,
          //           0.35,
          //           0.65,
          //           1,
          //           [
          //             {
          //               offset: 1,
          //               color: "rgba(210, 74, 24, 1)",
          //             },
          //             {
          //               offset: 0,
          //               color: "rgba(195, 221, 251, 1)",
          //             },
          //           ],
          //           false
          //         );
          //       },
          //       shadowBlur: 15,
          //       shadowColor: "rgba(0, 153, 255, 0.94)",
          //     },

          //     opacity: 1,
          //   },
          //   zlevel: 100,
          // },
        ],
      };

      myChart.showLoading();
      console.log(myChart.registerMap);
      //   $.getJSON(mapJson, function (geoJson) {
      this.$echarts.registerMap("centerMap", geoJson);
      myChart.hideLoading();

      myChart.setOption(option);

      let _this = this;
      // 鼠标移入浮窗方法
      myChart.on("mouseover", function (params) {

        // 点击地图弹出浮窗
        if (params.componentType === 'series' && params.seriesType === 'scatter') {
          // 触发事件并传递参数
          console.log(params)
          console.log(geoJson)
          _this.$emit("getMapInfo", params, geoJson);
        }
        // _this.$emit("getMapInfo", params, geoJson); // 触发确认事件并传递数据
        // alert(JSON.stringify(params));

        // 关闭鼠标移入高亮效果
        // myChart.setOption({
        //   series: [{
        //     type: 'map',  // 地图类型
        //     label: {
        //       normal: {
        //         show: false,
        //         textStyle: { color: "rgba(0,0,0,0)", fontSize: 0 },
        //       },
        //       emphasis: {
        //         show: false,
        //       },
        //     },
        //     emphasis: {
        //       itemStyle: {
        //         areaColor: {
        //           type: "radial",
        //           x: 0.5,
        //           y: 0.5,
        //           r: 0.5,
        //           // 鼠标移入的线性渐变颜色
        //           colorStops: [
        //             { offset: 1, color: "rgba(204, 165, 0, 0.70)" },
        //             { offset: 0, color: "rgba(153, 124, 0, 0)" },
        //           ],
        //           global: false,
        //         },
        //       }
        //     }
        //   }]
        // });
      });

      // 鼠标点击 跳转对应区域 显示对应区域地图
      myChart.on("click", function (params) {
        console.log('点击跳转')
        console.log(params)
        // 判断点击的是地图区域（非散点或其他元素）
        if (params.componentType === 'series') {
          const areaId = geoJson.features[params.dataIndex].properties.adcode
          // 跳转逻辑（示例：Vue Router）
          if (areaId) {
            _this.$router.push({
              path: '/junior',
              query: {
                administrativeAreaId: areaId,
                areaName: params.name
              }
            });
          }
        }
      });

      //   });

      // echarts参数设置
      myChart.setOption(option);
    },
  },
};
</script>
