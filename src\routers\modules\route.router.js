/* eslint-disable arrow-parens */
import loginRoutes from './login'; // 引入登录模块
/* 首页 */
const MOTION = (r) =>
  require.ensure([], () => r(require("@/views/motion/index")), "");
const QUMOTION = (r) =>
  require.ensure([], () => r(require("@/views/quMotion/index")), "");
const SNAPSHOT = (r) =>
  require.ensure([], () => r(require("@/views/snapshot/index")), "");
const REGION = (r) =>
  require.ensure([], () => r(require("@/views/region/index")), "");
const rdWorkPanorama = (r) =>
  require.ensure([], () => r(require("@/views/rdWorkPanorama/index")), "");
const dataTemplate = (r) =>
  require.ensure([], () => r(require("@/views/dataTemplate/index")), "");
const dataDetailTemplate = (r) =>
  require.ensure([], () => r(require("@/views/dataDetailTemplate/index")), "");
const dataTemplatePopularwill = (r) =>
  require.ensure([], () => r(require("@/views/dataTemplatePopularwill/index")), "");
const dutyActiveDetail = (r) =>
  require.ensure([], () => r(require("@/views/perform/dutyActiveDetail")), "");
const yearRankingList = (r) =>
  require.ensure([], () => r(require("@/views/perform/yearRankingList")), "");
const JUNIOR = (r) =>
  require.ensure([], () => r(require("@/views/junior/index")), "");
const LIAISONSTATION = (r) =>
  require.ensure([], () => r(require("@/views/liaisonStation/index")), "");
const REPRINFO = (r) =>
  require.ensure([], () => r(require("@/views/reprInfo/index")), "");
const PERFORM = (r) =>
  require.ensure([], () => r(require("@/views/perform/index")), "");
const PERSONNEL = (r) =>
  require.ensure([], () => r(require("@/views/personnel/index")), "");
const QUPERSONNEL = (r) =>
  require.ensure([], () => r(require("@/views/quPersonnel/index")), "");
const pdf = (r) =>
  require.ensure([], () => r(require("@/views/liaisonDetail/eventAnnouncement/pdf")), "");
const POPULARWILL = (r) =>
  require.ensure([], () => r(require("@/views/popularWill/index")), "");
const LIAISONDETAIL = (r) =>
  require.ensure([], () => r(require("@/views/liaisonDetail/index")), "");
const BASICINFORMATION = (r) =>
  require.ensure(
    [],
    () => r(require("@/views/liaisonDetail/basicInformation/index")),
    ""
  );
const VIDEOCONNECTION = (r) =>
  require.ensure(
    [],
    () => r(require("@/views/liaisonDetail/videoConnection/index")),
    ""
  );
const EVENTANNOUNCEMENT = (r) =>
  require.ensure(
    [],
    () => r(require("@/views/liaisonDetail/eventAnnouncement/index")),
    ""
  );

const SOLICITATIONOPINIONS = (r) =>
  require.ensure(
    [],
    () => r(require("@/views/liaisonDetail/solicitationOpinions/index")),
    ""
  );
const STATIONREPRESENTATIVE = (r) =>
  require.ensure(
    [],
    () => r(require("@/views/liaisonDetail/stationRepresentative/index")),
    ""
  );
const STATIONDETAILS = (r) =>
  require.ensure(
    [],
    () => r(require("@/views/liaisonDetail/stationRepresentative/stationDetails")),
    ""
  );

const HANDLINGOPINION = (r) =>
  require.ensure(
    [],
    () => r(require("@/views/liaisonDetail/handlingOpinion/index")),
    ""
  );

const DOME = (r) =>
  require.ensure([], () => r(require("@/views/dome/index")), "");

export default [{
    path: "/region",
    component: REGION,
    name: "region",
    meta: {
      title: "人大代表工作视窗",
      keepAlive: false,
    },
  },
  ...loginRoutes, // 将登录模块的路由配置合并到主路由中
  {
    path: "/motion",
    component: MOTION,
    name: "motion",
    meta: {
      title: "议案工作视窗",
      // keepAlive: false,
    },
  },
  {
    path: "/quMotion",
    component: QUMOTION,
    name: "quMotion",
    meta: {
      title: "议案工作视窗",
      // keepAlive: false,
    },
  },
  {
    path: "/snapshot",
    component: SNAPSHOT,
    name: "snapshot",
    meta: {
      title: "随手拍工作视窗",
      keepAlive: false,
    },
  },
  {
    path: "/junior",
    component: JUNIOR,
    name: "junior",
    meta: {
      title: "区域代表工作视窗",
      keepAlive: false,
    },
  },
  {
    path: "/liaisonStation",
    component: LIAISONSTATION,
    name: "liaisonStation",
    meta: {
      title: "各镇联络站",
      keepAlive: false,
    },
  },
  {
    path: "/liaisonDetail",
    component: LIAISONDETAIL,
    name: "liaisonDetail",
    meta: {
      title: "联络站详情",
      keepAlive: false,
    },
    redirect: "/liaisonDetail/basicInformation",
    children: [{
        path: "/liaisonDetail/basicInformation",
        component: BASICINFORMATION,
        name: "basicInformation",
        meta: {
          title: "基础信息",
          keepAlive: false,
        },
      },
      {
        path: "/liaisonDetail/videoConnection",
        component: VIDEOCONNECTION,
        name: "videoConnection",
        meta: {
          title: "视频连线",
          keepAlive: false,
        },
      },
      {
        path: "/liaisonDetail/eventAnnouncement",
        component: EVENTANNOUNCEMENT,
        name: "eventAnnouncement",
        meta: {
          title: "活动公告",
          keepAlive: false,
        },
      },
      {
        path: "/liaisonDetail/solicitationOpinions",
        component: SOLICITATIONOPINIONS,
        name: "solicitationOpinions",
        meta: {
          title: "意见征集",
          keepAlive: false,
        },
      },
      {
        path: "/liaisonDetail/stationRepresentative",
        component: STATIONREPRESENTATIVE,
        name: "stationRepresentative",
        meta: {
          title: "进站代表",
          keepAlive: false,
        },
      },
      {
        path: "/liaisonDetail/stationDetails",
        component: STATIONDETAILS,
        name: "stationDetails",
        meta: {
          title: "代表详情",
          keepAlive: false,
        },
      },
      {
        path: "/liaisonDetail/handlingOpinion",
        component: HANDLINGOPINION,
        name: "handlingOpinion",
        meta: {
          title: "问题处理",
          keepAlive: false,
        },
      },
    ],
  },
  {
    path: "/reprInfo",
    component: REPRINFO,
    name: "reprInfo",
    meta: {
      title: "代表信息工作视窗",
      keepAlive: false,
    },
  },
  {
    path: "/perform",
    component: PERFORM,
    name: "perform",
    meta: {
      title: "履职工作视窗",
      keepAlive: false,
    },
  },
  {
    path: "/personnel",
    component: PERSONNEL,
    name: "personnel",
    meta: {
      title: "人事任免工作视窗",
      keepAlive: false,
    },
  },
  {
    path: "/quPersonnel",
    component: QUPERSONNEL,
    name: "quPersonnel",
    meta: {
      title: "人事任免工作视窗",
      keepAlive: false,
    },
  },
  {
    path: "/popularWill",
    component: POPULARWILL,
    name: "popularWill",
    meta: {
      title: "民意收集情况工作视窗",
      keepAlive: false,
    },
  },
  {
    path: "/dome",
    component: DOME,
    name: "dome",
    meta: {
      title: "用于测试组件",
      keepAlive: false,
    },
  },
  {
    path: "/rdWorkPanorama",
    component: rdWorkPanorama,
    name: "rdWorkPanorama",
    meta: {
      title: "人大工作全景视窗",
      keepAlive: false,
    },
  },
  {
    path: "/dataTemplate",
    component: dataTemplate,
    name: "dataTemplate",
    meta: {
      title: "数据列表",
      keepAlive: false,
    },
  },
  {
    path: "/dataDetailTemplate",
    component: dataDetailTemplate,
    name: "dataDetailTemplate",
    meta: {
      title: "数据详情",
      keepAlive: false,
    },
  },
  {
    path: "/dataTemplatePopularwill",
    component: dataTemplatePopularwill,
    name: "dataTemplatePopularwill",
    meta: {
      title: "数据详情",
      keepAlive: false,
    },
  },
    {
    path: "/dutyActiveDetail",
    component: dutyActiveDetail,
    name: "dutyActiveDetail",
    meta: {
      title: "数据详情",
      keepAlive: false,
    },
  },
      {
    path: "/yearRankingList",
    component: yearRankingList,
    name: "yearRankingList",
    meta: {
      title: "活动数据列表",
      keepAlive: false,
    },
  },
  {
    path: "/pdf",
    component: pdf,
    name: "pdf",
    meta: {
      title: "pdf",
      keepAlive: false,
    },
  },
];