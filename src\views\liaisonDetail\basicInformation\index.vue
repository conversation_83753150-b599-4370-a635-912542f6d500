<template>
  <div class="basicInformation-page">
    <div class="basicInformation-main_content">
      <div class="qrcode">
        <canvas ref="qrCanvas"></canvas>
      </div>
      <!-- 二维码未有接口 -->
      <img class="right-img" :src="require('@/assets/image/jcxx_rt2.png')" alt="" />
      <div class="content">
        <!-- 需要动态展示 -->
        <div class="content__header">{{ liaisonStationInfo.name }}</div>
        <div class="content__warp">
          <div class="card1">
            <div class="bg-title">站长信息</div>
            <div class="card1-text">
              <div>站长：</div>
              <div>{{ liaisonStationInfo.userName }}</div>
            </div>
            <div class="card1-text">
              <div>站长电话：</div>
              <div>{{ liaisonStationInfo.stationAgentPhoneNumber }}</div>
            </div>
            <div class="card1-text">
              <div>站长办公电话：</div>
              <div>{{ liaisonStationInfo.stationOfficePhone }}</div>
            </div>
          </div>
          <div class="line"></div>
          <div class="card1">
            <div class="bg-title">联络人信息</div>
            <div class="card1-text">
              <div>联系人：</div>
              <div>{{ liaisonStationInfo.contactName }}</div>
            </div>
            <div class="card1-text">
              <div>联系人电话：</div>
              <div>{{ liaisonStationInfo.contactPhoneNumber }}</div>
            </div>
            <div class="card1-text">
              <div>办公电话：</div>
              <div>{{ liaisonStationInfo.contactOfficePhone }}</div>
            </div>
          </div>
        </div>
      </div>
      <img :src="require('@/assets/image/three_d.png')" alt="" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import QRCode from "qrcode";
import {
  getLiaisonStationList,
  getLiaisonStationListByJSC,
} from "@/api/dataPageApi/liaisonStation";
export default {
  name: "basicInformation-index",
  components: {},
  data() {
    return {
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],
      qrText: "",
      liaisonStationInfo: []
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.streetTownId = this.$route.query.streetTownId;
    this.liaisonStationId = this.$route.query.liaisonStationId;
    this.getLiaisonStationList(this.administrativeAreaId, this.streetTownId, this.liaisonStationId)
  },
  mounted() {
    // this.generateQRCode();
  },
  watch: {},
  methods: {
    getLiaisonStationList(administrativeAreaId, streetTownId, liaisonStationId) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        pageNum: 1,
        pageSize: 10,
        // 先展示从化区吕田镇
        administrativeAreaId: administrativeAreaId,
        streetTownId: streetTownId,
        liaisonStationId: liaisonStationId,
        // streetTownId: streetTownId,
        // administrativeAreaId: administrativeAreaId,
      }
      getLiaisonStationListByJSC(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 具体到每一条联络站的详情
          this.liaisonStationInfo = res.rows[0]
          this.qrText= "https://rdtest.rd.gz.cn/idblz/public/#/onlineLiaison?liaisonStationId=" + res.rows[0].id
          this.generateQRCode(this.qrText)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    generateQRCode(url) {
      console.log(url)
      QRCode.toCanvas(this.$refs.qrCanvas, url , (error) => {
        if (error) console.error(error);
        console.log("QR code generated!");
      });
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
.basicInformation-page {
  width: calc(100%);
  height: calc(100vh - 500px);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  // padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .basicInformation-main_content {
    width: 100%;
    margin-bottom: 25px;
    position: relative;

    .qrcode {
      width: 400.63px;
      height: 400.63px;
      background: url("@/assets/image/qrcode.png") no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 168px;
      left: 102px;
      padding: 60px;

      canvas {
        width: 400px !important;
        height: 400px !important;
        border-radius: 0px 0px 0px 0px;
      }
    }

    .content {
      margin-top: 382px;
      background: url("@/assets/image/llz_jcxx.png") no-repeat;
      height: 891px;
      background-size: contain;
      padding: 154px 0 0 702px;

      .content__header {
        font-family: PangMenZhengDao, PangMenZhengDao;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 70px;
        color: #ffffff;
        line-height: 42px;
        letter-spacing: 7px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .content__warp {
        margin-top: 120px;
        display: flex;
        flex-direction: row;

        .card1 {
          display: flex;
          flex-direction: column;
          min-width: 700px;

          .bg-title {
            width: 494px;
            height: 121.86px;
            background: url("@/assets/image/jjxx_card_bg.png") no-repeat;
            background-size: contain;
            font-family: PingFang SC, PingFang SC;
            // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: 600;
            font-size: 42px;
            color: #ffffff;
            text-align: center;
            letter-spacing: 4px;
            text-shadow: 0px 2px 3px rgba(51, 158, 188, 0.79);
            font-style: italic;
            text-transform: none;
            line-height: 142px;
            margin-bottom: 19px;
            padding-left: 30px;
          }

          .card1-text {
            margin-top: 50px;
            font-family: PangMenZhengDao, PangMenZhengDao;
            // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: 600;
            font-size: 42px;
            color: #ffffff;
            line-height: 42px;
            letter-spacing: 7px;
            font-style: italic;
            text-transform: none;
            display: flex;
            flex-direction: row;

            :first-child {
              width: 350px;
            }
          }
        }

        .line {
          width: 13px;
          height: 433px;
          margin: 0 281px;
          background: url("@/assets/image/line.png") no-repeat;
          background-size: contain;
        }
      }
    }

    img {
      transform: rotate(180deg);
      height: 66px;
      margin-top: 38px;
    }

    .right-img {
      transform: rotate(0deg); 
      position: absolute;
      right: 0;
      top: 225px;
      height: 130px;
      width: 1290.23px;
    }
  }
}</style>
