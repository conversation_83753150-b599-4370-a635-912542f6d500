import HttpClient from '@/utils/http-client';
import Vue from 'vue';
const vm = new Vue();
const http$ = new HttpClient(vm.$scmp_conf.baseUrl);

export default {
  // 获取用户信息
  getUserInfo() {
    return http$.request({
      url: '/common/getUserInfo',
      method: 'post',
      // baseURL: '/bog-factory-cms' // mock情况可以层叠
    });
  },
  // 获取导航菜单
  getMenuData() {
    return http$.request({
      url: '/common/navigation',
      method: 'get',
    });
  },
};
