<template>
  <div class="pie3d" style="width: 100%; height: 100%">
    <div ref="chart" style="width: 100%; height: 100%"></div>
    <div class="pie3d-container">
      <div class="percentage" v-if="percentage">{{ percentage }}%</div>
      <div class="title-text">{{ title }}</div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "Pie3DChart",
  props: {
    pieData: {
      type: Array,
      required: true,
    },
    internalDiameterRatio: {
      type: Number,
      default: 0.8,
    },
    gridConfig: {
      type: Object,
      default: () => ({
        boxHeight: 12,
        boxWidth: 150,
        boxDepth: 150,
        top: '-15%'
      })
    },
    showDivider: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null,
      option: null,
      selectedIndex: "",
      hoveredIndex: "",
      title: "",
      percentage: "",
    };
  },
  watch: {
    pieData: {
      handler(newData) {
        if (this.chart) {
          // 设置动画时长
          const option = this.getPie3D(newData, this.internalDiameterRatio);
          this.option = option;
          
          // 使用动画过渡
          this.chart.setOption(option, {
            transition: ['all'],
            duration: 1000,  // 动画持续时间，单位ms
            animationEasing: 'cubicInOut'  // 动画曲线
          });
        }
      },
      deep: true
    }
  },
  mounted() {
    console.log('mounted - pieData:', this.pieData);
    this.initChart();
    // 添加默认显示第一个数据的提示
    this.$nextTick(() => {
      if (this.pieData && this.pieData.length > 0) {
        const firstItem = this.pieData[1];
        this.title = firstItem.name;
        const total = this.pieData.reduce((sum, item) => sum + item.value, 0);
        this.percentage = ((firstItem.value / total) * 100).toFixed(1);
        
        // 模拟鼠标悬停效果
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        });
      }
    });
  },
  methods: {
    initChart() {
      if (this.chart) {
        this.chart.dispose();
      }
      this.chart = echarts.init(this.$refs.chart);
      this.option = this.getPie3D(this.pieData, this.internalDiameterRatio);
      this.chart.setOption(this.option);
      this.addEventListeners();
    },
    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
      let midRatio = (startRatio + endRatio) / 2;
      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;

      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }

      k = typeof k !== "undefined" ? k : 1 / 3;
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
      let hoverRate = isHovered ? 1.05 : 1;

      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },
        x: function (u, v) {
          if (u < startRadian) {
            return (
              offsetX +
              Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        y: function (u, v) {
          if (u < startRadian) {
            return (
              offsetY +
              Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1;
          }
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
        },
      };
    },

    getPie3D(pieData, internalDiameterRatio) {
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let legendData = [];
      let k = internalDiameterRatio
        ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
        : 1 / 3;

      console.log('getPie3D - 开始处理数据');
      console.log('输入数据:', pieData);
      
      pieData.forEach(function (data, i) {
        sumValue += data.value;
        let seriesItem = {
          name: data.name || `series${i}`,
          type: "surface",
          parametric: true,
          wireframe: { show: false },
          pieData: data,
          pieStatus: {
            selected: false,
            hovered: false,
            k: k,
          },
          itemStyle: data.itemStyle || {},
        };
        series.push(seriesItem);
      });

      console.log('数据总和:', sumValue);
      
      // 防止除以零的情况，当总和为0时，所有值都为0
      if (sumValue === 0) {
        const equalRatio = 1 / pieData.length;
        series.forEach(function (item, index) {
          const startRatio = index * equalRatio;
          const endRatio = (index + 1) * equalRatio;
          item.pieData.startRatio = startRatio;
          item.pieData.endRatio = endRatio;
          item.parametricEquation = this.getParametricEquation(
            startRatio,
            endRatio,
            false,
            false,
            k,
            1 // 当总和为0时，使用1作为值，以显示均等饼图
          );
          legendData.push(item.name);
        }, this);
      } else {
        series.forEach(function (item) {
          endValue = startValue + item.pieData.value;
          item.pieData.startRatio = startValue / sumValue;
          item.pieData.endRatio = endValue / sumValue;
          item.parametricEquation = this.getParametricEquation(
            item.pieData.startRatio,
            item.pieData.endRatio,
            false,
            false,
            k,
            item.pieData.value
          );
          startValue = endValue;
          legendData.push(item.name);
        }, this);
      }

      // series.push({
      //   name: "mouseoutSeries",
      //   type: "surface",
      //   parametric: true,
      //   wireframe: { show: false },
      //   itemStyle: { opacity: 0 },
      //   parametricEquation: {
      //     u: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
      //     v: { min: 0, max: Math.PI, step: Math.PI / 20 },
      //     x: function (u, v) {
      //       return Math.sin(v) * Math.sin(u) + Math.sin(u);
      //     },
      //     y: function (u, v) {
      //       return Math.sin(v) * Math.cos(u) + Math.cos(u);
      //     },
      //     z: function (u, v) {
      //       return Math.cos(v) > 0 ? 0.1 : -0.1;
      //     },
      //   },
      // });

      return {
        legend: {
          data: legendData,
          bottom: '0',
          left: "center",
          type: "plain",
          itemGap: 30,
          itemWidth: 22,
          itemHeight: 22,
          icon: 'rect',
          show: true,
          textStyle: {
            color: '#fff',
            fontSize: 22,
            lineHeight: 22,
            padding: [2, 8, 0, 0],
            verticalAlign: 'middle'
          }
        },

        tooltip: {
          showContent: false,  // 隐藏默认的提示框
          formatter: (params) => {
            if (params.seriesIndex === undefined || !this.option || !this.option.series) {
              return '';
            }
            
            const series = this.option.series[params.seriesIndex];
            if (!series || !series.pieData) {
              return '';
            }
            
            // 检查总和是否为0
            const sumValue = this.pieData.reduce((sum, item) => sum + item.value, 0);
            const percent = sumValue === 0 ? '0' : ((series.pieData.endRatio - series.pieData.startRatio) * 100).toFixed(1);
            this.title = series.name;
            this.percentage = percent;
            return `${series.name}: ${percent}%`;
          },
          textStyle: {
            fontSize: 32,
            fontWeight: "bold",
          },
        },
        xAxis3D: { min: -1, max: 1 },
        yAxis3D: { min: -1, max: 1 },
        zAxis3D: { min: -1, max: 1 },
        grid3D: {
          show: false,
          boxHeight: 10,  // 减小高度
          boxWidth: 120,  // 减小宽度
          boxDepth: 120,  // 减小深度
          top: '-15%',   // 向上移动
          viewControl: {
            alpha: 35,  // 调整视角
            rotateSensitivity: 0,
            zoomSensitivity: 0,
            panSensitivity: 0,
            autoRotate: false,
          },
          postEffect: {
            enable: true,
            bloom: { enable: true, bloomIntensity: 0.1 },
            SSAO: { enable: true, quality: "medium", radius: 2 },
          },
        },
        series: series,
        animation: true,
        animationThreshold: 1000,
        animationDuration: 1000,
        animationEasing: 'cubicInOut',
        animationDelay: function (idx) {
          return idx * 100;
        },
        animationDurationUpdate: 1000,
        animationEasingUpdate: 'cubicInOut',
        animationDelayUpdate: function (idx) {
          return idx * 100;
        }
      };
    },

    addEventListeners() {
      this.chart.on("mouseover", (params) => {
        console.log('mouseover event triggered');
        if (params.seriesIndex === undefined || !this.option || !this.option.series) {
          return;
        }
        
        const series = this.option.series[params.seriesIndex];
        if (!series || !series.pieData) {
          return;
        }
        
        // 检查总和是否为0
        const sumValue = this.pieData.reduce((sum, item) => sum + item.value, 0);
        const percent = sumValue === 0 ? '0' : ((series.pieData.endRatio - series.pieData.startRatio) * 100).toFixed(1);
        this.title = series.name;
        this.percentage = percent;
        this.hoveredIndex = params.seriesIndex;
      });

      // 添加鼠标移出事件监听
      this.chart.on("mouseout", () => {
        console.log('mouseout event triggered');
        this.title = "";
        this.percentage = "";
        this.hoveredIndex = "";
        console.log('after mouseout - title:', this.title);
        console.log('after mouseout - percentage:', this.percentage);
      });

      // 点击事件也需要更新文字
      this.chart.on("click", (params) => {
        console.log('click event triggered');
        console.log('click params:', params);
        let isSelected = !this.option.series[params.seriesIndex].pieStatus.selected;
        let isHovered = this.option.series[params.seriesIndex].pieStatus.hovered;
        let k = this.option.series[params.seriesIndex].pieStatus.k;
        let startRatio = this.option.series[params.seriesIndex].pieData.startRatio;
        let endRatio = this.option.series[params.seriesIndex].pieData.endRatio;

        if (this.selectedIndex !== "" && this.selectedIndex !== params.seriesIndex) {
          this.option.series[this.selectedIndex].parametricEquation = this.getParametricEquation(
            this.option.series[this.selectedIndex].pieData.startRatio,
            this.option.series[this.selectedIndex].pieData.endRatio,
            false,
            false,
            k,
            this.option.series[this.selectedIndex].pieData.value
          );
          this.option.series[this.selectedIndex].pieStatus.selected = false;
        }

        this.option.series[params.seriesIndex].parametricEquation = this.getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          this.option.series[params.seriesIndex].pieData.value
        );
        this.option.series[params.seriesIndex].pieStatus.selected = isSelected;
        this.selectedIndex = isSelected ? params.seriesIndex : "";
        
        // 更新文字和百分比
        const percent = ((endRatio - startRatio) * 100).toFixed(1);
        this.$nextTick(() => {
          this.title = params.seriesName;
          this.percentage = percent;
        });
        
        this.chart.setOption(this.option);
      });

      this.chart.on("globalout", () => {
        this.$nextTick(() => {
          this.title = "";
          this.percentage = "";
        });
        if (this.hoveredIndex !== "") {
          let hovered = this.option.series[this.hoveredIndex];
          hovered.parametricEquation = this.getParametricEquation(
            hovered.pieData.startRatio,
            hovered.pieData.endRatio,
            hovered.pieStatus.selected,
            false,
            hovered.pieStatus.k,
            hovered.pieData.value
          );
          hovered.pieStatus.hovered = false;
          this.hoveredIndex = "";
        }
        this.chart.setOption(this.option);
      });
    },
  },
  destroyed() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
};
</script>

<style scoped lang="less">
.pie3d {
  position: relative;
  .pie3d-container {
    position: absolute;
    left: 50%;
    top: calc(50% - 90px);
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    
    .percentage {
      font-size: 56px;
      color: #fff;
      font-family: DIN-MediumItalic, DIN-MediumItalic;
      text-shadow: 0px 0px 10px rgba(41, 241, 250, 0.71);
      font-weight: bold;
    }
    
    .title-text {
      font-size: 28px;
      color: #fff;
      text-align: center;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      line-height: 1.2;
      text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
      white-space: nowrap;
      padding: 0 20px;
    }
  }
}
</style>
