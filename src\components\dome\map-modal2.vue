<template>
  <el-dialog
    :visible="dialogVisible"
    width="500px"
    :show-close="false"
    :before-close="handleClose"
    :append-to-body="true" 
    class="map-modal"
    :modal="false"
  >
    <div class="modal-content" @mouseleave="handleClose" @click="handleClose">
      <div class="title">
        <img src="@/assets/image/left_2x.png" class="title-left-img" alt="" />
        <span class="title-text">{{ districtName }}</span>
        <img src="@/assets/image/right_2x.png" class="title-right-img" alt="" />
      </div>
      <div class="content-box">
        <!-- 联络站数量 -->
        <div class="station-count">
          <div class="section-title">
            <img src="@/assets/image/region-left-botton-2.png" class="title-bg" />
            <span class="text">{{ districtName }}统计概览</span>
          </div>
          <!-- 在这里添加图片上的内容 -->
          <div class="data-overview">
            <div class="data-item">
              <div class="label">市人大代表人数</div>
              <div class="value">{{ modalForm.districtNum }}<span class="unit">人</span></div>
              <img src="@/assets/image/ghq.png" class="icon" alt="" />
            </div>
            <div class="data-item">
              <div class="label">代表人数全市占比</div>
              <div class="value">{{ modalForm.districtRatio }}<span class="unit"></span></div>
              <img src="@/assets/image/ghq.png" class="icon" alt="" />
            </div>
          </div>
        </div>

        <!-- 五级代表驻站分布情况 -->
        <div class="rep-distribution">
          <div class="section-title">
            <img src="@/assets/image/region-left-botton-2.png" class="title-bg"  />
            <span class="text">五级代表驻站分布情况</span>
          </div>
          <div class="distribution-list">
            <div class="list-item">
              <span class="item-name">全国人大代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.country }}<span class="unit">人</span></span>
            </div>
            <div class="list-item">
              <span class="item-name">省级人大代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.province }}<span class="unit">人</span></span>
            </div>
            <div class="list-item">
              <span class="item-name">市级人大代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.city }}<span class="unit">人</span></span>
            </div>
            <div class="list-item">
              <span class="item-name">区级人大代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.administrativeArea }}<span class="unit">人</span></span>
            </div>
            <div class="list-item">
              <span class="item-name">镇级人大代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.streetTown }}<span class="unit">人</span></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "mapModal",
  props: {
    modalForm: {
      type: Object,
      default: () => null,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    districtName: {
      type: String,
      default: () => null,
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      normalStationList: [
        { name: '中心联络站', value: 6 },
        { name: '片区联络站', value: 14 },
        { name: '其中：优秀联络站', value: 2 }
      ],
      iconStationList: [
        { name: '广州市花都区太平镇中心联络站' },
        { name: '广州市花都区吕田镇中心联络站' }
      ],
      representativeList: [
        { name: '全国代表', value: 1 },
        { name: '省级代表', value: 46 },
        { name: '市级代表', value: 46 },
        { name: '区级代表', value: 46 },
        { name: '镇级代表', value: 46 }
      ]
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    },
  },
  methods: {
    handleClose() {
      this.$emit("update:visible", false);
    },
    modalOk() {
      this.$emit("update:visible", false);
      this.$emit("confirm", this.modalForm);
    },
  },
};
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/fonts/YouSheBiaoTiHei.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.map-modal {
  ::v-deep .el-dialog {
    background: transparent;
    border-radius: 8px;
    width: 310px !important;
    
    .el-dialog__header {
      display: none;
    }
    
    .el-dialog__body {
      background: rgba(3, 26, 68, 0.9);
      padding: 0;
      position: relative;
      
      .modal-content {
        padding: 20px;
        
        .title {
          position: relative;
          color: #fff;
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 20px;
          margin-top: 0px;
          padding: 5px 0;
          letter-spacing: 8px;
          text-shadow: 0 0 10px rgba(41, 241, 250, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          
          .title-left-img,
          .title-right-img {
            width: 18px;
            height: 18px;
          }
          
          .title-text {
            margin: 0 20px;
            line-height: 1;
          }
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            height: 80%;
            background: linear-gradient(90deg, 
              rgba(41, 241, 250, 0) 0%,
              rgba(41, 241, 250, 0.1) 50%,
              rgba(41, 241, 250, 0) 100%
            );
            filter: blur(5px);
            z-index: -1;
          }
        }

        .content-box {
          .section-title {
            position: relative;
            height: 50px;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-left: 55px;

            .title-bg {
              position: absolute;
              width: 100%;
              height: 100%;
              left: 0;
              top: 0;
            }

            .text {
              color: #fff;
              font-size: 14px;
              font-style: italic;
              position: relative;
              z-index: 1;
              line-height: 50px;
              margin-top: 9px;
              // 字间距为1px
              letter-spacing: 1px;

            }
          }

          .station-count {
            position: relative;
            margin-bottom: 5px;
            
            .data-overview {
              padding: 0px 15px;
              display: flex;
              justify-content: space-between;
              
              .data-item {
                width: 48%;
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
                position: relative;
                padding: 15px 0 0 0;
              
                
                &::before,
                &::after {
                  content: '';
                  position: absolute;
                  left: 0;
                  right: 0;
                  height: 1px;
                  background: linear-gradient(
                    to right,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(140, 200, 244, 0.8) 50%,
                    rgba(255, 255, 255, 0) 100%
                  );
                }
                
                &::before {
                  top: 0;
                }
                
                &::after {
                  bottom: 0;
                }
                
                .label {
                  font-family: YouSheBiaoTiHei, "YouSheBiaoTiHei";
                  font-size: 16px;
                  color: #fff;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                  margin-bottom: 10px;
                  white-space: nowrap;
                  font-weight: normal;
                  letter-spacing: 1px;
                  position: relative;
                  z-index: 2;
                }
                
                .value {
                  font-family: DIN-MediumItalic, DIN-MediumItalic;
                  font-style: italic;
                  font-size: 16px;
                  color: #40F3FB;
                  white-space: nowrap;
                  margin-bottom: 0;
                  text-shadow: 0px 0px 7px rgba(160, 243, 230, 0.71);
                  position: relative;
                  z-index: 2;
                  
                  .unit {
                    font-style: italic;
                    font-size: 16px;
                    color: #40F3FB;
                    margin-left: 1px;
                    font-family: DIN-MediumItalic, DIN-MediumItalic;
                    text-shadow: 0px 0px 7px rgba(160, 243, 230, 0.71);
                  }
                }
                
                .icon {
                  width: 100%;
                  height: auto;
                  margin-top: -30%;
                  position: relative;
                  z-index: 1;
                }
              }
            }
            
            .table-wrapper {
              padding: 0 5px;
              
              .table-con {
                position: relative;
                display: flex;
                align-items: center;
                height: 25px;
                margin-bottom: 8px;
              }

              .table-item {
                background: transparent;
                margin: 5px 0;
                padding: 1px 1px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: relative;
                border-left: 2px solid #1b4d71;
                border-bottom: 1px solid rgba(113, 161, 255, 0.11);

                .td2 {
                  color: #8cc8f4;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                  font-size: 14px;
                  letter-spacing: 2px;
                  padding-left: 15px;
                  flex: 1;
                }

                .td3 {
                  color: #fff;
                  font-size: 16px;
                  font-weight: bold;
                  width: 80px;
                  text-align: right;
                  padding-right: 10px;

                  .unit {
                    color: #2c7bb3;
                    font-size: 14px;
                    text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                    margin-left: 4px;
                  }
                }
              }
            }

            .icon-list {
              padding: 0 8px;

              .icon-item {
                background: transparent;
                margin: 15px 0;
                padding: 1px 1px;
                display: flex;
                align-items: center;
                position: relative;
                height: 35px;


                .house-icon {
                  position: absolute;
                  left: -18px;
                  top: -7px;
                  width: 55px;
                  height: 45px;
                }

                .td2 {
                  border-top: 1px solid rgba(113, 161, 255, 0.11);
                  border-bottom: 1px solid rgba(113, 161, 255, 0.11);
                  color: #fff;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                  font-size: 13px;
                  letter-spacing: 1px;
                  margin-left: 40px;
                  padding:10px 0;
                  flex: 1;
                }
              }
            }
          }

          .distribution-list {
            padding: 0 5px;
            
            .list-item {
              position: relative;
              display: flex;
              align-items: center;
              height: 25px;
              margin-bottom: 8px;
              background: transparent;
              border-left: 2px solid #1b4d71;
              border-bottom: 1px solid rgba(113, 161, 255, 0.11);
              margin: 5px 0;
              padding: 1px 1px;
              
              .item-name {
                flex: 1;
                padding-left: 15px;
                color: #8cc8f4;
                text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                font-size: 14px;
                letter-spacing: 2px;
              }
              
              .item-value {
                width: 80px;
                text-align: right;
                padding-right: 10px;
                color: #fff;
                font-size: 16px;
                font-weight: bold;
                
                .unit {
                  color: #2c7bb3;
                  font-size: 14px;
                  margin-left: 4px;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                }
              }
            }
          }
        }

        .close-btn {
          display: none;
        }
      }
    }
  }
}
</style>
