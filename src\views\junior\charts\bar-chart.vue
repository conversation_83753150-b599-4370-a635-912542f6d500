<template>
  <div class="bar-chart-wrapper">
    <div ref="chart" class="chart" @mousewheel="handleMouseWheel"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "barChart",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    xAxis: {
      type: Array,
      default: () => [],
    },
    // 滚动速度配置项
    scrollStep: {
      type: Number,
      default: 1
    },
    // 是否显示数值标签
    showLabels: {
      type: Boolean,
      default: true
    },

  },
  data() {
    return {
      chartInstance: null,
      currentStart: 0,
      currentEnd: 11,
      // 是否正在滚动标志，用于防止滚动过快
      isScrolling: false,
      // 滚动方向指示
      scrollDirection: {
        left: false,
        right: false
      },
      currentLabels: []
    };
  },
  mounted() {
    if (this.data) {
      this.initChart();
    }
    
    // 添加键盘事件监听
    window.addEventListener('keydown', this.handleKeyDown);
    window.addEventListener('keyup', this.handleKeyUp);
  },
  beforeDestroy() {
    // 移除键盘事件监听
    window.removeEventListener('keydown', this.handleKeyDown);
    window.removeEventListener('keyup', this.handleKeyUp);
    
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
  },
  watch: {
    data: {
      handler(val) {
        this.initChart();
      },
      deep: true,
    },
  },
  methods: {
    // 格式化数字显示，处理大数字显示
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
      }
      return num.toString();
    },
    
    initChart() {
      // 如果已有实例，先销毁
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
      
      // 创建新的图表实例
      this.chartInstance = this.$echarts.init(this.$refs.chart);
      
      // 确定初始显示范围
      let endPercent = 100;
      if (this.xAxis && this.xAxis.length > 11) {
        endPercent = Math.round(10 / this.xAxis.length * 100);
      }
      
      // 定义3D柱状图形状
      this.defineCubeShapes();
      
      // 设置图表配置
      const option = {
        title: {
          show: false  // 禁用标题，确保顶部不显示任何文字
        },
        grid: { 
          left: 0, 
          right: 0, 
          bottom: "5%", 
          top: "5%", 
          containLabel: true 
        },
        tooltip: { 
          show: true, 
          trigger: "axis", 
          axisPointer: { 
            type: "shadow" 
          }, 
          textStyle: { 
            fontSize: 32, 
            // fontWeight: "bold" 
          } 
        },
        legend: { 
          show: false 
        },
        xAxis: { 
          type: "category", 
          data: this.xAxis, 
          axisLabel: { 
            color: "white", 
            fontSize: 32, 
            interval: 0 ,
            margin: 20,
            // 文字竖列展示
            formatter: function (value) {
              return value.split('').join('\n');
            }
          }, 
          axisLine: { 
            show: false 
          }, 
          axisTick: false 
        },
        yAxis: { 
          line: { show: false }, 
          min: 0, 
          type: "value", 
          axisLine: { show: false }, 
          axisLabel: { show: false }, 
          splitLine: { show: false },
          minorTick: { show: false },
          minorSplitLine: { show: false },
          max: function(value) {
            // 根据当前数据最大值动态调整y轴最大值
            const maxValue = Math.max(...this.data);
            return maxValue * 1.2; // 增加20%的余量
          }.bind(this)
        },
        series: [
          // 背景柱状图
          { 
            type: "custom", 
            renderItem: this.renderBackground, 
            data: Array(this.data.length).fill(100),
            tooltip: { show: false } 
          },
          // 数据柱状图
          { 
            name: "统计数据", 
            type: "custom", 
            renderItem: this.renderBar, 
            data: this.data,
            label: {
              show: false, // 使用false替代未定义的showTopLabels，以确保不显示ECharts默认的标签
              position: 'top',
              distance: 10,
              formatter: (params) => {
                return this.formatNumber(params.value);
              },
              textStyle: {
                color: '#ffffff',
                fontSize: 36,
                fontWeight: 'bold'
              }
            },
            // 添加动画效果
            animationDelay: (idx) => {
              return idx * 150; // 每个柱子延迟150毫秒显示
            },
            animationDuration: 1000
          }
        ],
        dataZoom: [
          { 
            type: 'inside', 
            start: 0, 
            end: endPercent,
            zoomLock: true,     // 锁定缩放，只允许平移
            zoomOnMouseWheel: false,  // 禁用默认的鼠标滚轮缩放
            moveOnMouseWheel: false,  // 禁用默认的鼠标滚轮平移，我们自己处理
            throttle: 0  // 减少延迟，提高响应速度
          },
          // 添加滑动条控制器
          {
            type: 'slider',
            show: false,        // 隐藏滑动条，但保留功能
            start: 0,
            end: endPercent,
            height: 5,
            bottom: 5,
            zoomLock: true
          }
        ],
        
        // 设置整体动画
        animation: true,
        animationThreshold: 1000,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        animationDurationUpdate: 300
      };
      
      // 设置选项
      this.chartInstance.setOption(option);
      
      // 初始化数据范围
      this.currentStart = 0;
      this.currentEnd = Math.min(11, this.data.length);
      
      // 初始化标签
      this.updateLabels();
      
      // 监听 dataZoom 事件，更新标签显示
      this.chartInstance.on('dataZoom', this.handleDataZoom);
      
      // 监听点击事件
      this.chartInstance.on('click', (params) => {
        // 获取当前点击的数据索引
        const dataIndex = params.dataIndex;
        // 获取对应的streetTownId
        const streetTownId = this.data[dataIndex]?.streetTownId;
        
        this.$emit('chart-click', {
          name: params.name,
          value: params.value,
          streetTownId: streetTownId
        });
      });
    },
    
    // 添加更新标签方法
    updateLabels() {
      if (this.xAxis && this.xAxis.length) {
        this.currentLabels = this.xAxis.slice(this.currentStart, this.currentEnd);
      }
    },
    
    // 自定义鼠标滚轮事件处理
    handleMouseWheel(event) {
      event.preventDefault();  // 防止页面滚动
      
      // 如果正在滚动中，防止频繁触发
      if (this.isScrolling) return;
      
      this.isScrolling = true;
      setTimeout(() => {
        this.isScrolling = false;
      }, 50); // 滚动间隔，调整灵敏度
      
      // 根据滚轮方向决定移动方向
      const direction = event.deltaY > 0 ? 1 : -1;
      this.scrollChart(direction * this.scrollStep);
    },
    
    // 处理键盘按下事件
    handleKeyDown(event) {
      if (event.key === 'ArrowLeft') {
        this.scrollDirection.left = true;
        if (!this.isScrolling) {
          this.startContinuousScroll();
        }
      } else if (event.key === 'ArrowRight') {
        this.scrollDirection.right = true;
        if (!this.isScrolling) {
          this.startContinuousScroll();
        }
      }
    },
    
    // 处理键盘释放事件
    handleKeyUp(event) {
      if (event.key === 'ArrowLeft') {
        this.scrollDirection.left = false;
      } else if (event.key === 'ArrowRight') {
        this.scrollDirection.right = false;
      }
    },
    
    // 持续滚动
    startContinuousScroll() {
      if (this.isScrolling) return;
      
      const scroll = () => {
        if (!this.scrollDirection.left && !this.scrollDirection.right) {
          this.isScrolling = false;
          return;
        }
        
        let direction = 0;
        if (this.scrollDirection.left) direction -= 1;
        if (this.scrollDirection.right) direction += 1;
        
        this.scrollChart(direction * this.scrollStep);
        requestAnimationFrame(scroll);
      };
      
      this.isScrolling = true;
      requestAnimationFrame(scroll);
    },
    
    // 移动图表
    scrollChart(step) {
      if (!this.chartInstance) return;
      
      const option = this.chartInstance.getOption();
      if (!option.dataZoom || !option.dataZoom.length) return;
      
      const dataZoom = option.dataZoom[0];
      const range = dataZoom.end - dataZoom.start;
      
      // 计算新的起止范围
      let newStart = dataZoom.start + step / this.xAxis.length * 100;
      let newEnd = dataZoom.end + step / this.xAxis.length * 100;
      
      // 处理边界情况
      if (newStart < 0) {
        newStart = 0;
        newEnd = range;
      }
      
      if (newEnd > 100) {
        newEnd = 100;
        newStart = 100 - range;
      }
      
      // 应用新范围
      this.chartInstance.dispatchAction({
        type: 'dataZoom',
        dataZoomIndex: 0,
        start: newStart,
        end: newEnd
      });
    },
    
    // 定义3D柱状图形状
    defineCubeShapes() {
      const CubeBack = echarts.graphic.extendShape({
        shape: { x: 0, y: 0 },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c0 = [shape.x + 15, shape.y - 7];
          const c1 = [shape.x - 15, shape.y - 7];
          const c4 = [shape.x, shape.y - 12];
          const c2 = [xAxisPoint[0] - 15, xAxisPoint[1] - 6];
          const c5 = [xAxisPoint[0], xAxisPoint[1]];
          const c3 = [xAxisPoint[0] + 15, xAxisPoint[1] - 6];
          ctx.moveTo(c0[0], c0[1]).lineTo(c4[0], c4[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c5[0], c5[1]).lineTo(c3[0], c3[1]).closePath();
        },
      });
      
      const CubeLeft = echarts.graphic.extendShape({
        shape: { x: 0, y: 0 },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c0 = [shape.x, shape.y];
          const c1 = [shape.x - 15, shape.y - 7];
          const c2 = [xAxisPoint[0] - 15, xAxisPoint[1] - 6];
          const c3 = [xAxisPoint[0], xAxisPoint[1]];
          ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath();
        },
      });
      
      const CubeRight = echarts.graphic.extendShape({
        shape: { x: 0, y: 0 },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c1 = [shape.x, shape.y];
          const c2 = [xAxisPoint[0], xAxisPoint[1]];
          const c3 = [xAxisPoint[0] + 15, xAxisPoint[1] - 6];
          const c4 = [shape.x + 15, shape.y - 7];
          ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath();
        },
      });
      
      const CubeTop = echarts.graphic.extendShape({
        shape: { x: 0, y: 0 },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x, shape.y];
          const c2 = [shape.x + 15, shape.y - 7];
          const c3 = [shape.x, shape.y - 12];
          const c4 = [shape.x - 15, shape.y - 7];
          ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath();
        },
      });
      
      // 注册形状
      echarts.graphic.registerShape("CubeBack", CubeBack);
      echarts.graphic.registerShape("CubeLeft", CubeLeft);
      echarts.graphic.registerShape("CubeRight", CubeRight);
      echarts.graphic.registerShape("CubeTop", CubeTop);
    },
    
    // 渲染背景柱状图
    renderBackground(params, api) {
      const location = api.coord([api.value(0), api.value(1)]);
      return {
        type: "group",
        children: [{
          type: "CubeBack",
          shape: {
            api,
            xValue: api.value(0),
            yValue: api.value(1),
            x: location[0],
            y: location[1],
            xAxisPoint: api.coord([api.value(0), 0])
          },
          style: {
            fill: "rgba(16, 55, 77,1)"
          }
        }]
      };
    },
    
    // 渲染数据柱状图
    renderBar(params, api) {
      const location = api.coord([api.value(0), api.value(1)]);
      
      // 检查当前数据点是否在可视范围内
      const dataIndex = params.dataIndex;
      const isVisible = dataIndex >= this.currentStart && dataIndex < this.currentEnd;
      
      // 如果不在可视范围内，返回空组，不渲染
      if (!isVisible) {
        return {
          type: "group",
          children: []
        };
      }
      
      // 获取数值并格式化
      const value = api.value(1);
      const formattedValue = this.formatNumber(value);
      
      // 构建柱状图元素
      const barElements = [
        // 左侧面
        {
          type: "CubeLeft",
          shape: {
            api,
            xValue: api.value(0),
            yValue: api.value(1),
            x: location[0],
            y: location[1],
            xAxisPoint: api.coord([api.value(0), 0])
          },
          style: {
            fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(45,204,137,1)" },
              { offset: 0.4, color: "rgba(45,204,137,0.9)" },
              { offset: 0.6, color: "rgba(45,204,137,1)" },
              { offset: 1, color: "rgba(45,204,137,1)" }
            ])
          }
        },
        // 右侧面
        {
          type: "CubeRight",
          shape: {
            api,
            xValue: api.value(0),
            yValue: api.value(1),
            x: location[0],
            y: location[1],
            xAxisPoint: api.coord([api.value(0), 0])
          },
          style: {
            fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(36,164,122,1)" },
              { offset: 1, color: "rgba(36,164,122,1)" }
            ])
          }
        },
        // 顶部面
        {
          type: "CubeTop",
          shape: {
            api,
            xValue: api.value(0),
            yValue: api.value(1),
            x: location[0],
            y: location[1],
            xAxisPoint: api.coord([api.value(0), 0])
          },
          style: {
            fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(54,240,151,1)" },
              { offset: 1, color: "rgba(54,240,151,1)" }
            ])
          }
        }
      ];
      
      // 如果需要显示标签，添加数值标签元素，增强样式使其更清晰
      if (this.showLabels && value > 0) {
        barElements.push({
          type: 'text',
          style: {
            text: formattedValue,
            textAlign: 'center',
            textVerticalAlign: 'bottom',
            x: location[0],
            y: location[1] - 22, // 位置上移一些，离柱子顶部更远
            fill: '#FFFFFF', // 更亮的白色
            fontSize: 34, // 增大字体大小，可根据需求调整
            fontWeight: 'bold',
            fontFamily: 'Microsoft YaHei',
            textShadow: '0 0 8px rgba(0,0,0,0.8), 0 0 15px rgba(0, 0, 0, 0.7)', // 增强黑色阴影使文字更清晰
            backgroundColor: 'rgba(0, 24, 47, 0.7)', // 增加背景色透明度，使其更明显
            padding: [4, 8], // 增加内边距
            borderRadius: 4
          }
        });
      }
      
      return {
        type: "group",
        children: barElements
      };
    },
    
    // 处理dataZoom事件
    handleDataZoom(params) {
      if (!this.chartInstance) return;
      
      const option = this.chartInstance.getOption();
      if (!option.dataZoom || !option.dataZoom.length) return;
      
      // 获取当前缩放范围
      const dz = option.dataZoom[0];
      const start = Math.round((dz.start / 100) * this.xAxis.length);
      let end = Math.round((dz.end / 100) * this.xAxis.length);
      
      // 限制最多显示11个数据
      if (end - start > 11) {
        end = start + 11;
      }
      
      // 更新当前可视数据范围
      this.currentStart = start;
      this.currentEnd = end;
      
      // 更新标签
      this.updateLabels();
      
      // 强制重新渲染图表
      this.chartInstance.setOption({
        series: [{}, {}] // 保持系列不变，但触发重新渲染
      });
      
      // 显示提示框
      this.$nextTick(() => {
        if (this.chartInstance) {
          this.chartInstance.dispatchAction({
            type: 'showTip',
            seriesIndex: 1,
            dataIndex: start
          });
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.bar-chart-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;  /* 添加相对定位，使子元素能够绝对定位 */
}
.chart {
  width: 100%;
  height: 92%;  /* 稍微减少高度，为底部标签留出空间 */
  flex: 1;
  cursor: grab;   /* 添加抓手光标，提示可以拖动 */
}
.chart:active {
  cursor: grabbing;  /* 拖动时的光标 */
}

/* 自定义X轴标签样式 */
.x-axis-labels {
  position: absolute;
  bottom: 5px;
  left: 0;
  right: 0;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5%;
  z-index: 10;
}

.x-label {
  position: absolute;
  color: #42DDFF;
  font-size: 16px;
  font-weight: bold;
  font-family: 'Microsoft YaHei';
  text-shadow: 0 0 8px rgba(66,221,255,0.8), 0 0 15px rgba(0,200,255,0.5);
  text-align: center;
  white-space: nowrap;
  line-height: 1.2;
  background-color: rgba(0, 24, 47, 0.7);
  padding: 2px 8px;
  border-radius: 4px;
  z-index: 10;
  letter-spacing: 1px;
}
</style>