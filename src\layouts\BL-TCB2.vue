<template>
  <el-container class="main_container">
    <!-- 新增loading遮罩层 -->
    <div class="loading-mask" v-if="loading">
      <div class="loading-spinner">
        <!-- <i class="el-icon-circle-loading"></i> -->
        <span>Loading...</span>
      </div>
    </div>
    <el-header height="175px">
      <ScmpHeader> </ScmpHeader>
    </el-header>
    <el-main>
      <div class="main_main">
        <router-view :key="key" />
        <el-footer height="78px"></el-footer>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import { mapGetters, mapActions, mapState } from "vuex";
export default {
  name: "BLTCB",
  components: {
    ScmpHeader: () => import("@/comps/scmp-header"),
  },
  data() {
    return {
      activeIdx: "",
      resizeTimer: null,  // 新增防抖定时器
      loading: true      // 新增加载状态
    };
  },

  computed: {
    ...mapState("auth/menu", {
      menuArray: "menu",
    }),
    ...mapGetters("auth/user", [
      "userNameGetter",
      "userLoginNameGetter",
      "userMobilePhoneGetter",
      "userPhotoGetter",
    ]),
    key() {
      return new Date().getTime();
    },
  },

  mounted() {
    this.loading = true; // 初始化显示加载
    this.resize();
    window.onresize = () => {
      this.loading = true;  // 窗口变化时显示加载
      clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.resize();
      }, 1000);
    };
  },
  methods: {
    ...mapActions("auth/user", {
      actLogout: "ACT_Logout",
    }),
    async resize() {
      this.loading = true;
      await this.$nextTick();

      return new Promise(resolve => {
        requestAnimationFrame(() => {
          const mainContainer = document.querySelector('.main_container');
          const mainMain = document.querySelector('.main_main');
          const allContentHeights = Array.from(mainContainer?.children || []).map(el => el.offsetHeight);
          const maxContentHeight = Math.max(0, ...allContentHeights);
          const docHeight = Math.max(
            document.documentElement.scrollHeight,
            document.documentElement.offsetHeight,
            document.documentElement.clientHeight,
            maxContentHeight + 175 + 78
          );

          const cliWidth = document.documentElement.clientWidth || document.body.clientWidth;
          const cliHeight = document.documentElement.clientHeight || document.body.clientHeight;
          const contW = 3840;
          const contH = docHeight;
          const w = cliWidth / contW;
          const h = cliHeight / contH;
          const appDom = document.querySelector("#app");
          if (appDom) {
            appDom.style.transform = `scale(${w},${h})`;
            appDom.style.transformOrigin = "top left";
            appDom.style.width = contW + "px";
          }
          resolve();
        });
      }).finally(() => {
        this.loading = false;
      });
    }
  },
};
</script>

<style lang="less" scoped>
.main_container {
  display: flex;
  flex-direction: column;

  flex: 1;
  // background: rgb(6, 16, 33);
  background: linear-gradient(180deg,
      #030814 0%,
      rgba(0, 89, 255, 0.26) 100%,
    );

  border-radius: 0px 0px 0px 0px;

  // .main_backboard {
  // }
  .el-header {
    padding: 0;
  }

  .el-main {
    margin-top: -62px;
    padding: 0 16px 16px;
    background: linear-gradient(360deg,

        rgba(1, 25, 66, 0.81) 0%,
        rgba(0, 0, 0, 0.64) 100%);
  }

  .main_main {
    padding: 22px 24px 0px;
    background: url("@/assets/bg_6border.png") no-repeat;
    background-size: 100% 100%;

    .el-footer {
      background: url("@/assets/footer_bottom.png") no-repeat;
      background-size: 100% 100%;
    }
  }
}

/* 新增loading样式 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 1); // 白色背景
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .loading-spinner {
    font-size: 2rem !important;
    color: #0f99ed;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>