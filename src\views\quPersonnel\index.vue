<template>
  <div class="motion-page">
    <div class="motion-main_left">
      <div class="tabs-box">
        <div class="tabs-item" @click="handleClick(index,item.categoryName)" :class="{ active: activeId == index }"
          v-for="(item, index) in appointAndDismissList" :key="index">
          <div class="name">{{ item.categoryName }} </div>
          <div class="num">
            <div class="ren">任</div>
            <div class="ren-num">{{ item.appointCount }}</div>
            <div class="unit">人</div>
            <div class="mian">免</div>
            <div class="mian-num">{{ item.removalCount }}</div>
            <div class="unit">人</div>
          </div>
        </div>
      </div>
    </div>
    <div class="motion-main_center">
      <div class="center-top">
        <ScmpSpot :spotName="areaName" dropDownTitle="切换专题" :column="spotDownColumn" :dropDownList="dropDownList"
          @checkedItem="checkedItem">
        </ScmpSpot>
      </div>
      <div class="center-content">
        <div class="content-title">{{ categoryName }}</div>
        <div class="content-con">
          <div class="radio-box">
            <div class="radio-item" @click="radioChange(1)" :class="{ active: radioId == 1 }">
              任职
            </div>
            <div class="radio-item" @click="radioChange(2)" :class="{ active: radioId == 2 }">
              免职
            </div>
          </div>
          <div class="con-box">
            <div class="con-item" v-for="(item, index) in personnelDetail" :key="index">
              <div class="con-title">
                {{ item.title }}
              </div>
              <div class="row">
                <div class="row-item" v-for="(detailItem, detailIndex) in item.detail" :key="detailIndex">
                  <div class="img-box"></div>
                  <div class="text-box">
                    <div class="row-name">{{ detailItem.name }}</div>
                    <div class="row-text">
                      {{ detailItem.post }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getPersonnelDetail,
  getPersonnelQuDetail,
  getpersonnelStatistics,
  getpersonnelQuStatistics
} from "@/api/dataPageApi/personnel";
export default {
  name: "motion-index",
  components: {
    ScmpSpot: () => import("@/comps/scmp-spot"),
  },
  data() {
    return {
      areaId: '',
      yearValue1: String(new Date().getFullYear()),
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [{ name: "菜单项1" }, { name: "菜单项2" }],
      activeId: 0,
      radioId: 1,
      appointAndDismissList: [],
      categoryName: '',
      personnelDetail: [],
      areaName: ''
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    this.areaId = this.$route.query.administrativeAreaId;
    this.areaName = this.$route.query.areaName + '人事任免工作视窗 ';
    this.getpersonnelStatistics(this.yearValue1, '', 0)
  },
  mounted() { },
  watch: {},
  methods: {
    getPersonnelQuDetail(id,areaId) {
      const params = {
        appointType: this.radioId,
        categoryId: id,
        areaId: areaId
      }
      getPersonnelQuDetail(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.personnelDetail = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getpersonnelStatistics(data, id, index) {
      console.log(this.areaId,"-------------------------")
      const params = {
        cockpitQuery: '',
        year: data,
        areaId: this.areaId
      }
      getpersonnelQuStatistics(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.appointAndDismissList = res.data.items
          console.log(this.appointAndDismissList)
          if (index == 0) {
            // 初始化数据 默认展示第一条的数据以及标题
            this.categoryName = res.data.items[0].categoryName
            this.categoryId = res.data.items[0].categoryId
            console.log(this.areaId,"-----------------11111----------")
            this.getPersonnelQuDetail(res.data.items[0].categoryId,this.areaId)
          } else {
            this.getPersonnelQuDetail(id)
          }
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    checkedItem(obj) {
      console.log("选中", obj);
    },

    handleClick(index,name) {
      this.activeId = index;
      this.categoryId = this.appointAndDismissList[index].categoryId
      this.categoryName = name
      this.getPersonnelQuDetail(this.categoryId,this.areaId)
    },
    radioChange(val) {
      this.radioId = val;
      this.getPersonnelQuDetail(this.categoryId,this.areaId)

    },
  },
};
</script>

<style lang="less" scoped>
.motion-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .motion-main_left {
    width: 750px;
    height: 1800px;
    background: url("@/assets/image/personnel-left-bg.png") no-repeat;
    background-size: 100% 100%;
    background-position-x: 350px;
    position: relative;

    .tabs-box {
      width: 545px;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding-top: 160px;
      box-sizing: border-box;

      .tabs-item {
        padding: 0 20px;
        box-sizing: border-box;
        width: 545px;
        height: 158px;
        background: url("@/assets/image/personnel-left-item.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;

        .name {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 36px;
          color: #ffffff;
          line-height: 53px;
          padding-left: 20px;
        }

        .num {
          margin-top: 20px;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font-family: DIN, DIN;
          font-weight: bold;
          font-size: 30px;
          color: #ffffff;
          line-height: 41px;

          .ren {
            position: relative;
            margin-right: 10px;

            &::after {
              content: "";
              position: absolute;
              left: -15px;
              top: 50%;
              transform: translateY(-50%);
              width: 60px;
              height: 20%;
              background: linear-gradient(90deg,
                  transparent,
                  #ceb830,
                  transparent);
              filter: blur(4px);
              opacity: 0.8;
            }
          }

          .ren-num {
            margin-right: 10px;
            font-family: DIN-BlackItalic, DIN-BlackItalic;
            font-weight: 400;
            font-size: 35px;
            background: linear-gradient(to bottom, #ffffff 0%, #00d46a 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            line-height: 41px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }

          .unit {
            margin-right: 20px;
          }

          .mian {
            position: relative;
            margin-right: 10px;

            &::after {
              content: "";
              position: absolute;
              left: -15px;
              top: 50%;
              transform: translateY(-50%);
              width: 60px;
              height: 20%;
              background: linear-gradient(90deg,
                  transparent,
                  #66afed,
                  transparent);
              filter: blur(4px);
              opacity: 0.8;
            }
          }

          .mian-num {
            margin-right: 10px;
            font-family: DIN-BlackItalic, DIN-BlackItalic;
            font-weight: 400;
            font-size: 35px;
            background: linear-gradient(to bottom, #ffffff 0%, #17a4f3 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            line-height: 41px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      .active {
        background: url("@/assets/image/personnel-left-item-active.png") no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .motion-main_center {
    width: calc(100% - 750px);
    position: relative;

    .center-top {
      display: flex;
      flex-direction: row;
      justify-content: center;
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      line-height: 25px;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      position: absolute;
      left: 456px;

      .customTab {
        margin-top: 20px;
        padding: 40px 103px 60px 111px;
        position: absolute;
      }

      .customTab1 {
        background: url("@/assets/image/customTab1.png") no-repeat;
        background-size: contain;
        left: 45px;
      }

      .customTab2 {
        background: url("@/assets/image/customTab2.png") no-repeat;
        background-size: contain;
        left: 45px;
      }

      .customTabact1 {
        background: url("@/assets/image/customTabact1.png") no-repeat;
        background-size: contain;
        right: 45px;
      }

      .customTabact2 {
        background: url("@/assets/image/customTabact2.png") no-repeat;
        background-size: contain;
        right: 45px;
      }
    }

    .center-content {
      position: absolute;
      top: 160px;
      width: 100%;
      height: calc(100% - 190px);
      background: url("@/assets/image/personnel-right-bg.png") no-repeat;
      background-size: 100% 100%;

      .content-title {
        width: 754.34px;
        height: 225px;
        background: url("@/assets/image/personnel-tit-bg.png") no-repeat;
        background-size: 100% 100%;
        font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 38px;
        font-style: italic;
        color: #f6f9fe;
        line-height: 38px;
        text-shadow: 0px 0px 7px rgba(75, 180, 229, 0.25),
          0px 2px 8px rgba(5, 28, 55, 0.42);
        padding: 82px 0 0 165px;
        box-sizing: border-box;
      }

      .content-con {
        padding-left: 7%;
        box-sizing: border-box;

        .radio-box {
          display: flex;
          align-items: center;

          .radio-item {
            width: 247px;
            height: 52px;
            padding: 10px 20px;
            background: url("@/assets/image/perform-left-img2.png") no-repeat;
            background-size: 100% 100%;
            margin-right: 30px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 30px;
            color: #ffffff;
            line-height: 35px;
            text-align: center;
            cursor: pointer;
          }

          .active {
            background: url("@/assets/image/perform-left-img1.png") no-repeat;
            background-size: 100% 100%;
          }
        }

        .con-box {
          overflow-y: auto;
          height: 1250px;
          overflow-x: hidden;
    
          /* Webkit浏览器滚动条样式 */
          &::-webkit-scrollbar {
            height: 8px; // 滚动条高度
            background: rgba(0, 25, 63, 0.2); // 轨道背景
          }
          
          &::-webkit-scrollbar-thumb {
            background: linear-gradient(
              90deg,
              rgba(0, 25, 63, 0) 0%,
              rgba(10, 108, 222, 0.8) 50%,
              rgba(0, 25, 63, 0) 100%
            );
            border-radius: 4px;
            border: 1px solid rgba(10, 108, 222, 0.5); // 添加边框增强科技感
            /*box-shadow: 0 0 5px rgba(78, 153, 248, 0.5); // 发光效果*/
          }
          
          &::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(
              90deg,
              rgba(0, 25, 63, 0) 0%,
              rgba(10, 150, 255, 0.9) 50%,
              rgba(0, 25, 63, 0) 100%
            );
          }

          .con-item {
            margin-bottom: 80px;

            .con-title {
              margin-bottom: 20px;
              /*width: 1253px;*/
              width: 96%;
              height: 120px;
              line-height: 120px;
              padding-left: 60px;
              padding-top: 20px;
              background: url("@/assets/image/personnel-con-title.png") no-repeat;
              background-size: 100% 100%;
              font-family: PingFang SC, PingFang SC;
              font-weight: 600;
              font-size: 30px;
              font-style: italic;
              color: #ffffff;
              letter-spacing: 3px;
              text-shadow: 0px 2px 3px rgba(51, 158, 188, 0.79);
              text-align: center;
            }

            .row {
              display: flex;
              align-items: center;
              justify-content: space-between;
              overflow-x: auto;
              overflow-y: hidden;
              width: 100%;
              padding-bottom: 15px; // 为滚动条留出空间
    
              /* Webkit浏览器滚动条样式 */
              &::-webkit-scrollbar {
                height: 8px; // 滚动条高度
                background: rgba(0, 25, 63, 0.2); // 轨道背景
              }
              
              &::-webkit-scrollbar-thumb {
                background: linear-gradient(
                  90deg,
                  rgba(0, 25, 63, 0) 0%,
                  rgba(10, 108, 222, 0.8) 50%,
                  rgba(0, 25, 63, 0) 100%
                );
                border-radius: 4px;
                border: 1px solid rgba(10, 108, 222, 0.5); // 添加边框增强科技感
                /*box-shadow: 0 0 5px rgba(78, 153, 248, 0.5); // 发光效果*/
              }
              
              &::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(
                  90deg,
                  rgba(0, 25, 63, 0) 0%,
                  rgba(10, 150, 255, 0.9) 50%,
                  rgba(0, 25, 63, 0) 100%
                );
              }
              /* 隐藏外部溢出 */
              .row-item {
                height: 191px;
                width: 30%;
                min-width: calc(33% - 20px);
                background: linear-gradient(90deg,
                    rgba(0, 25, 63, 0) 0%,
                    rgba(10, 108, 222, 0.47) 53%,
                    rgba(0, 25, 63, 0) 100%);
                display: flex;
                align-items: center;

                .img-box {
                  width: 240px;
                  height: 130px;
                  background: url("@/assets/image/sfz.png") no-repeat;
                  background-size: auto 100%;
                  background-position: left center;
                }

                .text-box {

                  box-sizing: border-box;
                  padding: 0 80px 0 30px;

                  .row-name {
                    width: max-content;
                    // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
                    font-weight: bold;
                    font-size: 40px;
                    color:  #ceb830;
                    line-height: 47px;
                    text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                    position: relative;
                    font-style: italic;
                    &::before {
                      content: "";
                      position: absolute;
                      left: -25%;
                      top: 50%;
                      transform: translateY(-50%);
                      width: 150%;
                      height: 90%;
                      // background: linear-gradient(90deg,
                      //     transparent,
                      //     #ceb830,
                      //     transparent);
                      filter: blur(4px);
                      opacity: 0.4;
                    }
                  }

                  .row-text {
                    margin-top: 20px;
                    // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
                    font-weight: bold;
                    font-size: 32px;
                    color: #ffffff;
                    line-height: 38px;
                    text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                    text-align: left;
                    font-style: italic;
                    text-transform: none;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.motion-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
  /* 设置透明度 */
}</style>
