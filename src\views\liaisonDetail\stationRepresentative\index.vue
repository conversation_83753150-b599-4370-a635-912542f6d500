<template>
  <div class="basicInformation-page">
    <div class="basicInformation-main_list" v-if="sfDm1.length > 0">
      <div class="item" @click="jump(item)" v-for="(item, index) in sfDm1" :key="index">
        <img :src="baseURL + '/dbxx' + item.photoUrl" alt="" />
        <div class="item-right">
          <div class="first-child">{{ item.dbSf }}</div>
          <div>姓名：{{ item.userName }}</div>
          <div>单位及职务：{{ item.workUnit }}-{{ item.unty }}</div>
          <div>本年已进社区：<span>{{ item.activityCount }}</span> 次</div>
        </div>
      </div>
    </div>
    <div class="basicInformation-main_list" v-if="sfDm2.length > 0">
      <div class="item" @click="jump(item)" v-for="(item, index) in sfDm2" :key="index">
        <img :src="baseURL + '/dbxx' + item.photoUrl" alt="" />
        <div class="item-right">
          <div class="first-child">{{ item.dbSf }}</div>
          <div>姓名：{{ item.userName }}</div>
          <div>单位及职务：{{ item.workUnit }}-{{ item.unty }}</div>
          <div>本年已进社区：<span>{{ item.activityCount }}</span> 次</div>
        </div>
      </div>
    </div>
    <div class="basicInformation-main_list" v-if="sfDm3.length > 0">
      <div class="item" @click="jump(item)" v-for="(item, index) in sfDm3" :key="index">
        <img :src="baseURL + '/dbxx' + item.photoUrl" alt="" />
        <div class="item-right">
          <div class="first-child">{{ item.dbSf }}</div>
          <div>姓名：{{ item.userName }}</div>
          <div>单位及职务：{{ item.workUnit }}-{{ item.unty }}</div>
          <div>本年已进社区：<span>{{ item.activityCount }}</span> 次</div>
        </div>
      </div>
    </div>
    <div class="basicInformation-main_list" v-if="sfDm4.length > 0">
      <div class="item" @click="jump(item)" v-for="(item, index) in sfDm4" :key="index">
        <img :src="baseURL + '/dbxx' + item.photoUrl" alt="" />
        <div class="item-right">
          <div class="first-child">{{ item.dbSf }}</div>
          <div>姓名：{{ item.userName }}</div>
          <div>单位及职务：{{ item.workUnit }}-{{ item.unty }}</div>
          <div>本年已进社区：<span>{{ item.activityCount }}</span> 次</div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import { mapGetters } from "vuex";
import {
  getLiaisonStationDb
} from "@/api/dataPageApi/liaisonStation";
import global_ from "../../../global.vue";
export default {
  name: "basicInformation-index",
  components: {},
  data() {
    return {
      baseURL: global_.basePath_1,
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [{ name: "菜单项1" }, { name: "菜单项2" }],
      // dbList: [],
      sfDm1: [],
      sfDm2: [],
      sfDm3: [],
      sfDm4: []
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.streetTownId = this.$route.query.streetTownId;
    this.liaisonStationId = this.$route.query.liaisonStationId;
    this.getLiaisonStationDb(this.administrativeAreaId, this.streetTownId, this.liaisonStationId)
  },
  mounted() { },
  watch: {},
  methods: {
    getLiaisonStationDb(administrativeAreaId, streetTownId, liaisonStationId) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        // pageNum: 1,
        // pageSize: 10,
        // liaisonStationId: '6714ad0097ab492e9ecec088bba205c8'
        liaisonStationId: liaisonStationId,
        administrativeAreaId: administrativeAreaId,
        streetTownId: streetTownId,
      }
      getLiaisonStationDb(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // this.dbList = res.rows
          res.rows.forEach((item) => {
            const sfDmValues = item.sfDm.split(",").map(Number); // 将 sfDm 转换为数字数组
            const minSfDm = Math.min(...sfDmValues); // 取最小值
            // 根据最小值分配到对应的组
            if (minSfDm === 1) {
              this.sfDm1.push(item);
            } else if (minSfDm === 2) {
              this.sfDm2.push(item);
            } else if (minSfDm === 3) {
              this.sfDm3.push(item);
            } else if (minSfDm === 4) {
              this.sfDm4.push(item);
            }
          });
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    checkedItem(obj) {
      console.log("选中", obj);
    },
    jump(item) {
      this.$router.push({
        path: "/liaisonDetail/stationDetails",
        query: {
          item: JSON.stringify(item),
        },
      });
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
.basicInformation-page {
  width: calc(100%);
  // height: calc(100vh - 500px);
  height: 105%;
  display: flex;
  flex-direction: column;
  // justify-content: space-between;
  // padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;
  z-index: 9;
  overflow-y: auto;
  overflow-x: hidden;
  /* Webkit浏览器滚动条样式 */
  &::-webkit-scrollbar {
    height: 8px; // 滚动条高度
    background: rgba(0, 25, 63, 0.2); // 轨道背景
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg,
        rgba(0, 25, 63, 0) 0%,
        rgba(10, 108, 222, 0.8) 50%,
        rgba(0, 25, 63, 0) 100%);
    border-radius: 4px;
    border: 1px solid rgba(10, 108, 222, 0.5); // 添加边框增强科技感
    /*box-shadow: 0 0 5px rgba(78, 153, 248, 0.5); // 发光效果*/
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg,
        rgba(0, 25, 63, 0) 0%,
        rgba(10, 150, 255, 0.9) 50%,
        rgba(0, 25, 63, 0) 100%);
  }


  .basicInformation-main_list {
    // width: calc(100% - 130px);
    width: calc(100% - 70px);
    // margin-top: 80px;
    // margin-bottom: 25px;
    position: relative;
    display: flex;
    flex-direction: row;
    margin-top: 52px;
    padding: 0px 75px;
    cursor: pointer;

    .item {
      width: 1039.05px;
      height: auto;
      padding: 25px 0 18px 123px;
      background: url("@/assets/image/llz_jcxx.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;

      img {
        width: 246px;
        height: 295px;
        border-radius: 0px 0px 0px 0px;
      }
    }

    .item-right {
      display: flex;
      flex-direction: column;
      margin-left: 49px;

      .first-child {
        font-family: PangMenZhengDao, PangMenZhengDao;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 40px;
        color: #ffffff;
        letter-spacing: 7px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 48px;
      }

      div {
        font-family: PangMenZhengDao, PangMenZhengDao;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;

        font-weight: 400;
        font-size: 40px;
        color: #ffffff;
        line-height: 64px;
        letter-spacing: 7px;
        text-align: left;
        font-style: normal;
        text-transform: none;

        span {
          font-family: PangMenZhengDao, PangMenZhengDao;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;

          font-weight: 400;
          font-size: 40px;
          color: rgba(0, 255, 203, 1);
          letter-spacing: 7px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}

.basicInformation-main_list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.item {
  width: calc(33.33% - 20px);
  /* 每行三个，减去间距 */
  margin-right: 20px;
  margin-bottom: 20px;
}

.item:nth-child(3n) {
  margin-right: 0;
  /* 每行第三个去掉右边距 */
}
</style>
