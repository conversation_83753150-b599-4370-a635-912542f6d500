import Vue from "vue";
import axios from "axios";
import store from "@/stores";
import {
  invalidCode,
  messageDuration,
  noPermissionCode,
  requestTimeout,
  successCode,
  tokenName,
  contentType,
} from "@/config/settings";
const instance = axios.create({
  baseURL: Vue.prototype.GLOBAL.basePath_1,
  timeout: requestTimeout,
  headers: {
    "Content-type": "application/json;charset=UTF-8",
    flag: false,
  },
});
import { getAccessToken } from "@/utils/accessToken";
instance.interceptors.request.use((config) => {
  if (store.getters.accessToken) {
    // let noTokenUrl = [
    //   "/group/getSiteList",
    // ];
    if (config.url && config.url.indexOf("/group/getSiteList") > -1) {
      config.headers.token = "";
      config.headers["menuId"] =store.getters.navigation.menuData.menuId;
      config.headers["systemId"]=store.getters.navigation.menuData.systemId;
      config.headers["hidden"]=store.getters.navigation.menuData.hidden;
    } else {
      config.headers.token = store.getters.accessToken;
      config.headers["flag"] = false;
      if (getAccessToken()) {
        config.headers.token = getAccessToken();
        config.headers["menuId"] =store.getters.navigation.menuData.menuId;
        config.headers["systemId"]=store.getters.navigation.menuData.systemId;
        config.headers["hidden"]=store.getters.navigation.menuData.hidden;
      }
    }
  }

  return config;
});
instance.interceptors.response.use((response) => {
  const { code, message } = response.data;
  if (code == 401 || code == 402) {
    // token 过期
    window.top.postMessage(
      {
        event: "logout", // 约定的消息事件
        args: {}, // 参数
      },
      "*"
    );
  } else {
    return response;
  }
});
export const openInstance = axios.create({
  baseURL: Vue.prototype.GLOBAL.basePath_1,
  timeout: requestTimeout,
  headers: {
    "Content-type": "application/json;charset=UTF-8",
    flag: false,
  },
});
export const instance_1 = instance;
//无token
export const instance_3 = axios.create({
  baseURL: Vue.prototype.GLOBAL.basePath_1,
  timeout: requestTimeout,
  headers: {
    "Content-type": "application/json;charset=UTF-8",
    flag: false,
  },
});
instance_3.interceptors.request.use((config) => {
        config.headers["menuId"] =store.getters.navigation.menuData.menuId;
        config.headers["systemId"]=store.getters.navigation.menuData.systemId;
        config.headers["hidden"]=store.getters.navigation.menuData.hidden;

  return config;
});
instance_3.interceptors.response.use((response) => {
  const { code, message } = response.data;
  if (code == 401 || code == 402) {
    // token 过期
    window.top.postMessage(
      {
        event: "logout", // 约定的消息事件
        args: {}, // 参数
      },
      "*"
    );
  } else {
    return response;
  }
});
//token 写死
export const instance_4 = axios.create({
  baseURL: Vue.prototype.GLOBAL.basePath_1,
  timeout: requestTimeout,
  headers: {
    token: store.getters.accessToken,
    "Content-type": "application/json;charset=UTF-8",
    flag: false,
  },
});
instance_4.interceptors.response.use((response) => {
  const { code, message } = response.data;
  if (code == 401 || code == 402) {
    // token 过期
    window.top.postMessage(
      {
        event: "logout", // 约定的消息事件
        args: {}, // 参数
      },
      "*"
    );
  } else {
    return response;
  }
});
// export const instance_2 = axios.create({
//   baseURL: Vue.prototype.GLOBAL.basePath_2,
//   timeout: requestTimeout,
//   headers: {
//     token: store.getters.accessToken,
//     "Content-type": "application/json;charset=UTF-8",
//   },
// });
export const instance_2 = instance;

// ——————————————————————————————————————
//  这里多配置了一个基础请求   13楼
const instance13 = axios.create({
  baseURL: Vue.prototype.GLOBAL.basePath_3,
  timeout: requestTimeout,
  headers: {
    "content-type": "application/json;charset=UTF-8",
    flag: false,
  },
});
const instance14 = axios.create({
  baseURL: Vue.prototype.GLOBAL.basePath_3,
  timeout: requestTimeout,
  headers: {
    "content-type": "application/json;charset=UTF-8",
    flag: false,
  },
});
instance13.interceptors.request.use((config) => {
  if (store.getters.accessToken) {
    config.headers.token = store.getters.accessToken;
    config.headers["flag"] = false;
    config.headers["menuId"] =store.getters.navigation.menuData.menuId;
    config.headers["systemId"]=store.getters.navigation.menuData.systemId;
    config.headers["hidden"]=store.getters.navigation.menuData.hidden;
  }
  if (getAccessToken()) {
    config.headers.token = getAccessToken();
    config.headers["menuId"] =store.getters.navigation.menuData.menuId;
    config.headers["systemId"]=store.getters.navigation.menuData.systemId;
    config.headers["hidden"]=store.getters.navigation.menuData.hidden;
  }
  return config;
});
instance14.interceptors.request.use((config) => {
  if (store.getters.accessToken) {
    // config.headers.token = store.getters.accessToken;
    config.headers["flag"] = false;
    config.headers["menuId"] =store.getters.navigation.menuData.menuId;
    config.headers["systemId"]=store.getters.navigation.menuData.systemId;
    config.headers["hidden"]=store.getters.navigation.menuData.hidden;
  }
  if (getAccessToken()) {

    config.headers["menuId"] =store.getters.navigation.menuData.menuId;
    config.headers["systemId"]=store.getters.navigation.menuData.systemId;
    config.headers["hidden"]=store.getters.navigation.menuData.hidden;
  }
  return config;
});
instance13.interceptors.response.use((response) => {
  const { code, message } = response.data;
  if (code == 401 || code == 402) {
    // token 过期
    window.top.postMessage(
      {
        event: "logout", // 约定的消息事件
        args: {}, // 参数
      },
      "*"
    );
  } else {
    return response;
  }
});
instance14.interceptors.response.use((response) => {
  const { code, message } = response.data;
  if (code == 401 || code == 402) {
    // token 过期
    window.top.postMessage(
      {
        event: "logout", // 约定的消息事件
        args: {}, // 参数
      },
      "*"
    );
  } else {
    return response;
  }
});
export const instance_yajy = instance13;
export const instance_yajy2 = instance14;
// export default {
//   instance_1,
//   instance_2
// }
