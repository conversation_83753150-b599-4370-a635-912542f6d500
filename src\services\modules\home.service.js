import HttpClient from '@/utils/http-client';
import { config } from '../../configs/index';
const http$ = new HttpClient(config.baseUrl);

export default {
  // 获取数据接入趋势
  getDataAccessTrend(params) {
    return http$.request({
      url: '/common/getDataAccessTrend',
      method: 'get',
      params,
    });
  },
  // 获取部门目录
  getDeptCatalog(params) {
    return http$.request({
      url: '/common/getDeptCatalog',
      method: 'get',
      params,
    });
  },
  // 获取数据直达
  getDirect(params) {
    return http$.request({
      url: '/common/getDirect',
      method: 'get',
      params,
    });
  },
  //  获取热门使用资源
  getPopularResource(params) {
    return http$.request({
      url: '/common/getPopularResource',
      method: 'get',
      params,
    });
  },
  // 获取资源申请动态
  getResourceApply(params) {
    return http$.request({
      url: '/common/getResourceApply',
      method: 'get',
      params,
    });
  },
  // 获取资源类型占比
  getResourceTypeRatio(params) {
    return http$.request({
      url: '/common/getResourceTypeRatio',
      method: 'get',
      params,
    });
  },
  // 获取顶部统计信息
  getTopInfo(params) {
    return http$.request({
      url: '/common/getTopInfo',
      method: 'get',
      params,
    });
  },
  // 获取主题专题库

  getZtZt(params) {
    return http$.request({
      url: '/common/getZtZt',
      method: 'get',
      params,
    });
  },
};
