<template>
  <div class="motion-page">
    <div class="motion-main_left">
      <ScmpCard :cardName="areaName ? '本周扫码反映反映问题情况' : '本周扫码反映问题情况'" backgroundImage="card_bg4">
        <div class="left-box1" slot="main">
          <div class="left-box1-top">
            <AreaChart :data="areaChartData" :xAxis="areaxAxisList" />
          </div>
          <div class="radio-box">
            <div class="radio-item" @click="handleChartTypeChange(1)" :class="{ active: chartType === 1 }">民意解决方式分布
            </div>
            <div class="radio-item" @click="handleChartTypeChange(2)" :class="{ active: chartType === 2 }">民意关键词热度
            </div>
          </div>
          <div class="left-box1-bottom">
            <template v-if="chartType === 1">
              <PieChart1 :pie-data="pieData" :internal-diameter-ratio="0.8" :grid-config="{
                boxHeight: 8,
                boxWidth: 120,
                boxDepth: 120,
                top: '-15%'
              }" :show-divider="true" />
            </template>
            <template v-else>
              <div class="wordCloud-warp">
                <div class="wordCloud-warp_bg"></div>
                <wordCloud class="wordCloud-warp_main" :data="wordCloudData">
                </wordCloud>
              </div>
            </template>
          </div>
        </div>
      </ScmpCard>
    </div>

    <div class="motion-main_center">
      <div class="center-top">
        <ScmpSpot :spotName="areaName ? areaName + '扫码反映问题工作视窗' : '广州市扫码反映问题工作视窗'" dropDownTitle="切换专题"
          :column="spotDownColumn" :dropDownList="dropDownList" @checkedItem="checkedItem">
        </ScmpSpot>
      </div>
      <div class="con-bottom">
        <div class="tongji-box">
          <img
            :src="areaName ? require('../../assets/image/popularWill-right-title2.png') : require('../../assets/image/popularWill-right-title.png')"
            class="tongji-title" alt="" />
          <div class="tongji-item">
            <div class="name">累计收集数</div>
            <div class="num">{{ onlineLiaisonStationHub.collectedOpinionsCount }}</div>
          </div>
          <div class="tongji-item">
            <div class="name">累计办结数</div>
            <div class="num">{{ onlineLiaisonStationHub.resolvedOpinionsCount }}</div>
          </div>
          <!-- <div class="tongji-item item-bg2">
            <div class="name">平均办理周期</div>
            <div class="num">{{ onlineLiaisonStationHub.averageProcessingCycle }} <span>天</span></div>
          </div> -->
          <div class="tongji-item item-bg2">
            <div class="name">办结率</div>
            <div class="num">{{ onlineLiaisonStationHub.resolutionRate }}</div>
          </div>
        </div>
        <div class="card-wrapper">
          <ScmpCard cardName="问题处理情况列表" backgroundImage="card_bg2" :rightPicture="false">
            <div slot="main" class="con-bottom-box">
              <!-- 筛选区域 -->
              <div class="filter-section">
                <div class="filter-item">
                  <span class="filter-label">群众反映主题：</span>
                  <el-input 
                    v-model="searchTitle" 
                    placeholder="请输入群众反映主题" 
                    clearable
                    class="filter-input">
                  </el-input>
                </div>
                <div class="filter-item" v-if="this.administrativeAreaId == null">
                  <span class="filter-label">区域：</span>
                  <el-select 
                    v-model="selectedRegion" 
                    placeholder="请选择区域" 
                    clearable
                    class="filter-select"
                    @change="handleRegionChange">
                    <el-option
                      v-for="item in regionList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
                <div class="filter-buttons">
                  <el-button type="primary" @click="handleSearch" class="search-btn">搜索</el-button>
                  <el-button @click="handleReset" class="reset-btn">重置</el-button>
                </div>
              </div>
              
              <div class="table">
                <ScmpTable :tableData="deptDictList" :tableColumn="deptDictTableColumn" :rowNum="11"
                  :indicesGrouping="true">
                </ScmpTable>
              </div>
              <ScmpPagination :total="deptDictListTotal" :current-page.sync="currentPage" :page-size.sync="pageSize"
                @current-change="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
          </ScmpCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getOnlineLiaisonStationHotKey,
  getOnlineLiaisonStationHub,
  getOnlineLiaisonStationList,
  getOnlineLiaisonStationTransType,
  getOnlineLiaisonStationWeek
} from "@/api/dataPageApi/popularWill";
export default {
  name: "motion-index",
  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    ScmpSpot: () => import("@/comps/scmp-spot"),
    ScmpTable: () => import("@/comps/scmp-tablePopularWill"),
    ScmpPagination: () => import("@/components/pagination"),
    AreaChart: () => import("./charts/area-chart.vue"),
    PieChart1: () => import("@/views/popularWill/charts/pie-chart.vue"),
    wordCloud: () => import("@/components/motion/wordCloud.vue"),
  },
  data() {
    return {
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],
      areaChartData: [],
      areaxAxisList: [],
      chartType: 2, // 1: 解决方式分布, 2: 关键词热度
      pieData: [],
      keywordPieData: [
        // {
        //   name: "环境卫生",
        //   value: 35,
        //   itemStyle: { color: "#3CC3DF" },
        // },
        // {
        //   name: "交通出行",
        //   value: 25,
        //   itemStyle: { color: "#FF928A" },
        // },
        // {
        //   name: "物业管理",
        //   value: 20,
        //   itemStyle: { color: "#8979FF" },
        // },
        // {
        //   name: "社区服务",
        //   value: 20,
        //   itemStyle: { color: "#FFB72A" },
        // },
      ],
      wordCloudData: [
        // { name: "环境卫生(35)", value: 35 },
      ],
      // Table配置
      deptDictList: [
        // {
        //   id: "da874237075a43cd9525d4a62cfb45fb",
        //   issueNum: "202513180",
        //   title: "道路维护",
        //   liaisonReplyComment: '',
        //   dbComment: '',
        //   status: 2,
        //   createTime: "2025-04-22 17:57:50.939000",
        //   statusName: "已转线下办理中"
        // }
        // {
        //   a: "20240101",
        //   q: "空调水外露",
        //   w: "待代表提交意见",
        //   e: "待处理中...",
        //   r: "协调办理中...",
        //   t: "2024-11-15 09:05:34",
        // },
        // {
        //   a: "20240101",
        //   q: "空调水外露",
        //   w: "待代表提交意见",
        //   e: "待处理中...",
        //   r: "协调办理中...",
        //   t: "2024-11-15 09:05:34",
        // },
        // {
        //   a: "20240101",
        //   q: "空调水外露",
        //   w: "待代表提交意见",
        //   e: "待处理中...",
        //   r: "协调办理中...",
        //   t: "2024-11-15 09:05:34",
        // },
        // {
        //   a: "20240101",
        //   q: "空调水外露",
        //   w: "待代表提交意见",
        //   e: "待处理中...",
        //   r: "协调办理中...",
        //   t: "2024-11-15 09:05:34",
        // },
        // {
        //   a: "20240101",
        //   q: "空调水外露",
        //   w: "待代表提交意见",
        //   e: "待处理中...",
        //   r: "协调办理中...",
        //   t: "2024-11-15 09:05:34",
        // },
      ],
      deptDictTableColumn: [
        { prop: "issueNum", label: "编号", align: "center", width: "300" },
        // { prop: "liaisonStationName", label: "联络站名称" , align: "center", width: "500"  },
        { prop: "title", label: "群众反映主题", align: "center", width: "550" },
        // { prop: "dbComment", label: "代表意见" , align: "center" },
        { prop: "liaisonReplyComment", label: "人大代表意见", align: "center", width: "650" },
        { prop: "statusName", label: "状态", align: "center", width: "200" },
        { prop: "createTime", label: "反映时间", align: "center" },
      ],
      currentPage: 1,
      pageSize: 10,
      onlineLiaisonStationHub: [],
      onlineLiaisonStationWeek: [],
      administrativeAreaId: 0,
      deptDictListTotal: 0,
      year: 2025,
      // 筛选相关数据
      searchTitle: '',
      selectedRegion: '',
      regionList: [
        { value: '440104', label: '越秀区' },
        { value: '440103', label: '荔湾区' },
        { value: '440105', label: '海珠区' },
        { value: '440106', label: '天河区' },
        { value: '440111', label: '白云区' },
        { value: '440112', label: '黄埔区' },
        { value: '440113', label: '番禺区' },
        { value: '440114', label: '花都区' },
        { value: '440115', label: '南沙区' },
        { value: '440183', label: '增城区' },
        { value: '440184', label: '从化区' }
      ]
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
    currentPieData() {
      return this.chartType === 1 ? this.pieData : this.keywordPieData;
    }
  },
  created() {
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.areaName = this.$route.query.areaName
    this.year = this.$route.query.year;
    this.getOnlineLiaisonStationTransType(this.year, this.administrativeAreaId)
  },
  mounted() {
    this.getOnlineLiaisonStationHotKey(this.year, this.administrativeAreaId)
    this.getOnlineLiaisonStationHub(this.year, this.administrativeAreaId)
    this.getOnlineLiaisonStationList(this.year, this.administrativeAreaId)
    this.getOnlineLiaisonStationWeek(this.year, this.administrativeAreaId)
  },
  watch: {},
  methods: {
    // 页码改变事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.deptDictList = []
      this.getOnlineLiaisonStationList(this.administrativeAreaId)
    },

    // 每页条数改变事件
    handleSizeChange(val) {
      this.pageSize = val
      this.deptDictList = []
      this.currentPage = 1 // 条数改变时重置到第一页
      this.getOnlineLiaisonStationList(this.administrativeAreaId)
    },
    getOnlineLiaisonStationHotKey(year, data) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        year: this.year,
        administrativeAreaId: data || this.administrativeAreaId,
      }
      getOnlineLiaisonStationHotKey(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // this.wordCloudData = res.data
          this.wordCloudData = res.data.map(item => ({
            value: item.num,  // 将 num 改为 value
            name: `${item.name}(${item.num})`  // 修改name格式
          }));
          console.log(this.wordCloudData)

        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getOnlineLiaisonStationHub(year, data) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        year: this.year,
        administrativeAreaId: data || this.administrativeAreaId,
      }
      getOnlineLiaisonStationHub(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.onlineLiaisonStationHub = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    async getOnlineLiaisonStationList(year, data) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      }
      const data1 = {
        administrativeAreaId: data || this.administrativeAreaId,
        year: this.year,
        // 添加筛选参数
        title: this.searchTitle || undefined,
        region: this.selectedRegion || undefined,
      }
      await getOnlineLiaisonStationList(params, data1)
        .then(res => {
          console.log(res);  // 处理返回的数据
          //对时间格式化只保留年月日时分秒
          res.rows.forEach(item => {
            item.createTime = item.createTime.substring(0, 19);
          });
          this.deptDictList = res.rows
          this.deptDictListTotal = res.total
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getOnlineLiaisonStationTransType(year, data) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        year: this.year,
        administrativeAreaId: data || this.administrativeAreaId,
      }
      getOnlineLiaisonStationTransType(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          const typeMapping = {
            "转代表研究中": {
              name: "现场答复",
              color: "#3CC3DF"
            },
            "已转线下办理中": {
              name: "线下处理",
              color: "#FF928A"
            },
            "已转12345热线办理中": {
              name: "转12345热线",
              color: "#8979FF"
            },
          };
          const desiredOrder = ["现场答复", "线下处理", "转12345热线"];
          const transformedData = res.data.map(item => {
            const mapping = typeMapping[item.transTypeName];
            return {
              name: mapping.name,
              value: parseInt(item.num),
              itemStyle: { color: mapping.color }
            };
          });

          this.pieData = desiredOrder.map(name =>
            transformedData.find(item => item.name === name)
          );
          console.log(this.pieData)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getOnlineLiaisonStationWeek(year, data) {
      const params = {
        year: this.year,
        administrativeAreaId: data || this.administrativeAreaId,
      }
      getOnlineLiaisonStationWeek(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // this.onlineLiaisonStationWeek = res.data
          const sortedData = res.data.sort((a, b) => {
            return new Date(a.dayName) - new Date(b.dayName);
          });

          this.areaxAxisList = sortedData.map(item => item.dayName);
          this.areaChartData = sortedData.map(item => Number(item.num));
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    checkedItem(obj) {
      console.log("选中", obj);
    },
    // handleCurrentChange(val) {
    //   console.log('当前页:', val);
    // },
    // handleSizeChange(val) {
    //   console.log('每页条数:', val);
    // },
    handleChartTypeChange(type) {
      // 如果点击的是当前已选中的选项，则直接返回，避免重复触发
      if (this.chartType === type) {
        return;
      }
      // 否则更新 chartType，并执行对应的方法
      this.chartType = type;
      if (type === 1) {
        this.getOnlineLiaisonStationTransType()
      } else if (type === 2) {
        this.getOnlineLiaisonStationHotKey()
      }
    },
    
    // 筛选相关方法
    handleSearch() {
      this.currentPage = 1;
      this.getOnlineLiaisonStationList(this.year, this.administrativeAreaId);
    },
    
    handleReset() {
      this.searchTitle = '';
      this.selectedRegion = '';
      this.currentPage = 1;
      this.getOnlineLiaisonStationList(this.year, this.administrativeAreaId);
    },
    
    handleRegionChange(value) {
      console.log('选择的区域:', value);
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
::v-deep .scmp-spot {
  width: 1200px !important;
  
  .title {
    left: 45% !important;
    font-size: inherit !important;
    white-space: normal !important;
    word-break: break-word !important;
  }
}
.motion-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;
  padding-bottom: 50px;

  .motion-main_left {
    width: 885px;

    .left-box1 {
      width: 883px;
      height: 1746px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .left-box1-top {
        height: 47%;
        width: 100%;
        box-sizing: border-box;
        padding: 20px 60px;
      }

      .left-box1-bottom {
        height: 47%;
        width: 100%;
        box-sizing: border-box;
        padding: 20px 60px 60px 60px;
        position: relative;

        :deep(.chart) {
          width: 100% !important;
          height: 100% !important;
          position: absolute;
          left: 0;
          top: 0;
        }
      }

      .radio-box {
        height: 6%;
        display: flex;
        align-items: center;
        padding-left: 60px;

        .radio-item {
          width: 247px;
          height: 52px;
          padding: 10px 20px;
          background: url("@/assets/image/perform-left-img2.png") no-repeat;
          background-size: 100% 100%;
          margin-right: 30px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 30px;
          color: #ffffff;
          line-height: 35px;
          text-align: center;
          cursor: pointer;
        }

        .active {
          background: url("@/assets/image/perform-left-img1.png") no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }

  .motion-main_center {
    width: calc(100% - 883px);
    position: relative;
    padding-left: 80px;
    box-sizing: border-box;

    .center-top {
      display: flex;
      flex-direction: row;
      justify-content: center;
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      line-height: 25px;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      position: absolute;
      left: 330px;

      .customTab {
        margin-top: 20px;
        padding: 40px 103px 60px 111px;
        position: absolute;
      }

      .customTab1 {
        background: url("@/assets/image/customTab1.png") no-repeat;
        background-size: contain;
        left: 45px;
      }

      .customTab2 {
        background: url("@/assets/image/customTab2.png") no-repeat;
        background-size: contain;
        left: 45px;
      }

      .customTabact1 {
        background: url("@/assets/image/customTabact1.png") no-repeat;
        background-size: contain;
        right: 45px;
      }

      .customTabact2 {
        background: url("@/assets/image/customTabact2.png") no-repeat;
        background-size: contain;
        right: 45px;
      }
    }

    .con-bottom {
      position: absolute;
      top: 160px;
      width: 100%;
      padding-right: 80px;
      box-sizing: border-box;

      .tongji-box {
        display: flex;
        justify-content: space-between;

        .tongji-title {
          width: 367px;
          height: 188px;
        }

        .tongji-item {
          height: 280px;
          width: 20%;
          background: url("@/assets/image/perform-tongji11.png") no-repeat;
          background-size: 100% 100%;
          position: relative;

          .name {
            position: absolute;
            left: 12%;
            top: 20%;
            // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: bold;
            font-size: 36px;
            color: #ffffff;
            line-height: 48px;
            text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
            -webkit-text-stroke: 0px rgba(255, 255, 255, 0.11);
            text-align: left;
            font-style: italic;
            text-transform: none;
            -webkit-text-stroke: 0px #ffffff;
            -webkit-background-clip: text;
            /* WebKit 浏览器专用属性，使背景剪切至文本 */
            background-clip: text;
            /* 标准属性 */
          }

          .num {
            position: absolute;
            left: 15%;
            top: 40%;
            font-family: DIN-MediumItalic, DIN-MediumItalic;
            font-weight: 400;
            font-size: 50px;
            color: #29f1fa;
            line-height: 59px;
            text-shadow: 0px 0px 7px rgba(255, 179, 179, 0.71);
            -webkit-text-stroke: 0px #ffffff;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #ca320b 100%),
              linear-gradient(90deg, #f6ffff 0%, #29f1fa 100%),
              linear-gradient(270.00000009864743deg,
                rgba(255, 144, 62, 0) 0%,
                #fbe947 56%);
            -webkit-text-stroke: 0px rgba(255, 255, 255, 0.11);
            -webkit-text-stroke: 0px #ffffff;
            -webkit-background-clip: text;
            /* WebKit 浏览器专用属性，使背景剪切至文本 */
            background-clip: text;
            /* 标准属性 */

            span {
              font-family: DIN, DIN;
              font-weight: bold;
              font-size: 30px;
              color: #ffffff;
              line-height: 35px;
            }
          }
        }

        .item-bg2 {
          background: url("@/assets/image/perform-tongji2.png") no-repeat;
          background-size: 100% 100%;
        }
      }

      .card-wrapper {
        position: relative;
      }

      .con-bottom-box {
        height: 1300px;
        padding: 40px 60px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;

        .filter-section {
          position: absolute;
          top: -60px; // 向上偏移到标题位置
          right: 100px; // 调整右边距与表格右对齐
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 40px;
          font-size: 18px;
          z-index: 10;

          .filter-item {
            display: flex;
            align-items: center;
            gap: 15px;

            .filter-label {
              color: #fff;
              font-family: PingFang SC, PingFang SC;
              font-weight: 400;
              font-size: 36px;
              white-space: nowrap;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }

            .filter-input {
              width: 330px;
              
              /deep/ .el-input__inner {
                background: rgba(57, 205, 255, 0.12);
                background-image: none;
                border-radius: 4px;
                border: 1px solid #7edcfb;
                color: #fff;
                box-sizing: border-box;
                display: inline-block;
                height: 52px;
                line-height: 52px;
                outline: 0;
                padding: 0 15px;
                transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 36px;
                font-style: normal;
                text-transform: none;
                box-shadow: none;
                
                &::placeholder {
                  color: rgba(255, 255, 255, 0.6);
                  font-size: 36px;
                }
                
                &:focus {
                  border-color: #7edcfb;
                  box-shadow: none;
                }
              }
              
              /deep/ .el-input__clear {
                color: rgba(255, 255, 255, 0.6);
                box-shadow: none;
                
                &:hover {
                  color: #fff;
                }
              }
            }

            .filter-select {
              width: 330px;
              
              /deep/ .el-input__inner {
                background: rgba(57, 205, 255, 0.12);
                background-image: none;
                border-radius: 4px;
                border: 1px solid #7edcfb;
                color: #fff;
                box-sizing: border-box;
                display: inline-block;
                height: 52px;
                line-height: 52px;
                outline: 0;
                padding: 0 15px;
                transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 36px;
                font-style: normal;
                text-transform: none;
                box-shadow: none;
                
                &::placeholder {
                  color: rgba(255, 255, 255, 0.6);
                  font-size: 36px;
                }
                
                &:focus {
                  border-color: #7edcfb;
                  box-shadow: none;
                }
              }
              
              /deep/ .el-input__suffix {
                color: rgba(255, 255, 255, 0.6);
              }
              
              /deep/ .el-input__clear {
                color: rgba(255, 255, 255, 0.6);
                box-shadow: none;
                
                &:hover {
                  color: #fff;
                }
              }
            }
          }

          .filter-buttons {
            display: flex;
            gap: 30px;

            .search-btn {
              background: rgba(10, 255, 255, 0.21);
              border-radius: 2px;
              border: 1px solid #33fefe;
              color: #33fefe;
              padding: 12px 24px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
              font-size: 36px;
              font-style: normal;
              text-transform: none;
              box-shadow: none;
              
              &:hover {
                background: rgba(10, 255, 255, 0.35);
                border-color: #33fefe;
                color: #33fefe;
                box-shadow: none;
              }
            }

            .reset-btn {
              background: rgba(14, 200, 88, 0.22);
              border-radius: 2px;
              border: 1px solid #0ec858;
              color: #0ec858;
              padding: 12px 24px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
              font-size: 36px;
              font-style: normal;
              text-transform: none;
              box-shadow: none;
              
              &:hover {
                background: rgba(14, 200, 88, 0.35);
                border-color: #0ec858;
                color: #0ec858;
                box-shadow: none;
              }
            }
          }
        }

        .table {
          flex: 1;
          height: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: column;

          ::v-deep .el-table {
            flex: 1;
            background-color: transparent;
            height: calc(100% - 100px);

            &::before {
              display: none;
            }

            &.el-table--border {
              border-bottom: 1px solid #ffffff;
            }

            .el-table__header-wrapper {
              th {
                background: #0251b2;
                color: #fff;
                font-family: PingFang SC, PingFang SC;
                font-weight: 500;
                font-size: 26px;
                padding: 0;
                height: 103px;
                line-height: 103px;

                &:first-child {
                  padding-left: 20px;
                }
              }
            }

            .el-table__body-wrapper {
              height: calc(100% - 103px);
              overflow-y: auto;

              &::-webkit-scrollbar {
                width: 10px !important;
                background-color: rgba(0, 25, 63, 0.3);
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(0, 147, 255, 0.8);
                border-radius: 5px;
                border: 2px solid rgba(0, 25, 63, 0.3);
              }

              &::-webkit-scrollbar-track {
                background: rgba(0, 25, 63, 0.3);
                border-radius: 5px;
              }

              &::-webkit-scrollbar-corner {
                background: rgba(0, 25, 63, 0.3);
              }

              .el-table__row {
                background-color: transparent;

                td {
                  background-color: transparent;
                  color: #fff;
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 500;
                  font-size: 26px;
                  padding: 0;
                  height: 103px;
                  line-height: 103px;

                  &:first-child {
                    padding-left: 20px;
                  }
                }
              }
            }
          }
        }

        ::v-deep .scmp-pagination {
          margin: 20px 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .el-pagination__total {
            font-size: 36px;
            color: rgb(55, 147, 175);
            font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
            margin-right: 20px;

            span {
              font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
              font-weight: 300;
            }
          }

          .el-pagination__jump {
            font-size: 36px;
            color: #fff;
            display: flex;
            align-items: center;
            font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
            margin-left: 50px;

            .el-pagination__goto {
              font-size: 36px;
              color: #fff;
              margin-right: 12px;
            }

            .el-input {
              height: 48px;
              margin: 0 12px;
              width: 80px;
              vertical-align: middle;

              .el-input__inner {
                background: rgba(9, 31, 51, 0.8);
                border: 1px solid rgba(0, 147, 255, 0.6);
                color: #fff;
                height: 48px;
                font-size: 36px;
                text-align: center;
                box-shadow: none;
                font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
                font-weight: 500;
                padding: 0 15px;
              }
            }
          }

          button {
            background: rgba(9, 31, 51, 0.8);
            border: 1px solid rgba(0, 147, 255, 0.95);
            color: #fff;
            font-size: 28px;

            &:hover {
              color: #fff;
              background: rgba(9, 31, 51, 0.8);
            }
          }

          .btn-prev,
          .btn-next {
            height: 48px;
            width: 48px;
            background: rgba(9, 31, 51, 0.8);
            border: 1px solid rgba(0, 147, 255, 0.6);
            margin: 0 12px;
            box-shadow: none;

            .el-icon {
              font-size: 30px;
              color: #fff;
            }

            &:disabled {
              background: rgba(9, 31, 51, 0.4);

              .el-icon {
                color: rgba(255, 255, 255, 0.4);
              }
            }
          }

          .el-pager {
            display: flex;
            align-items: center;
            background: transparent;

            li {
              font-size: 36px;
              height: 48px;
              min-width: 48px;
              line-height: 48px;
              background: rgba(9, 31, 51, 0.8);
              border: 1px solid rgba(0, 147, 255, 0.95);
              border-bottom-width: 2px;
              color: #fff;
              margin: 0 12px;
              padding: 0 15px;
              font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
              font-weight: 500;
              box-shadow: none;

              &:hover {
                color: #fff;
              }

              &.active {
                background: linear-gradient(91deg, rgb(58, 205, 250) 0%, rgb(58, 205, 250) 49%, rgb(58, 205, 250) 100%);
                color: #fff;
                border: 1px solid rgb(58, 205, 250);
                border-bottom-width: 2px;
                box-shadow: none;
                text-shadow: none;
              }

              &.more {
                font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
                font-weight: 500;

                &:hover {
                  color: #fff;
                }
              }

              &.btn-quicknext,
              &.btn-quickprev {
                position: relative;
                font-size: 22px;
                height: 48px;
                line-height: 48px;
                background: rgba(9, 31, 51, 0.8);
                border: 1px solid rgba(0, 147, 255, 0.95);
                border-bottom-width: 2px;
                color: #fff;
                margin: 0 12px;
                padding: 0 15px;
                box-shadow: none;
                font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
                font-weight: 500;
                cursor: default;
                transition: all 0.3s;
                overflow: hidden;

                &:hover {
                  color: #fff;
                  background: rgba(9, 31, 51, 0.8);
                  border-color: rgba(0, 147, 255, 0.95);
                }

                &::before {
                  content: "...";
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  line-height: 48px;
                  text-align: center;
                }
              }
            }
          }
        }
      }
    }
  }
}

.wordCloud-warp {
  position: relative;
  height: 542px;
  margin-top: 90px;

  .wordCloud-warp_bg {
    position: absolute;
    width: 114%;
    margin-left: -62px;
    top: 78px;
    height: 400px;
    background: url("@/assets/image/wordCloud-warp_bg.png") no-repeat;
    background-size: 100% auto;
  }

  .wordCloud-warp_main {
    width: 100%;
    height: 100%;
  }
}

// 全局样式，去除下拉框阴影
/deep/ .el-select-dropdown {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: none;
  
  .el-select-dropdown__item {
    color: #fff;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
    
    &.selected {
      background: #409eff;
      color: #fff;
    }
  }
}

.motion-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
  /* 设置透明度 */
}
</style>
