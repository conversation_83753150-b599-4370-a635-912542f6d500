<template>
  <div ref="chart" class="chart"></div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "barC<PERSON>",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    xAxis: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    // 初始化视图
    if (this.data) {
      this.getIint(this.data);
    }
  },
  watch: {
    data: {
      handler(val) {
        this.getIint(val);
      },
      deep: true,
    },
  },
  methods: {
    getIint() {
      let myChart = this.$echarts.init(this.$refs.chart);
      const CubeBack = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c0 = [shape.x + 15, shape.y - 7]; //右上
          const c1 = [shape.x - 15, shape.y - 7]; //左上
          const c4 = [shape.x, shape.y - 12]; //上中点
          const c2 = [xAxisPoint[0] - 15, xAxisPoint[1] - 6]; //左下
          const c5 = [xAxisPoint[0], xAxisPoint[1]]; //下中点
          const c3 = [xAxisPoint[0] + 15, xAxisPoint[1] - 6]; //右下
          ctx
            .moveTo(c0[0], c0[1])
            .lineTo(c4[0], c4[1])
            .lineTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c5[0], c5[1])
            .lineTo(c3[0], c3[1])
            .closePath();
        },
      });
      const CubeLeft = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c0 = [shape.x, shape.y];
          const c1 = [shape.x - 15, shape.y - 7]; //左上角
          const c2 = [xAxisPoint[0] - 15, xAxisPoint[1] - 6]; //右上角
          const c3 = [xAxisPoint[0], xAxisPoint[1]];
          ctx
            .moveTo(c0[0], c0[1])
            .lineTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .closePath();
        },
      });
      const CubeRight = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c1 = [shape.x, shape.y];
          const c2 = [xAxisPoint[0], xAxisPoint[1]];
          const c3 = [xAxisPoint[0] + 15, xAxisPoint[1] - 6]; //右下
          const c4 = [shape.x + 15, shape.y - 7];
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });
      const CubeTop = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x, shape.y];
          const c2 = [shape.x + 15, shape.y - 7]; //右下  右
          const c3 = [shape.x, shape.y - 12]; //右上  上
          const c4 = [shape.x - 15, shape.y - 7];
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });
      echarts.graphic.registerShape("CubeBack", CubeBack);
      echarts.graphic.registerShape("CubeLeft", CubeLeft);
      echarts.graphic.registerShape("CubeRight", CubeRight);
      echarts.graphic.registerShape("CubeTop", CubeTop);
      var option = {
        grid: {
          left: 0,
          right: 0,
          bottom: "10%",
          top: "5%",
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          textStyle: {
            fontSize: 32,
            fontWeight: "bold",
          },
        },
        legend: {
          show: true,
          data: [
            {
              icon: "rect",
              textStyle: {
                color: "white",
              },
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(116,236,159,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(116,236,159,1)",
                  },
                ]),
              },
            },
          ],
        },
        xAxis: {
          type: "category",
          data: this.xAxis, //数据
          axisLabel: {
            color: "white",
            fontSize: 20,
            interval: 0,
          },
          axisLine: {
            show: false, //不显示 
          },
          axisTick: false,
        },
        yAxis: {
          line: {
            show: false,
          },
          min: 0, //最小
          // max: 1200,//最大
          // interval: 200,//相差 
          type: "value",
          axisLine: {
            //坐标轴样式
            show: false, //不显示
          },
          axisLabel: {
            show: false, //不显示
            color: "#DEDEDE",
          },
          splitLine: {
            show: false, //不显示
            //分隔辅助线
            lineStyle: {
              type: "dashed", //线的类型 虚线0
              opacity: 1, //透明度
            },
          },
        },
        series: [
          {
            type: "custom",
            tooltip: {
              show: false,
            },
            renderItem: (params, api) => {
              const location = api.coord([api.value(0), api.value(1)]);
              return {
                type: "group",
                children: [
                  {
                    type: "CubeBack",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: "rgba(16, 55, 77,1)",
                    },
                  },
                ],
              };
            },
            data: [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100],
          },
          {
            zlevel: 1,
            z: -1,
            color: "#3d98df",
            tooltip: {
              // triggerOn:'mousemove'
            },
            type: "custom",
            renderItem: (params, api) => {
              const location = api.coord([api.value(0), api.value(1)]);
              return {
                type: "group",
                children: [
                  {
                    type: "CubeLeft",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "rgba( 45,204,137,1)",
                        },
                        {
                          offset: 0.4,
                          color: "rgba( 45,204,137,0.9)",
                        },
                        {
                          offset: 0.6,
                          color: "rgba( 45,204,137,1)",
                        },
                        {
                          offset: 1,
                          color: "rgba( 45,204,137,1)",
                        },
                      ]),
                    },
                  },
                  {
                    type: "CubeRight",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "rgba( 36,164,122,1)",
                        },
                        {
                          offset: 1,
                          color: "rgba( 36,164,122,1)",
                        },
                      ]),
                    },
                  },
                  {
                    type: "CubeTop",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "rgba( 54,240,151,1)",
                        },
                        {
                          offset: 1,
                          color: "rgba( 54,240,151,1)",
                        },
                      ]),
                    },
                  },
                ],
              };
            },
            data: this.data,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
<style lang="less" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>