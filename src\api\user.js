import request from "@/utils/request";
import { encryptedData } from "@/utils/encrypt";
import { loginRSA } from "@/config/settings";

import qs from "qs";
export async function mobileLogin(data) {
  data = {
    mobile: data.mobile,
    smscode: data.smscode,
  };
  return request({
    url: "/api/v1/wechat/wechatsmallapp/mobileLogin",
    method: "post",
    params: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export async function login(data) {
  data = {
    account: data.userName,
    pwd: data.password,
    key: data.key,
    code: data.code,
    flag:true
  };
  if (loginRSA) {
    data = await encryptedData(data);
  }
  return request({
    url: "/api/v1/auth/login",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function getInfo(token) {
  return request({
    url: "/api/v1/auth/userInfo",
    method: "post",
    // data: {
    //   token,
    // },
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function logout(token) {
  return request({
    url: "/api/v1/auth/logout",
    method: "post",
    data: {
      token,
    },
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function loginUserInfo(data) {
  return request({
    url: "/api/v1/loginUser/loginUserInfo",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: "/api/v1/display/user/findList",
    method: "post",
    data,
  });
}

export function getTree(form) {
  let params = { tenantId: form.tenantId, rootOrgId: form.rootOrgId };
  return request({
    url: `/api/v1/display/user/findOUTree`,
    method: "post",
    params,
    data: { userIds: form.userIds || [], sortOrder: form.sortOrder || "" },
  });
}

export function getOrganizInformationTree(code) {
  let params = {  rootOrgId: 'root', code };
  return request({
    url: `/api/v1/range/findOUTree`,
    method: "post",
    params,
  });
}

export function setOrganizInformationTree(data) {
  // let params = {  rootOrgId: 'root' };
  return request({
    url: `/api/v1/range/setOrgTree`,
    method: "post",
    data,
  });
}


export function getSimpleList(data) {
  return request({
    url: "/api/v1/display/user/findSimpleList",
    method: "post",
    data,
  });
}

export function getPage(data) {
  return request({
    url: "/api/v1/manage/user/findPage",
    method: "post",
    data,
  });
}

export function getUserById(params) {
  return request({
    url: "/api/v1/manage/user/findUser",
    method: "post",
    params,
  });
}

export function checkAccount(params) {
  return request({
    url: "/api/v1/manage/user/checkAccount",
    method: "post",
    params,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/manage/user/addUserWithRel",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/manage/user/changeUser",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/manage/user/removeUser",
    method: "post",
    data,
  });
}

export function changeSort(data) {
  return request({
    url: "/api/v1/manage/user/changeSort",
    method: "post",
    data,
  });
}

export function doStop(params) {
  return request({
    url: "/api/v1/manage/user/stopUser",
    method: "post",
    params,
  });
}

export function getOptions(params) {
  return request({
    url: "/api/v1/display/user/queryOptions",
    method: "post",
    params,
  });
}

export function doAddRel(data) {
  return request({
    url: "/api/v1/manage/user/addUserOrgRel",
    method: "post",
    data,
  });
}

export function doChangeRel(data) {
  return request({
    url: "/api/v1/manage/user/changeUserOrgRel",
    method: "post",
    data,
  });
}

export function doRemoveRel(data) {
  return request({
    url: "/api/v1/manage/user/removeUserOrgRel",
    method: "post",
    data,
  });
}

export function doChangeRelByOrg(data, orgId) {
  return request({
    url: "/api/v1/manage/user/changeUserOrgRelBatch/" + orgId,
    method: "post",
    data,
  });
}

// 更新个人主题 参数 {"theme":""}
export function updateTheme(data) {
  return request({
    url: "/api/v1/display/user/updateTheme",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}
export function sendLogInfo(params) {
  return request({
    url: "/system/accessLog/sendLogInfo",
    method: "post",
    params,
  });
}

// 查询用户信息
// export function fetchUserData(userId) {
//   return instance_1({
//     url: "/api/v1/system/user/findUser",
//     method: "post",
//     data: { userId },
//     headers: {
//       "Content-Type": "application/x-www-form-urlencoded",
//     },
//   });
// }

export function findTreeByCode(params) {
  return request({
    url: "/api/v1/range/findTreeByCode",
    method: "get",
    params,
  });
}

export function saveTreeSortByCode(params, data) {
  return request({
    url: "/api/v1/range/saveTreeSortByCode",
    method: "post",
    params,
    data
  });
}

export function listByCode(params) {
  return request({
    url: "/api/v1/orgRangeVirtualNode/listByCode",
    method: "get",
    params
  });
}

export function saveVirtualNode(data) {
  return request({
    url: "/api/v1/orgRangeVirtualNode/save",
    method: "post",
    data
  });
}

export function updateVirtualNode(data) {
  return request({
    url: "/api/v1/orgRangeVirtualNode/update",
    method: "post",
    data
  });
}
export function getNodesByVirtualNodeId(params) {
  return request({
    url: "/api/v1/orgRangeVirtualNode/getNodesByVirtualNodeId",
    method: "get",
    params
  });
}
export function saveSubVirtualNodes(params, data) {
  return request({
    url: "/api/v1/orgRangeVirtualNode/saveSubVirtualNodes",
    method: "post",
    params,
    data
  });
}
export function deleteVirtualNode(params) {
  return request({
    url: "/api/v1/orgRangeVirtualNode/delete",
    method: "post",
    params
  });
}

export function getClickMenu(params){
  return request({
    url: "/api/v1/menu/click/setClickDetail",
    method: "post",
    data: params
  });
}


export function getUserInfo(){
  return request({
    url: "/api/v1/auth/userInfo",
    method: "post",
    headers: {
      "appid": "sjjsc",
    },
  });
}