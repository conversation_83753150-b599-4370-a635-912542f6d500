<template>
  <div class="motion-page">
    <div class="motion-main_left">
      <ScmpCard cardName="建议涉及分类">
        <div slot="main" class="motion-main_left_warp">
          <div class="main_left_main">
            <div class="item" v-for="(item, index) in newProgressBarList" :key="index"
              @click="getFindPageList(findPageColum, '建议涉及分类', item.name, item.proposalContentTypes)">
              <div class="label-long" v-if="index == 7">{{ item.name }}</div>
              <div class="label" v-else>{{ item.name }}</div>
              <div class="progress-bar">
                <div class="bar" :style="`width:${item.displayPercentage}%`"></div>
                <div class="cursor" :style="`left:calc(${item.displayPercentage}% - 5px)`">
                  <div class="sub"></div>
                </div>
              </div>
              <div class="number">{{ item.number }}</div>
            </div>
          </div>
          <div class="wordCloud-warp">
            <div class="wordCloud-warp_bg"></div>
            <wordCloud class="wordCloud-warp_main" :data="wordCloud">
            </wordCloud>
          </div>
        </div>
      </ScmpCard>
    </div>

    <div class="motion-main_center">
      <div class="center-top">
        <div class="customTab" :class="`customTab${tabActive}`" @click="tabSwitching(1)">
          大会期间
        </div>
        <ScmpSpot :spotName="areaName" dropDownTitle="切换专题" :column="spotDownColumn" :dropDownList="dropDownList"
          @checkedItem="checkedItem">
        </ScmpSpot>
        <div class="customTab" :class="`customTabact${tabActive == 1 ? 0 : 1}`" @click="tabSwitching(0)">
          闭会期间
        </div>
      </div>
      <div class="common-center three-card">
        <div v-for="(item, index) in threeCardList" :key="index"
          @click="getFindPageList(findPageColum, '建议数量列表', item.name, '', '', item.proposalType, index)">
          <div class="three-card_background">
            <div class="title">{{ item.name }}</div>
            <div class="number">
              <span>{{ item.number }}</span>件
            </div>
          </div>
          <div class="three-card_light"></div>
        </div>
      </div>
      <div class="common-center l1-r5">
        <div class="l1">
          <div class="name">今日新增</div>
          <div class="number"><span>{{ todayInsert }}</span>件</div>
        </div>
        <div class="r5">
          <div class="r5-header">待处理议案建议</div>
          <div class="r5-list">
            <div class="r5-item" v-for="(item, index) in (tabActive === 1 ? fourCalorieList : fourCalorieListClosed)"
              :key="index" @click="getFindPageList(findPageColum, '待处理议案建议', item.name, '', item.status)">
              <div class="r5-item_top">
                <div class="number">
                  <span>{{ item.number }}</span>件
                </div>
                <div class="name">{{ item.name }}</div>
              </div>
              <div class="r5-item_bottom"></div>
            </div>
          </div>
        </div>
      </div>
      <!-- 议案动态 -->
      <div class="common-center selp-list">
        <ScmpTitle style="margin-top: -40px" @getList="getList" @resetFields="resetFields"></ScmpTitle>
        <div class="data-list" style="padding: 0 82px">
          <customizeSteps v-for="(item, index) in (tabActive === 1 ? stepsList : stepsList2)" :key="index"
            :header="item.name" :headPer="item.headPer" :data="item.stepObj" :areaName="areaName" :isMeeting="tabActive"></customizeSteps>
        </div>
      </div>
    </div>

    <div class="motion-main_right">
      <ScmpCard cardName="重点督办建议" pictureMode="2">
        <!-- <div slot="main" class="scmp-card__main" @click="goDataView(deptDictList, deptDictTableColumn, '重点督办建议')"> -->
        <div slot="main" class="scmp-card__main">
          <ScmpTable :tableData="deptDictList" :tableColumn="deptDictTableColumn" :areaName="areaName" :rowNum="11"
            :indicesGrouping="true" :isMeeting="tabActive">
          </ScmpTable>
        </div>
      </ScmpCard>
      <!-- <ScmpCard cardName="分办单位承办情况" pictureMode="2" style="margin-top: 44px"> -->
      <ScmpCard :cardName="tabActive === 1 ? '大会建议办理情况' : '闭会建议办理情况'" pictureMode="2" style="margin-top: 44px">

        <div slot="main" class="four-cards">
          <div class="single-card" v-for="(item, index) in singleCardList" :key="index">
            <div class="title">{{ item.name }}</div>
            <div class="rate">
              <span>{{ item.rate }}</span>%
            </div>
          </div>
        </div>
      </ScmpCard>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getHandleStatus,
  getPendingCountQz,
  getProposalCountQz,
  getTodayInsert,
  getContentTypeCountQz,
  findQzByPage,
  getQZTodayInsert,
  getQZRecommendList
} from "@/api/dataPageApi/motion";
export default {
  name: "motion-index",
  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    ScmpTable: () => import("@/comps/scmp-YAJYTable"),
    ScmpSpot: () => import("@/comps/scmp-spot"),
    ScmpTitle: () => import("@/comps/scmp-title"),
    wordCloud: () => import("@/components/motion/wordCloud.vue"),
    customizeSteps: () => import("@/components/motion/customizeSteps.vue"),
  },
  data() {
    return {
      // Table配置
      deptDictList: [],
      deptDictTableColumn: [
        { prop: "sort", label: "序号", align: "center", width: 90 },
        { prop: "proposalNum", label: "建议号", align: "center", width: 180 },
        { prop: "proposalTitle", label: "标题", align: "center" },
        { prop: "statusText", label: "状态", align: "center", width: 180 },
      ],
      findPageColum: [
        { prop: "meeting", label: "届次", align: "center", width: 250 },
        { prop: "proposalNum", label: "建议号", align: "center", width: 250 },
        { prop: "proposalType", label: "类型", align: "center", width: 250 },
        { prop: "proposalTitle", label: "标题", align: "center", width: 1000 },
        { prop: "headPer", label: "领衔代表", align: "center", width: 250 },
        { prop: "zBDWName", label: "主办单位", align: "center", width: 550 },
        { prop: "createTime", label: "提出时间", align: "center", width: 550 },
        { prop: "status", label: "建议状态", align: "center", width: 350 },
      ],
      // 词云图数据
      wordCloud: [],
      // 进度条列表
      progressBarList: [],
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],
      // 中间内容
      tabActive: 1,
      // 三卡位
      threeCardList: [
        {
          name: "议案数",
          number: 0,
          type: 1,
        },
        {
          name: "建议数",
          number: 0,
          type: 2,

        },
        {
          name: "供参考建议数",
          number: 0,
          type: 4,
        },
      ],
      // 左一右四-四卡
      fourCalorieList: [
        {
          name: "待校核",
          number: 0,
        },
        {
          name: "待分类",
          number: 0,
        },
        {
          name: "待初审",
          number: 0,
        },
        {
          name: "待复审",
          number: 0,
        },
        {
          name: "待分办",
          number: 0,
        },
      ],
      // 新增闭会期间的列表
      fourCalorieListClosed: [
        {
          name: "待校核",
          number: 0,
        },
        {
          name: "待分类",
          number: 0,
        },
        {
          name: "待初审",
          number: 0,
        },
        {
          name: "待分办",
          number: 0,
        },
      ],
      // 步骤数据
      stepsList: [
        // {
        //   name: "张三领衔提出\"关于增加养老福利的建议\"",
        //   stepObj: {
        //     active: 4,
        //     titleList: [
        //       "预提交",
        //       "核校",
        //       "分类",
        //       "初审",
        //       "复审",
        //       "分办",
        //       "签收",
        //       "答复",
        //       "评价",
        //       "已办结",
        //     ],
        //   },
        // },
      ],
      // 步骤数据
      stepsList2: [
        // {
        //   name: "张三领衔提出\"关于增加养老福利的建议\"",
        //   stepObj: {
        //     active: 4,
        //     titleList: [
        //       "提交",
        //       "核校",
        //       "分类",
        //       "审核",
        //       "分办",
        //       "签收",
        //       "答复",
        //       "评价",
        //       "已办结",
        //     ],
        //   },
        // },
      ],
      // 右下方四卡位
      singleCardList: [],
      todayInsert: 0,
      proposalCount: [],
      dhType: 1,
      findPageList: [],
      findPageListTotal: 0,
      orgCode: 0,
      lastMeetingId: 0,
      deptDictListTotal: 0,
      currentPage: 1,
      pageSize: 10,
      areaName: '',
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
    // newProgressBarList() {
    //   return this.progressBarList.map(item => {
    //     // 动态计算比例（避免固定除以1000）
    //     const maxValue = Math.max(...this.progressBarList.map(i => i.value), 1500);
    //     const percentage = (item.value / maxValue) * 500;

    //     return {
    //       ...item,
    //       displayPercentage: Math.max(percentage, 0) // 保证最小显示5%
    //     };
    //   });
    // }
    newProgressBarList() {
      return this.progressBarList.map(item => {
        // 动态计算比例，确保不超过100%
        const maxValue = Math.max(...this.progressBarList.map(i => i.value));
        let percentage = (item.value / maxValue) * 100; // 限制在100%以内

        // 设置最小显示宽度为10%
        const minPercentage = 0.1;
        let displayPercentage;
        if (item.value === 0) {
          displayPercentage = 0;
        } else {
          displayPercentage = Math.max(percentage, minPercentage);
        }

        // 确保不超过100%
        displayPercentage = Math.min(displayPercentage, 100);

        return {
          ...item,
          displayPercentage: displayPercentage
        };
      });
    }
  },
  created() {
    this.orgCode = this.$route.query.administrativeAreaId;
    this.lastMeetingId = this.$route.query.lastMeetingId
    this.areaName = this.$route.query.areaName + '议案建议工作视窗'
    this.fetchData(this.dhType, this.lastMeetingId)
  },
  mounted() { },
  watch: {},
  methods: {
    // 跳转详情
    goDataDetail(id) {
      this.$router.push({
        path: '/dataDetailTemplate',
        query: {
          proposalId: id,
          isMeeting: this.tabActive
          // item: itemString,
          // colum: columString,
          // title: title + '-' + name
        }
      });
    },

    // 列表详情下钻
    goDataView(item, colum, title) {
      const itemString = JSON.stringify(item);
      const columString = JSON.stringify(colum);
      console.log(itemString)
      this.$router.push({
        path: '/dataTemplate',
        query: {
          total: item.length,
          title: title,
          item: itemString,
          colum: columString,
          isMeeting: this.tabActive
        }
      });
    },

    fetchData(dhType, id) {
      this.getContentTypeCountQz(dhType, id)
      this.getHandleStatus(dhType, id)
      this.getPendingCountQz(dhType, id)
      this.getProposalCountQz(dhType, id)
      this.getRecommendList(dhType, id)
      this.getTodayInsert(dhType, id)
      this.findQzByPage()
    },
    getContentTypeCountQz(dhType, id) {
      const params = {
        // isMeeting: dhType,
        isMeeting: '',
        // meeting: id,
        meeting: '',
        areaId: this.orgCode,
        proposalType: this.tabActive == 0 ? 3 : 2,
      }
      getContentTypeCountQz(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 类别映射关系
          const categoryMap = {
            "JYFL01": "法制",
            "JYFL02": "监察和司法",
            "JYFL09": "预算",
            "JYFL03": "经济",
            "JYFL04": "城建环资",
            "JYFL05": "农村农业",
            "JYFL06": "教科文卫",
            "JYFL07": "华侨外事民族宗教",
            "JYFL08": "其他",
            "JYFL10": "社会建设"
          };

          // 定义展示顺序（可选）
          const displayOrder = [
            "JYFL01",
            "JYFL02",
            "JYFL09",
            "JYFL03",
            "JYFL04",
            "JYFL05",
            "JYFL06",
            "JYFL07",
            "JYFL10",
            "JYFL08",
          ];

          // 处理数据
          // const test =
          //   {
          //     JYFL01: 2,
          //     JYFL02: 96,
          //     JYFL03: 287,
          //     JYFL04: 129,
          //     JYFL05: 21,
          //     JYFL06: 188,
          //     JYFL07: 2,
          //     JYFL09: 41,
          //     JYFL10: 96,
          //     JYFL08: 62,
          //   }

          console.log(res.data)
          this.progressBarList = displayOrder.map(code => ({
            name: categoryMap[code],
            value: res.data[code] || 0,  // 没有的数据显示为0
            // value: Math.floor(Math.random() * 1501) + 100,  // 没有的数据显示为0
            proposalContentTypes: code,
          }));

          console.log('progressBarList')
          console.log(this.progressBarList)
          const filteredData = this.progressBarList.filter(item => item.value >= 0)
            .map(item => ({
              name: `${item.name}(${item.value})`,
              value: item.value
            }));
          this.wordCloud = filteredData;
          console.log(this.wordCloud)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getHandleStatus(dhType, id) {
      const params = {
        isMeeting: dhType,
        meeting: id,
        orgCode: this.orgCode
      }
      getHandleStatus(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 映射关系配置
          const fieldMapping = {
            "FB_RATIO": "已分办",
            "QS_RATIO": "已签收",
            "DF_RATIO": "已答复",
            "PJ_RATIO": "已评价"
          };

          // 转换逻辑

          // if (this.tabActive == 0) {
          //   this.singleCardList = [
          //     {
          //       "name": "已分办",
          //       "rate": 0
          //     },
          //     {
          //       "name": "已签收",
          //       "rate": 0
          //     },
          //     {
          //       "name": "已答复",
          //       "rate": 0
          //     },
          //     {
          //       "name": "已评价",
          //       "rate": 0
          //     }
          //   ]
          //   console.log('singleCardList')
          //   console.log(this.singleCardList)
          // } else {
          this.singleCardList = Object.entries(fieldMapping).map(([field, name]) => ({
            name,
            // rate: Math.round(res.data[0][field] * 100)  // 比率转百分比并四舍五入
            rate: 0  //区无此类数据
          }));
          console.log('singleCardList')
          console.log(this.singleCardList)
          // }

        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getPendingCountQz(dhType, id) {
      const params = {
        // isMeeting: dhType,
        // meeting: id,
        areaId: this.orgCode,
        proposalType: this.tabActive == 0 ? 3 : 2,
      }
      this.fourCalorieList = []
      getPendingCountQz(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 字段映射配置
          const FIELD_MAPPING = {
            "DJH": { name: "待校核", status: 20 },
            "DFL": { name: "待分类", status: 21 },
            "DCH": { name: "待初审", status: 22 },
            "DFS": { name: "待复审", status: 30 },
            "DFB": { name: "待分办", status: 40 }
          };

          const FIELD_MAPPING_close = {
            "DJH": { name: "待校核", status: 20 },
            "DFL": { name: "待分类", status: 21 },
            "DCH": { name: "待初审", status: 22 },
            // "DFS": { name: "待复审", status: 30 },
            "DFB": { name: "待分办", status: 40 }
          };



          if (dhType == 0) {
            // 数据转换函数 闭会期间
            this.fourCalorieListClosed = Object.entries(FIELD_MAPPING_close).map(([field, mapping]) => ({
              name: mapping.name,
              number: res.data[0][field] || 0,
              status: mapping.status
            }));
            console.log('fourCalorieListClosed')
            console.log(this.fourCalorieListClosed)
          } else {
            // 数据转换函数 大会期间
            this.fourCalorieList = Object.entries(FIELD_MAPPING).map(([field, mapping]) => ({
              name: mapping.name,
              number: res.data[0][field] || 0,
              status: mapping.status
            }));
          }

        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getProposalCountQz(dhType, id) {
      const params = {
        // isMeeting: dhType,
        // meeting: id,
        areaId: this.orgCode,
        proposalType: this.tabActive == 0 ? 3 : 2,
      }
      getProposalCountQz(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 类型映射关系
          const typeMapping = {
            "1": { name: "议案数", proposalType: '1' },
            "2": { name: "建议数", proposalType: '2' },
            "3": { name: "建议数", proposalType: '3' }, // 闭会也计入建议数
            "4": { name: "供参考建议", proposalType: '4' }
          };

          // 初始化统计对象
          const result = {
            "议案数": { number: 0, proposalType: '1' },    // 默认议案数为0
            "建议数": { number: 0, proposalType: '2' },
            "供参考建议": { number: 0, proposalType: '4' }
          };

          // 遍历原始数据并统计
          Object.entries(res.data).forEach(([type, count]) => {
            const mapping = typeMapping[type];
            if (mapping) {
              result[mapping.name].number += count;
            }
          });

          // 转换为目标格式

          if (this.tabActive == 1) {
            this.threeCardList = Object.entries(result).map(([name, data]) => ({
              name,
              number: data.number,
              proposalType: data.proposalType
            }));
            this.threeCardList[0].number = res.data.YIAN
            this.threeCardList[1].number = res.data.DHJY
            this.threeCardList[2].number = res.data.GCKJY
          } else {
            this.threeCardList = Object.entries(result).map(([name, data]) => ({
              name,
              number: data.number,
              proposalType: data.proposalType
            }));
            this.threeCardList[0].number = res.data.YIAN
            this.threeCardList[1].number = res.data.BHJY
            this.threeCardList[2].number = res.data.GCKJY
          }

          console.log('threeCardList')
          console.log(this.threeCardList)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getRecommendList(dhType, id) {
      const params = {
        isMeeting: dhType,
        // meeting: id,
        areaId: this.orgCode,
        stress: 1,
        proposalType: this.tabActive == 0 ? 3 : 2,
      }
      getQZRecommendList(params)
        .then(res => {
          console.log(res);  // 处理返回的数据

          // 状态映射表
          const statusMap = {
            10: "提交",
            20: "校核",
            21: "预分类",
            22: "初审",
            30: "复审",
            40: "分办",
            41: "不予立案审核",
            45: "不予立案确认",
            50: "签收",
            60: "答复",
            70: "评价",
            90: "办结"
          };
          // this.deptDictListTotal = this.deptDictList.length
          this.deptDictList = res.data.map(item => ({
            ...item,
            statusText: statusMap[item.status] || `未知状态(${item.status})`
          }));
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getTodayInsert(dhType, id) {
      const params = {
        isMeeting: dhType,
        // meeting: id,
        orgCode: this.orgCode
      }
      getQZTodayInsert(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // this.todayInsert = res.data.length || 0
          this.todayInsert = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    findQzByPage(title, name, type) {
      const params = {
        page: 1,
        rows: 30,
        order: "desc",
        // meeting: this.lastMeetingId,
        huiban: "",
        proposalContentType: type ? type : '',
        proposalTitle: title || '',
        headPer: name || '',
        proposalType: this.tabActive == 0 ? 3 : 2,
        isMeeting: this.tabActive,
        areaId: this.orgCode
      }
      findQzByPage(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 状态映射表
          const statusMap = {
            10: { title: "提交", step: 0 },
            20: { title: "校核", step: 1 },
            21: { title: "预分类", step: 2 },
            22: { title: "初审", step: 3 },
            30: { title: "复审", step: 4 },
            40: { title: "分办", step: 5 },
            41: { title: "不予立案审核", step: 5 }, // 41归为分办
            45: { title: "不予立案确认", step: 5 }, // 45归为分办
            50: { title: "签收", step: 6 },
            60: { title: "答复", step: 7 },
            70: { title: "评价", step: 8 },
            90: { title: "已办结", step: 9 }
          };

          // 完整的步骤列表（按顺序）
          const fullSteps = [
            "提交",
            "校核",
            "预分类",
            "初审",
            "复审",
            "分办",
            "签收",
            "答复",
            "评价",
            "已办结"
          ];
          // 转换函数
          this.stepsList = res.data.records.map(item => {
            const currentStatus = item.status;
            // 确定当前active步骤（处理40/41/45特殊情况）
            const active = statusMap[currentStatus]?.step ?? 0;
            return {
              // name: `${item.headPer}领衔提出"${item.proposalTitle}"`, // 使用headPer和proposalTitle
              name: item.proposalTitle,
              headPer: item.headPer,
              stepObj: {
                active, // 当前步骤索引
                titleList: fullSteps, // 完整步骤列表
                proposalId: item.proposalId
              }
            };
          });

          if (this.tabActive == 0) {
            this.stepsList2 = res.data.records.map(item => {
              const currentStatus = item.status;
              // 确定当前active步骤（处理40/41/45特殊情况）
              const active = statusMap[currentStatus]?.step ?? 0;
              return {
                // name: `${item.headPer}领衔提出"${item.proposalTitle}"`, // 使用headPer和proposalTitle
                name: item.proposalTitle,
                headPer: item.headPer,
                stepObj: {
                  active, // 当前步骤索引
                  titleList: fullSteps, // 完整步骤列表
                  proposalId: item.proposalId
                }
              };
            });
          }

          console.log(this.stepsList);
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getFindPageList(colum, title, name, type, status, proposalType, checkType) {
      console.log('checkType')
      console.log(checkType)
      const params = {
        page: this.currentPage,
        rows: this.pageSize,
        order: "desc",
        // meeting: this.lastMeetingId,
        huiban: "",
        proposalContentType: type ? type : '',
        proposalTitle: '',
        headPer: '',
        // proposalType: proposalType,
        // proposalType: this.tabActive == 0 ? 3 : 2,

        proposalType: checkType === 0 ? 1
          : (checkType === 1 ? (this.tabActive === 1 ? 2 : 3)
            : (checkType === 2 ? 4
              : (this.tabActive === 0 ? 3 : 2))), // 默认保持原逻辑
        status: status || '',
        hasCheck: proposalType ? true : '',
        isMeeting: this.tabActive,
        areaId: this.orgCode
      }
      findQzByPage(params)
        .then(res => {
          const meetingMap = {
            "144": "十六届五次",
            "143": "十六届四次",
            "142": "十六届三次",
            "141": "十六届一次",
            "140": "十五届六次",
            "137": "十五届五次",
            "136": "十五届四次",
            "135": "十五届三次",
            "132": "十四届一次",
            "133": "十五届一次",
            "93": "十二届四次",
            "68": "八届一次",
            "67": "七届一次",
            "66": "六届一次",
            "65": "五届一次",
            "73": "九届一次",
            "78": "十届一次",
            "92": "十二届三次",
            "91": "十二届二次",
            "90": "十二届一次",
            "87": "十一届五次",
            "86": "十一届四次",
            "85": "十一届三次",
            "84": "十一届二次",
            "83": "十一届一次",
            "82": "十届五次",
            "81": "十届四次",
            "80": "十届三次",
            "79": "十届二次",
            "77": "九届五次",
            "76": "九届四次",
            "75": "九届三次",
            "74": "九届二次",
            "72": "八届五次",
            "71": "八届四次",
            "70": "八届三次",
            "69": "八届二次",
            "64": "四届一次",
            "63": "三届一次",
            "62": "二届一次",
            "61": "一届一次"
          }
          const proposalTypeMap = {
            "1": "大会议案",
            "2": "大会建议",
            "3": "闭会建议",
            "4": "供参考建议",
          }

          const statusMap = {
            10: { title: "提交", step: 0 },
            20: { title: "校核", step: 1 },
            21: { title: "预分类", step: 2 },
            22: { title: "初审", step: 3 },
            25: { title: "复审", step: 3 },
            30: { title: "复审", step: 4 },
            40: { title: "分办", step: 5 },
            41: { title: "不予立案审核", step: 5 }, // 41归为分办
            42: { title: "分办审核中", step: 5 },
            43: { title: "分办复核中", step: 5 }, // 41归为分办
            45: { title: "不予立案确认", step: 5 }, // 45归为分办
            50: { title: "签收", step: 6 },
            60: { title: "答复", step: 7 },
            70: { title: "评价", step: 8 },
            90: { title: "已办结", step: 9 }
          };

          // this.findPageListTotal = res.data.total
          this.findPageList = res.data.records.map(item => ({
            ...item,
            // meeting: meetingMap[item.meeting] || item.meeting,
            // meeting: this.lastMeetingId,
            status: statusMap[item.status]?.title || item.status,
            proposalType: proposalTypeMap[item.proposalType] || item.proposalType
          }));
          // console.log(this.findPageList)

          const columString = JSON.stringify(colum);
          const itemString = JSON.stringify(this.findPageList);
          const reParams = JSON.stringify(params);
          // console.log('itemString')
          // console.log(itemString)
          // console.log( res.data.total)

          // if (this.findPageList.length > 0) {
          // if (this.tabActive != 0) {
          this.$router.push({
            path: '/dataTemplate',
            query: {
              title: title + '-' + name,
              total: res.data.total,
              item: itemString,
              colum: columString,
              params: reParams,
              orgCode: this.orgCode,
              areaName: this.areaName,
              isMeeting: this.tabActive
            }
          });
          // }
          // }

          console.log(this.stepsList);
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    checkedItem(obj) {
      console.log("选中", obj);
    },
    /**
     * @description tab切换
     */
    tabSwitching(str) {

      if (this.tabActive === str) {
        return;
      }
      this.tabActive = str;
      this.fetchData(str, this.lastMeetingId)
    },
    getList(value) {
      console.log("查询数据", value);
      // 根据输入的关键词 进行搜索
      this.findQzByPage(value.title, value.name)
    },
    resetFields(value) {
      // 重置输入框的内容
      console.log('重置数据')
      this.findQzByPage(value.title, value.name)
    }
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
.scmp-eltable {
  ::v-deep .mb15 {
    height: 965px !important;
  }
}

.motion-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .motion-main_left {
    width: 853px;
    margin-top: 54px;
    margin-bottom: 25px;

    .motion-main_left_warp {
      padding: 21px 35px 0px 48px;

      .main_left_main {
        height: 1045px;
        background: url("@/assets/image/bar_bottom_bg.png") no-repeat;
        background-size: contain;
        /* 背景图覆盖整个元素区域 */
        background-position: center bottom;
        background-attachment: fixed;
        padding-bottom: 72px;
        margin-top: 46px;

        .item {
          height: 106px;
          border-radius: 0px 0px 0px 0px;
          display: flex;
          flex-direction: row;
          //   justify-content: space-between;
          align-items: center;
          cursor: pointer;

          .label {
            /*width: 120px;*/
            width: 180px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 30px;
            color: #ffffff;
            line-height: 35px;
            font-style: normal;
            text-transform: none;
            padding-left: 32px;
          }

          .label-long {
            text-align: center;
            width: 140px;
            margin-right: 40px;
            margin-left: -10px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 30px;
            color: #ffffff;
            line-height: 36px;
            font-style: normal;
            text-transform: none;
            padding-left: 32px;
          }

          .progress-bar {
            width: 479px;
            height: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0px 0px 0px 0px;
            margin: 0 30px 0 6px;
            position: relative;

            .bar {
              height: 100%;
              width: 0;
              background: linear-gradient(90deg, #5a3fff 0%, #1ed6ff 100%);
              border-radius: 0px 0px 0px 0px;
            }

            .cursor {
              position: absolute;
              top: -5px;
              width: 42px;
              height: 42px;
              background: rgba(137, 164, 255, 0.5);
              box-shadow: 0px 0px 6px 4px rgba(108, 176, 255, 0.65);
              border-radius: 21px;
              line-height: 42px;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;

              .sub {
                width: 26px;
                height: 26px;
                background: #b8eaff;
                box-shadow: 0px 0px 5px 1px rgba(255, 255, 255, 0.85);
                border-radius: 13px;
              }
            }
          }

          .number {
            font-family: DIN-BlackItalic, DIN-BlackItalic;
            font-weight: 400;
            font-size: 40px;
            color: #ffe062;
            line-height: 47px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      .wordCloud-warp {
        position: relative;
        height: 542px;

        .wordCloud-warp_bg {
          position: absolute;
          width: 114%;
          margin-left: -62px;
          top: 78px;
          height: 400px;
          background: url("@/assets/image/wordCloud-warp_bg.png") no-repeat;
          background-size: 100% auto;
        }

        .wordCloud-warp_main {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .motion-main_center {
    width: 1992px;
    position: relative;

    .center-top {
      display: flex;
      flex-direction: row;
      justify-content: center;
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      line-height: 25px;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      position: relative;

      .customTab {
        margin-top: 20px;
        padding: 40px 103px 60px 111px;
        position: absolute;
        cursor: pointer;
      }

      .customTab1 {
        background: url("@/assets/image/customTab1.png") no-repeat;
        background-size: contain;
        color: #ffd700;
        font-weight: bold;
        left: 45px;
      }

      .customTab0 {
        background: url("@/assets/image/customTab2.png") no-repeat;
        background-size: contain;
        left: 45px;
      }

      .customTabact1 {
        background: url("@/assets/image/customTabact1.png") no-repeat;
        background-size: contain;

        right: 45px;
      }

      .customTabact0 {
        background: url("@/assets/image/customTabact2.png") no-repeat;
        background-size: contain;
        color: #ffd700;
        font-weight: bold;
        right: 45px;
      }
    }

    .three-card {
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin-top: 18px;
      height: 344px;

      .three-card_background {
        width: 550px;
        height: 290px;
        background: url("@/assets/image/Three-card_background.png") no-repeat;
        background-size: 100% 100%;
        position: relative;
        cursor: pointer;

        .title {
          position: absolute;
          left: 77px;
          top: 59px;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 41px;
          color: #ffffff;
          line-height: 48px;
          text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
          text-stroke: 0px rgba(255, 255, 255, 0.11);
          text-align: left;
          font-style: normal;
          text-transform: none;
          -webkit-text-stroke: 0px rgba(255, 255, 255, 0.11);
        }

        .number {
          position: absolute;
          left: 77px;
          top: 112px;
          font-family: DIN, DIN;
          font-weight: bold;
          font-size: 30px;
          color: #ffffff;
          line-height: 35px;
          text-stroke: 1px rgba(255, 255, 255, 0.11);
          text-align: right;
          font-style: normal;
          text-transform: none;
          -webkit-text-stroke: 1px rgba(255, 255, 255, 0.11);

          span {
            margin-right: 10px;
            font-family: DIN-MediumItalic, DIN-MediumItalic;
            font-weight: 400;
            font-size: 50px;
            color: rgba(251, 233, 71, 1);
            line-height: 59px;
            text-shadow: 0px 0px 7px rgba(255, 179, 179, 0.71);
            text-stroke: 0px #ffffff;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #ca320b 100%),
              linear-gradient(90deg, #f6ffff 0%, #29f1fa 100%),
              linear-gradient(270.00000009864743deg,
                rgba(255, 144, 62, 0) 0%,
                #fbe947 56%);
            -webkit-text-stroke: 0px #ffffff;
            -webkit-background-clip: text;
            /* WebKit 浏览器专用属性，使背景剪切至文本 */
            background-clip: text;
            /* 标准属性 */
          }
        }
      }

      .three-card_light {
        width: 550px;
        height: 204px;
        margin-top: -172px;
        background: url("@/assets/image/center_light.png") no-repeat;
        background-size: 48% 100%;
        background-position: center;
      }
    }

    .l1-r5 {
      display: flex;
      flex-direction: row;
      height: 362px;

      .l1 {
        display: flex;
        flex-direction: column;
        margin-left: 220px;

        .name {
          margin-top: 24px;
          font-family: PangMenZhengDao, PangMenZhengDao;
          font-weight: 400;
          font-size: 70px;
          color: #ffffff;
          line-height: 82px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .number {
          margin-top: 32px;
          width: 306px;
          height: 203px;
          background: url("@/assets/image/l1_bg.png") no-repeat;
          background-size: contain;
          background-position: center bottom;
          font-family: DIN, DIN;
          font-weight: bold;
          font-size: 30px;
          color: #ffffff;
          line-height: 35px;
          text-stroke: 1px rgba(255, 255, 255, 0.11);
          text-align: center;
          font-style: normal;
          text-transform: none;
          -webkit-text-stroke: 1px rgba(255, 255, 255, 0.11);

          span {
            margin-right: 25px;
            font-family: DIN, DIN;
            font-weight: 500;
            font-size: 90px;
            color: #ffe062;
            line-height: 105px;
            text-shadow: 0px 0px 13px rgba(121, 175, 255, 0.96);
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      .r5 {
        display: flex;
        flex-direction: column;
        flex: 1;

        // justify-content: space-between;
        .r5-header {
          margin-top: 34px;
          background: url("@/assets/image/r5_title.png") no-repeat;
          background-size: 100% 100%;
          width: max-content;
          height: 52px;
          padding: 0 288px;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 40px;
          color: #ffffff;
          line-height: 47px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-left: 182px;
        }

        .r5-list {
          margin-left: 20px;
          height: 229px;
          border-radius: 8px 8px 8px 8px;
          border: 4px solid;
          border-right: 0;
          border-image: linear-gradient(180deg,
              rgba(28, 110, 190, 0),
              rgba(28, 110, 190, 0.86),
              rgba(28, 110, 190, 0)) 4 4;
          display: flex;
          flex-direction: row;

          .r5-item {
            margin: 0 30px;
            width: 176px;
            cursor: pointer;

            .r5-item_top {
              margin-top: 24px;
              height: 189px;
              width: 100%;
              background: url("@/assets/image/four_calorie.png") no-repeat;
              background-position: center bottom;
              background-size: contain;
              position: relative;

              .number {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 30px;
                color: #ffffff;
                line-height: 35px;
                text-shadow: 0px 0px 23px #010507;
                text-align: center;
                font-style: normal;
                text-transform: none;

                span {
                  margin-right: 13px;
                  font-family: DIN, DIN;
                  font-weight: bold;
                  font-size: 70px;
                  color: #28f1fa;
                  line-height: 82px;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71),
                    0px 0px 5px rgba(66, 158, 219, 0.93);
                  text-align: center;
                  font-style: normal;
                  text-transform: none;
                  background: linear-gradient(157.0241536189072deg,
                      #ffffff 0%,
                      #45f4ff 99%);

                  -webkit-background-clip: text;
                  /* WebKit 浏览器专用属性，使背景剪切至文本 */
                  background-clip: text;
                  /* 标准属性 */
                }
              }

              .name {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 40px;
                color: #ffffff;
                line-height: 47px;
                text-shadow: 0px 0px 23px #010507;
                text-align: center;
                font-style: normal;
                text-transform: none;
              }
            }

            .r5-item_bottom {
              margin-top: -100px;
              height: 150px;
              width: 100%;
              background: url("@/assets/image/center_light.png") no-repeat;
              background-size: contain;
            }
          }
        }
      }
    }

    .selp-list {
      height: 1107px;
    }

    .data-list {
      height: 830px;
      // border: 1px solid;
      overflow-y: auto;
      margin-right: 30px;

      &::-webkit-scrollbar {
        height: 8px; // 滚动条高度
        background: rgba(0, 25, 63, 0.2); // 轨道背景
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(90deg,
            rgba(0, 25, 63, 0) 0%,
            rgba(10, 108, 222, 0.8) 50%,
            rgba(0, 25, 63, 0) 100%);
        border-radius: 4px;
        border: 1px solid rgba(10, 108, 222, 0.5); // 添加边框增强科技感
        /*box-shadow: 0 0 5px rgba(78, 153, 248, 0.5); // 发光效果*/
      }

      &::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(90deg,
            rgba(0, 25, 63, 0) 0%,
            rgba(10, 150, 255, 0.9) 50%,
            rgba(0, 25, 63, 0) 100%);
      }
    }

    .common-center {
      background: url("@/assets/image/common-center_bg.png") no-repeat;
      width: 100%;
      background-size: contain;
      background-position: center bottom;
    }
  }

  .motion-main_right {
    width: 839px;
    margin-bottom: 25px;
    margin-top: 54px;

    // 新增滚动条样式（修正位置）
    ::v-deep .scmp-card {
      .scmp-card__main {
        max-height: 1010px;
        // overflow-y: auto !important;
        padding-right: 12px; // 给滚动条留出空间
        cursor: pointer;

        &::-webkit-scrollbar {
          width: 8px;
          background: rgba(255, 255, 255, 0.1);
        }

        &::-webkit-scrollbar-thumb {
          background: linear-gradient(180deg, #5A3FFF 0%, #1ED6FF 100%);
          border-radius: 4px;
        }
      }
    }

    .four-cards {
      padding: 24px 25px 68px 40px;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start; // 替代原先的space-between布局方式

      .single-card {
        position: relative;
        flex: 1;
        height: 209px;
        margin: 30px 5px 5px 0; // 间隙为5px

        background: url("@/assets/image/single_card_background.png") no-repeat;
        background-size: contain;
        width: calc((100% - 10px) / 2); // 这里的10px = (分布个数2-1)*间隙5px, 可以根据实际的分布个数和间隙区调整
        min-width: calc((100% - 10px) / 2); // 加入这两个后每个item的宽度就生效了
        max-width: calc((100% - 10px) / 2); // 加入这两个后每个item的宽度就生效了

        &:nth-child(2n) {
          // 去除第2n个的margin-right
          margin-right: 0;
        }

        .title {
          position: absolute;
          left: 188px;
          top: 28px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 50px;
          color: #ffffff;
          line-height: 59px;
          text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .rate {
          position: absolute;
          left: 188px;
          top: 94px;
          font-family: DIN, DIN;
          font-weight: bold;
          font-size: 50px;
          color: #ffffff;
          line-height: 59px;
          text-align: right;
          font-style: normal;
          text-transform: none;

          span {
            font-family: DIN-BoldItalic, DIN-BoldItalic;
            font-weight: 400;
            margin-right: 10px;
            color: #00d46a;
            font-size: 50px;
            line-height: 59px;
            text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #17a4f3 100%),
              linear-gradient(90deg, #ffffff 0%, #00d46a 100%);
            -webkit-background-clip: text;
            /* WebKit 浏览器专用属性，使背景剪切至文本 */
            background-clip: text;
            /* 标准属性 */
          }
        }
      }
    }
  }
}

.motion-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
  /* 设置透明度 */
}
</style>
