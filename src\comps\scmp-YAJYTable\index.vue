<template>
  <div class="scmp-eltable">
    <el-table :data="tableData" class="table-content mb15" :stripe="true" header-row-class-name="headerRowClassName"
      header-cell-class-name="headerCellClassName" :row-class-name="setRowClassName" :height="rowNum * 99"
      @row-click="handleRowClick">
      <el-table-column v-for="(item, index) in tableColumn" :key="index" :prop="item.prop" :label="item.label"
        :show-overflow-tooltip="item.showOverflowTooltip !== false" :width="item.width" :align="item.align ? item.align : 'left'">
        <template slot="header" slot-scope="{ column }">
          <div class="custom-table_header">
            <el-tooltip class="item" effect="dark" :content="column.label" placement="top">
              <div>{{ column.label }}</div>
            </el-tooltip>
          </div>
        </template>
        <template slot-scope="scope">
          <div v-if="item.prop == 'sort'">
            <div>
              {{ (scope.$index + 1).toString().padStart(2, "0") }}
            </div>
          </div>
          <div v-else>
            <el-tooltip :content="scope.row[item.prop]" placement="top" :disabled="!item.showOverflowTooltip">
              <span class="cell-text">{{ scope.row[item.prop] }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "scmp-table",
  props: {
    tableColumn: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    rowNum: {
      type: Number,
      default: 3,
    },
    indicesGrouping: {
      type: Boolean,
      default: false,
    },
    idKey: {  // 新增prop，用于指定ID字段名
      type: String,
      default: 'id'  // 默认使用'id'字段
    },
    areaName: {
      type: String,
      default: ''
    },
    isMeeting: {
      type: [String, Number],
      default: undefined
    }
  },
  methods: {
    // 跳转详情
    goDataDetail(id) {
      this.$router.push({
        path: '/dataDetailTemplate',
        query: {
          proposalId: id,
          areaName: this.areaName,
          isMeeting: this.isMeeting
        }
      });
    },

    // 处理行点击事件
    handleRowClick(row) {
      console.log('表格组件收到点击:', row);
      // 触发父组件的 row-click 事件
      this.$emit('row-click', row);

      // 原有的提案数据处理逻辑
      const id = row.proposalId;
      if (id) {
        this.goDataDetail(id);
      }
    },

    setRowClassName({ rowIndex }) {
      return rowIndex % 2 === 0 ? "evenRowClassName" : "oddRowClassName";
    },
  },
};
</script>
<style lang="less" scoped>
.scmp-eltable {
  padding: 21px 39px 24px 43px;

  ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 10px;
    background-color: rgba(0, 25, 63, 0.3);
  }

  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: rgba(0, 147, 255, 0.8);
    border-radius: 5px;
  }

  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    background-color: rgba(0, 25, 63, 0.3);
  }

  .el-table {
    border: none !important;
  }

  ::v-deep .el-table__row {
    cursor: pointer;
  }

  ::v-deep .headerRowClassName {
    height: 100px; /* 增加表头高度 */
    background: linear-gradient(360deg,
        rgba(90, 211, 251, 0.5) 0%,
        rgba(90, 211, 251, 0) 100%);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;
    border-top: 0;
    border-right: 0;
    border-image: linear-gradient(180deg,
        rgba(90, 211, 251, 0),
        rgba(90, 211, 251, 1)) 1 1;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 30px;
    color: #ffffff;
    line-height: 80px; /* 增加行高,与表头高度接近 */
    font-style: normal;
    text-transform: none;
  }

  ::v-deep .el-table th.el-table__cell {
    background-color: transparent !important;
  }

  ::v-deep .el-table,
  .el-table__expanded-cell {
    background-color: transparent !important;
  }

  ::v-deep .el-table tr {
    background-color: rgba(54, 94, 114, 0.24) !important;
    height: 96px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 30px;
    color: #ffffff;
    font-style: normal;
    text-transform: none;
  }

  ::v-deep .el-table .cell {
    line-height: inherit;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 10px; /* 添加左右内边距 */
    box-sizing: border-box;
    max-width: 100%; /* 确保不超出最大宽度 */
  }

  ::v-deep .el-table__body,
  .el-table__footer,
  .el-table__header {
    height: 80px !important;
  }

  ::v-deep .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border: 0 !important;
  }

  ::v-deep .el-table__row--striped td {
    background-color: rgba(54, 94, 114, 0.5) !important;
    /* 使用!important来确保样式覆盖 */
    border-right: 1px solid rgba(90, 211, 251, 0.3); /* 添加单元格右边框 */
  }

  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    background-color: transparent !important;
  }

  ::v-deep .el-table .el-table__cell {
    padding: 0;
    border-right: 1px solid rgba(90, 211, 251, 0.3); /* 添加单元格右边框，防止内容重叠 */
  }

  ::v-deep .el-table__empty-text {
    color: #fff;
    font-size: 32px;
  }

  ::v-deep .el-table__empty-block {
    height: auto !important;
  }

  .headerCellClassName {
    font-size: 30px; /* 增大字体大小与headerRowClassName一致 */
    font-family: PingFang SC, PingFang SC-Medium;
    font-weight: 500;
    color: #ebefff;
    text-align: center;
    vertical-align: middle;

    .cell {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .el-table::before {
    //去除底部白线
    left: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 0px !important;
  }

  .evenRowClassName {
    height: 80px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: center;

    color: #6eb7f0;
    line-height: 80px;
    background-color: transparent !important;
  }

  ::v-deep .oddRowClassName {
    height: 80px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: center;
    line-height: 80px;
  }

  .cell-text {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
  }

  .custom-table_header {
    width: fit-content;

    display: inline-block;
  }
}
</style>
