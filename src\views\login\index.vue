<template>
  <div class="page login-page">
    <div class="container">
      <div class="form-holder has-shadow">
        <div class="row">
          <!-- 左侧图片区域 -->
          <div class="col-lg-6">
            <div class="info">
              <!-- <img src="../../assets/login_images/background.jpg" alt="Login Image" class="img-fluid" /> -->
            </div>
          </div>

          <!-- 右侧登录表单区域 -->
          <div class="col-lg-6 bg-white">
            <div class="form">
              <div class="content wrap-info">
                <div class="logo">
                  <p>欢迎使用!</p>
                </div>
                <h1>{{ title }}</h1>

                <!-- Element-UI 表单 -->
                <el-form
                  ref="loginForm"
                  :model="loginForm"
                  :rules="loginRules"
                  label-position="left"
                  class="login-form"
                >
                  <!-- 用户名输入 -->
                  <el-form-item prop="userName">
                    <el-input
                      v-model.trim="loginForm.userName"
                      placeholder="请输入用户名"
                      prefix-icon="el-icon-user"
                      tabindex="1"
                    />
                  </el-form-item>

                  <!-- 密码输入 -->
                  <el-form-item prop="password">
                    <el-input
                      v-model.trim="loginForm.password"
                      :type="passwordType"
                      placeholder="请输入密码"
                      prefix-icon="el-icon-lock"
                      tabindex="2"
                      show-password
                    />
                  </el-form-item>

                  <!-- 验证码输入 -->
                  <el-form-item prop="code">
                    <el-input
                      v-model.trim="loginForm.code"
                      placeholder="请输入验证码"
                      prefix-icon="el-icon-picture"
                      tabindex="3"
                      style="width: 70%; float: left;"
                      @keyup.enter.native="handleLogin"
                    />
                    <!-- <div class="captcha_code" style="width: 80px; margin: 6px 5px; float: left; margin-left: 6%;">
                      <img :src="imgData" @click="changeCode" style="width: 100%; cursor: pointer;" alt="验证码" />
                    </div> -->
                  </el-form-item>

                  <!-- 登录按钮 -->
                  <el-button
                    :loading="loading"
                    type="primary"
                    class="login-btn"
                    style="width: 100%; height: 40px;"
                    @click.native.prevent="handleLogin"
                  >
                    登录
                  </el-button>

                  <!-- 底部链接 -->
                  <div class="tips" style="margin-top: 10px; text-align: right;">
                    <el-link type="primary" @click="handleForget">忘记密码</el-link>
                    <el-link type="primary" style="margin-left: 20px;" @click="handleMobile">手机登录</el-link>
                    <el-link type="primary" style="margin-left: 20px;" @click="handleCode">粤政易扫码登录</el-link>
                  </div>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <forget-fram ref="forgetFram"></forget-fram>
    <!-- <mobile-fram ref="mobileFram"></mobile-fram> -->
  </div>
</template>

<script>
import { isPassword } from "@/utils/validate";
import { getCaptcha } from "@/api/captcha";
import ForgetFram from "../common/forget";
// import mobileFram from "../common/mobile";


export default {
  components: {
    ForgetFram,
    // mobileFram,
  },
  name: 'LoginPage',
  data() {
    const validateUserName = (rule, value, callback) => {
      if ("" == value) {
        callback(new Error("用户名不能为空"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!isPassword(value)) {
        callback(new Error("密码不能少于6位"));
      } else {
        callback();
      }
    };
    const validateCode = (rule, value, callback) => {
      if ("" == value) {
        callback(new Error("验证码不能为空"));
      } else {
        callback();
      }
    };
    return {
      title: '登录系统', // 页面标题
      // loginForm: {
      //   userName: '', // 用户名
      //   password: '', // 密码
      //   code: '', // 验证码
      // },
      loginForm: {
        userName: "lhc",
        // userName: "licaihui",
        password: "gzrd@2021",
        code: "5986",
        key: "e8963082bb8f4d698bdcca07c9f201fc",
      },
      loginRules: {
        userName: [
          {
            required: true,
            trigger: "blur",
            validator: validateUserName,
          },
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            validator: validatePassword,
          },
        ],
        code: [
          {
            required: true,
            trigger: "blur",
            validator: validateCode,
          },
        ],
      },
      passwordType: 'password', // 密码输入框类型
      imgData: '', // 验证码图片
      loading: false, // 登录按钮加载状态
    };
  },
  methods: {
    changeCode() {
      let that = this;
      getCaptcha().then((res) => {
        if (res.code == 200) {
          that.loginForm.key = res.data.key;

          let file = res.data.image; // 把整个base64给file
          var type = "image/png"; // 定义图片类型（canvas转的图片一般都是png，也可以指定其他类型）
          let conversions = that.base64ToBlob(file, type); // 调用base64转图片方法----vue写法
          // window.URL = window.URL || window.webkitURL; //blob对象转换为blob-url
          var url = window.URL.createObjectURL(conversions);
          that.imgData = url;
        }
      });
    },

    /**
     * base64转blob
     * 原理：利用URL.createObjectURL为blob对象创建临时的URL
     */
    base64ToBlob(urlData, type) {
      let arr = urlData.split(",");
      let mime = arr[0].match(/:(.*?);/)[1] || type;
      // 去掉url的头，并转化为byte
      let bytes = window.atob(arr[1]);
      // 处理异常,将ascii码小于0的转换为大于0
      let ab = new ArrayBuffer(bytes.length);
      // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
      let ia = new Uint8Array(ab);
      for (let i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ab], {
        type: mime,
      });
    },

    async handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
            .dispatch("user/login", this.loginForm)
            .then(() => {
              const routerPath = this.redirect === "/404" ? "/" : this.redirect;
              this.$router.push({ path: "/region" }).catch((res) => {
                console.log("🤗🤗🤗, res =>", res);
              });
              this.loading = false;
            })
            .catch((error) => {
              console.log(error);
              if (error.message != "验证码不匹配") {
                this.changeCode();
              }
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },

    // 处理登录
    // handleLogin() {
    //   this.$refs.loginForm.validate((valid) => {
    //     if (valid) {
    //       this.loading = true;
    //       // 模拟登录请求
    //       setTimeout(() => {
    //         this.loading = false;
    //         this.$message.success('登录成功');
    //       }, 1000);
    //     } else {
    //       this.$message.error('请填写完整信息');
    //     }
    //   });
    // },
    
    // 切换验证码
    changeCode() {
      // this.imgData = `https://via.placeholder.com/80x30?t=${Date.now()}`;
      this.imgData = ``;
    },
    // 忘记密码
    handleForget() {
      this.$message.info('忘记密码功能暂未开放');
      // this.$refs["forgetFram"].handleShow("1");
    },
    // 手机登录
    handleMobile() {
      this.$message.info('手机登录功能暂未开放');
      // this.$refs["mobileFram"].handleShow("1");
    },
    // 粤政易扫码登录
    handleCode() {
      // this.$message.info('粤政易扫码登录功能暂未开放');
      window.location.replace(
        "http://xtbg.gdzwfw.gov.cn/gzsrd/lz/ilzpc/base/api/v1/wechat/wechatsmallapp/yzyLogin"
      );
    },
  },
};
</script>

<style scoped>
/* 基础样式 */
.page.login-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: auto;
  min-height: 100vh;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.form-holder {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col-lg-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 15px;
  box-sizing: border-box;
  background: url("~@/assets/login_images/background.jpg") center center
    no-repeat;
  background-size: cover;
}

.info {
  /*background: #409eff;*/
  color: #fff;
  padding: 20px;
  text-align: center;
}

.bg-white {
  background: #fff;
}

.form {
  padding: 40px;
}

.logo {
  text-align: center;
  margin-bottom: 20px;
}

.logo p {
  font-size: 18px;
  color: #409eff;
}

h1 {
  text-align: center;
  font-size: 24px;
  margin-bottom: 20px;
}

.login-btn {
  margin-top: 20px;
}

.tips {
  margin-top: 10px;
  text-align: right;
}

.captcha_code img {
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 兼容性处理 */
@media (max-width: 768px) {
  .col-lg-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .info {
    display: none; /* 在小屏幕下隐藏左侧图片区域 */
  }

  .form {
    padding: 20px;
  }
}
</style>