<template>
  <div class="eventAnnouncement-page">
    <div class="eventAnnouncement-main_left">
      <ScmpCard cardName="活动目录">
        <div slot="main" class="eventAnnouncement-main_warp">
          <div class="item" :class="{ itemActive: index === activeIndex }" v-for="(item, index) in itemList"
            :key="index" @click="checkedItem(index, item)">
            {{ item.title }}
          </div>
        </div>
      </ScmpCard>
    </div>
    <div class="eventAnnouncement-main_right">
      <div class="header">
        <div>{{ activeNotice?.liaisonStationName || '' }}</div>
        <div>{{ activeNotice?.title || '' }}</div>
      </div>
      <div v-if="activeNotice.content">
        <p v-html="activeNotice.content">
        </p>
      </div>
      <div v-else class="pdf-box">
        <vue-pdf-embed :source="pdfsrc" class="pdf-viewer" />
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getLiaisonStationNotice, getLiaisonStationDetails, getLiaisonStationNoticeJSC } from "@/api/dataPageApi/liaisonStation";
import { API_CONFIG } from "@/main";
import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed';
export default {
  name: "eventAnnouncement-index",
  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    VuePdfEmbed
  },
  data() {
    return {
      itemList: [], // 活动目录列表
      activeIndex: 0, // 当前选中的活动目录索引
      activeNotice: null, // 当前选中的公告内容
      activeNoticeFile: null, // 当前选中的公告内容文件
      noticeUrl: '', // 公告文件路径
      // pdfsrc: 'https://zhrd.rd.gz.cn/idblz/public/#/pdf?filePath=file%2FliaisonStationNotice%2F2025%2F04%2F03%2F%E5%B9%BF%E5%B7%9E%E5%B8%82%E6%B5%B7%E7%8F%A0%E5%8C%BA%E7%90%B6%E6%B4%B2%E8%A1%97%E9%81%93%E4%BA%BA%E5%A4%A7%E4%BB%A3%E8%A1%A8%E4%B8%AD%E5%BF%83%E8%81%94%E7%BB%9C%E7%AB%99%E6%8E%A5%E5%BE%85%E6%97%A5%E6%B4%BB%E5%8A%A8%E5%85%AC%E5%91%8A20250403172304.pdf'
      // pdfsrc: 'C:/Users/<USER>/Desktop/大会建议流程图（1.10）.pdf'
      // pdfsrc: 'http:/183.48.44.47/gzrd/uploadPath/大会建议流程图（1.10）.pdf'
      pdfsrc: '',
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.streetTownId = this.$route.query.streetTownId;
    this.liaisonStationId = this.$route.query.liaisonStationId;
    // this.embedPDF("https://pdfobject.com/pdf/sample-3pp.pdf")
  },
  mounted() {
    this.getLiaisonStationNotice(this.administrativeAreaId, this.streetTownId, this.liaisonStationId);
  },
  methods: {
    embedPDF(pdfsrc) {
      const options = {
        width: "600px",
        height: "500px",
      };
      // replace 'example.pdf' with your actual PDF file path
      pdf.embed(pdfsrc, this.$refs.pdf, options);
    },
    getLiaisonStationNotice(administrativeAreaId, streetTownId, liaisonStationId) {
      const params = {
        pageNum: 1,
        pageSize: 10,
        title: '',
        liaisonStationId: liaisonStationId,
        administrativeAreaId: administrativeAreaId,
        streetTownId: streetTownId,
      };
      getLiaisonStationNoticeJSC(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.itemList = res.rows;
          // 默认选中第一条
          if (this.itemList.length > 0) {
            this.activeNotice = this.itemList[0]; // 设置第一条公告内容
            getLiaisonStationDetails(this.activeNotice.id)
              .then(res => {
                const fileUrl = res.data.furl;
                // this.pdfsrc= "http://localhost/%E5%A4%A7%E4%BC%9A%E5%BB%BA%E8%AE%AE%E6%B5%81%E7%A8%8B%E5%9B%BE%EF%BC%881.10%EF%BC%89.pdf";
                this.pdfsrc = API_CONFIG.PDF_VIEW_PREFIX + "/file/view?file=" + fileUrl;
                console.log("this.pdfsrc", this.pdfsrc);
              })
          }
          // this.embedPDF(this.pdfsrc);
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    checkedItem(index, item) {
      this.activeIndex = index; // 更新选中的索引
      this.activeNotice = this.itemList[index]; // 更新选中的公告内容
      console.log("选中", index);
      if (!item.content) {
        getLiaisonStationDetails(this.activeNotice.id)
          .then(res => {
            const fileUrl = res.data.furl;
            console.log("API_CONFIG", API_CONFIG);
            this.pdfsrc = API_CONFIG.PDF_VIEW_PREFIX + "/file/view?file=" + fileUrl;
            // this.pdfsrc='https://rdtest.rd.gz.cn/ilzss/file/view?file=file/liaisonStationNotice/2025/05/08/广州市越秀区洪桥街道代表人中心联络站接待日活动公告20250508201141.pdf';
            console.log("this.pdfsrc", this.pdfsrc);
            // this.pdfsrc= "http://localhost/%E5%A4%A7%E4%BC%9A%E5%BB%BA%E8%AE%AE%E6%B5%81%E7%A8%8B%E5%9B%BE%EF%BC%881.10%EF%BC%89.pdf";

          })
          .catch(error => {

          })

      }
    },
  },
};
</script>

<style lang="less" scoped>
.eventAnnouncement-page {
  width: calc(100%);
  height: calc(100vh - 500px);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  color: #fff;
  font-size: 60px;
  position: relative;

  .eventAnnouncement-main_left {
    position: absolute;
    height: 1446px;
    width: 843px;

    .eventAnnouncement-main_warp {
      height: 1531px;
      width: 743px;
      padding: 5px 45px 0;
      z-index: 999;
      position: relative;
      overflow-y: auto;
      overflow-x: hidden; // 新增这行，隐藏X轴滚动条
  
      &::-webkit-scrollbar {
        width: 10px !important;
        background-color: rgba(0, 25, 63, 0.3);
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 147, 255, 0.8);
        border-radius: 5px;
        border: 2px solid rgba(0, 25, 63, 0.3);
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 25, 63, 0.3);
        border-radius: 5px;
      }

      &::-webkit-scrollbar-corner {
        background: rgba(0, 25, 63, 0.3);
      }

      .item {
        height: 120px;
        width: 750px;
        // font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 34px;
        color: #ffffff;
        line-height: 120px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        cursor: pointer; // 添加鼠标指针样式
        // 单行文本缩略点展示
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .itemActive {
        background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
        border-radius: 3px 3px 3px 3px;
      }
    }
  }

  .eventAnnouncement-main_right {
    position: absolute;
    height: 1522px;
    width: 2483px;
    right: 0;
    background: url("@/assets/image/tzgg_bg.png") no-repeat;
    background-size: 100% 100%;
    padding: 0 81px 120px 99px;
    overflow-y: auto;
    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;

    /* IE 和 Edge */
    &::-webkit-scrollbar {
      display: none;
      /* Chrome、Safari 和 Opera */
    }

    .header {
      width: 1718px;
      height: 131px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 50px;
      color: #ffffff;
      line-height: 100px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin: 20px auto 90px;
    }

    p {
      width: 2483px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      line-height: 60px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}

.pdf-box {
  width: 100%;
  height: 1350px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: #f5f5f5;
  border: 1px solid #ddd;
  overflow-y: auto;
  overflow-x: hidden;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

.pdf-viewer {
  width: 100%;
  height: auto;
  min-height: 100%;
}
</style>