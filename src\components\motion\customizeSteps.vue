<template>
  <div class="steps-warp">
    <div class="steps-header">
      <div class="icon">
        <div></div>
      </div>
      <div class="text" @click="goDataDetail(data.proposalId)">
        {{ header + '（' + headPer +'）'  }}
      </div>
    </div>
    <div class="steps-main"  @click="goDataDetail(data.proposalId)">
      <el-steps :space="stepSpace" :active="data.active">
        <el-step :title="item" v-for="(item, index) in data.titleList" :key="index">
          <template slot="icon">
            <img v-if="index == 0" class="steps-node" :src="require('@/assets/image/steps_first.png')" alt="" />
            <img v-else-if="index != 0 && index < data.active" class="steps-node"
              :src="require('@/assets/image/steps_success.png')" alt="" />

            <img v-else-if="index != 0 && index == data.active" class="steps-node"
              :src="require('@/assets/image/steps_underway.png')" alt="" />

            <img v-else-if="index != 0 && index > data.active" class="steps-node"
              :src="require('@/assets/image/steps_wait.png')" alt="" />
          </template>
        </el-step>
      </el-steps>
    </div>
  </div>
</template>
<script>
export default {
  name: "wordCloud",
  props: {
    // 标题
    header: {
      type: String,
      default: "",
    },
    headPer: {
      type: String,
      default: "",
    },
    areaName: {
      type: String,
      default: "",
    },
    data: {
      type: Object,
      default: () => { },
    },
    isMeeting: {
      type: [String, Number],
      default: undefined
    },
  },
  data() {
    return {

    }
  },
  methods: {
    // 跳转详情
    goDataDetail() {
      this.$router.push({
        path: '/dataDetailTemplate',
        query: {
          proposalId: this.data.proposalId,
          areaName: this.areaName,
          isMeeting: this.isMeeting
          // item: itemString,
          // colum: columString,
          // title: title + '-' + name
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
.steps-warp {
  height: auto; /* 修改：固定高度改为自适应 */
  min-height: 272px; /* 保留最小高度防止内容过少时容器过矮 */

  .steps-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-top: 38px;
    cursor: pointer;

    .icon {
      margin-right: 31px;
      width: 42px;
      height: 42px;
      background: rgba(137, 164, 255, 0.5);
      box-shadow: 0px 0px 6px 4px rgba(108, 176, 255, 0.65);
      border-radius: 0px 0px 0px 0px;
      border-radius: 21px;
      display: flex;
      align-items: center;
      justify-content: center;

      //   padding: 8px;
      div {
        width: 26px;
        height: 26px;
        background: #b8eaff;
        box-shadow: 0px 0px 5px 1px rgba(255, 255, 255, 0.85);
        border-radius: 0px 0px 0px 0px;
        border-radius: 13px;
      }
    }

    .text {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 46px;
      color: #ffffff;
      line-height: 56px; /* 增加行高适配换行 */
      text-align: left;
      font-style: normal;
      text-transform: none;
      max-width: calc(100% - 73px); /* 计算宽度：总宽度 - 图标宽度(42px+边距31px) */
      word-wrap: break-word; /* 关键：允许长单词/连续字符换行 */
    }
  }

  .steps-main {
    margin-left: 12px;
    margin-top: 30px; /* 调整：减少顶部边距适配换行后的标题高度 */
    cursor: pointer;

    .steps-node {
      width: 56px;
      height: 56px;
    }

    ::v-deep .el-step__title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #ffffff;
      line-height: 30px;
      text-align: center; /* 文字居中对齐 */
      font-style: normal;
      text-transform: none;
      max-width: 90px; /* 限制宽度，让超过3个字的文本换行 */
      word-wrap: break-word;
      white-space: normal; /* 允许文字换行 */
      overflow-wrap: break-word; /* 强制换行 */
      hyphens: auto; /* 自动断词 */
    }

    ::v-deep .el-step__main {
      margin-left: -45px; /* 调整左边距，确保文字居中对齐到节点 */
      text-align: center;
    }

    ::v-deep .el-step.is-horizontal .el-step__line {
      height: 24px;
      top: 18px;
      background: #e8f2fa;
    }

    ::v-deep .el-step__head.is-finish {
      .el-step__line {
        background: #32b48a;
        border-color: #32b48a;
      }
    }
  }
}
</style>

>
