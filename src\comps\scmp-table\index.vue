<template>
  <div class="scmp-eltable">
    <el-table
      :data="tableData"
      class="table-content mb15"
      :stripe="true"
      header-row-class-name="headerRowClassName"
      header-cell-class-name="headerCellClassName"
      :row-class-name="setRowClassName"
      :height="rowNum * 99"
    >
      <el-table-column
        v-for="(item, index) in tableColumn"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        show-overflow-tooltip
        :width="item.width"
        :align="item.align ? item.align : 'left'"
      >
        <template slot="header" slot-scope="{ column }">
          <div class="custom-table_header">
            <div>{{ column.label }}</div>
          </div>
        </template>
        <template slot-scope="scope">
          <div v-if="item.prop == 'sort'">
            <div>
              {{ (scope.$index + 1).toString().padStart(2, "0") }}
            </div>
          </div>
          <div class="table_col" v-else>
            {{ scope.row[item.prop] }}
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  name: "scmp-table",
  props: {
    // 表格header
    tableColumn: {
      type: Array,
      default: () => [],
    },
    // 表格数据
    tableData: {
      type: Array,
      default: () => [],
    },
    // 可视行数
    rowNum: {
      type: Number,
      default: 3,
    },
    indicesGrouping: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    setRowClassName({ rowIndex }) {
      // console.log(rowIndex);
      return rowIndex % 2 === 0 ? "evenRowClassName" : "oddRowClassName";
    },
  },
};
</script>
<style lang="less" scoped>
.scmp-eltable {
  padding: 21px 39px 24px 43px;
  .el-table__body-wrapper::-webkit-scrollbar {
    /*width: 0;宽度为0隐藏*/
    width: 0px;
  }
  .el-table {
    border: none !important;
  }

  ::v-deep .headerRowClassName {
    height: 80px;
    background: linear-gradient(
      360deg,
      rgba(90, 211, 251, 0.5) 0%,
      rgba(90, 211, 251, 0) 100%
    );
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;
    border-top: 0;
    border-right: 0;
    border-image: linear-gradient(
        180deg,
        rgba(90, 211, 251, 0),
        rgba(90, 211, 251, 1)
      )
      1 1;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 30px;
    color: #ffffff;
    line-height: 21px;
    font-style: normal;
    text-transform: none;
  }
  ::v-deep .el-table th.el-table__cell {
    background-color: transparent !important;
  }
  ::v-deep .el-table,
  .el-table__expanded-cell {
    background-color: transparent !important;
  }
  ::v-deep .el-table tr {
    background-color: rgba(54, 94, 114, 0.24) !important;
    height: 96px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 30px;
    color: #ffffff;
    font-style: normal;
    text-transform: none;
  }
  ::v-deep .el-table .cell {
    line-height: inherit;
    overflow: visible;
  }
  ::v-deep .el-table__body,
  .el-table__footer,
  .el-table__header {
    height: 80px !important;
  }
  ::v-deep .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border: 0 !important;
  }
  ::v-deep .el-table__row--striped td {
    background-color: rgba(
      54,
      94,
      114,
      0.5
    ) !important; /* 使用!important来确保样式覆盖 */
  }
  ::v-deep
    .el-table--enable-row-hover
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background-color: transparent !important;
  }
  ::v-deep .el-table .el-table__cell {
    padding: 0;
  }
  ::v-deep .el-table__empty-text {
    color: #fff;
    font-size: 32px;
  }
  ::v-deep .el-table__empty-block {
    height: auto !important;
  }
  .headerCellClassName {
    font-size: 16px;
    font-family: PingFang SC, PingFang SC-Medium;
    font-weight: 500;
    color: #ebefff;
    text-align: center;
    .cell {
      display: inline-block;
    }
  }
  .el-table::before {
    //去除底部白线
    left: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 0px !important;
  }

  .evenRowClassName {
    height: 80px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: center;

    color: #6eb7f0;
    line-height: 80px;
    background-color: transparent !important;
  }
  ::v-deep .oddRowClassName {
    height: 80px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: center;
    line-height: 80px;
  }
  .table_col {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .custom-table_header {
    width: fit-content;

    display: inline-block;
  }
}
</style>
