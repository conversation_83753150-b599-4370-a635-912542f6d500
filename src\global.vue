<script type="text/javascript">
import store from "./stores";


// const basePath_1 = "https://rdtest.rd.gz.cn/ilzss"; //服务器
// const basePath_2 = "https://rdtest.rd.gz.cn/ilzss"; //服务器
// const basePath_3 = "https://rdtest.rd.gz.cn/yajy/api/v1"; //服务器
//代表履职配置
const basePath_1 = "http://127.0.0.1:8083/ilz/api/v1"; //服务器
const basePath_2 = "http://127.0.0.1:8083/ilz/api/v1"; //服务器
//建议议案配置
// const basePath_1 = "http://127.0.0.1:8082/ilz/api/v1"; //服务器
// const basePath_2 = "http://127.0.0.1:8082/ilz/api/v1"; //服务器
const basePath_3 = "http://127.0.0.1:8082/api/v1"; //本地
// const basePath_3 = ""; //本地
const basePath_4 = "https://rdtest.rd.gz.cn/idblz/#"; //本地
const basePath_5 = "https://rdtest.rd.gz.cn/lzpc/#"; //本地

const token = store.getters.accessToken;

export default {
  basePath_1,
  basePath_2,
  basePath_3,
  basePath_4,
  basePath_5,
  token,
};
</script>
