<template>
  <div class="basicInformation-page">
    <!-- <div class="basicInformation-main_content">
      <div class="content_top">
        <img class="left" :src="require('@/assets/icon/icon_yjzj.png')" alt="" />
        <div>
          《广州市中小学生心理健康促进条例（草案修改稿·征求意见稿）》向社会各界公开征求意见
        </div>
      </div>
      <div class="content_bottom">
        <div class="content_bottom_left">
          <div class="qrcode">
            <canvas ref="qrCanvas"></canvas>
          </div>
          <div class="text">扫码参与立法</div>
        </div>
        <div class="line"></div>
        <div class="content_bottom_right">
          开始时间:2024-06-15 结束时间:2024-07-13
          来源:广州市人大常委会法工委议。
          《广州市中小学生心理健康促进条例》拟于2024年7月下旬进行第三次审议并交付表决。为践行全过程人民民主，广泛听取民意，做好该法规案的审议工作，现公开征求广大市民和社会各界对《广州市中小学生心理健康促进条例(草案修改稿)》的意见和建议。请于7月13日前扫描二维码填写意见。
        </div>
      </div>
    </div> -->
    <div class="basicInformation-main_content" v-for="(item, index) in surveyList" :key="index">
      <div class="content_top">
        <img class="left" :src="require('@/assets/icon/icon_yjzj.png')" alt="" />
        <div>
          {{ item.surveyName }}
        </div>
      </div>
      <div class="content_bottom">
        <div class="content_bottom_left">
          <!-- <div class="qrcode">
            <canvas ref="qrCanvas"></canvas>
          </div> -->
          <div class="qrcode">
            <!-- 使用动态ref绑定每个canvas -->
            <canvas :ref="'qrCanvas_' + index"></canvas>
          </div>
          <div class="text">扫码参与立法</div>
        </div>
        <div class="line"></div>
        <div class="content_bottom_right">
          开始时间:{{ item.startTime }} 结束时间:{{ item.endTime }}
          来源:{{ item.organization }}。
          {{ item.surveyDesc }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import QRCode from "qrcode";
import { mapGetters } from "vuex";
import {
  getSurveyList,
  collectionOfOpinions
} from "@/api/dataPageApi/liaisonStation";
export default {
  name: "basicInformation-index",
  components: {},
  data() {
    return {
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],
      qrText: "",
      surveyList: [],
      administrativeAreaId: ''
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.collectionOfOpinions()
  },
  mounted() {
    // this.generateQRCode();          
  },
  watch: {},
  methods: {
    collectionOfOpinions() {
      const params = {
        pageNum: 1,
        pageSize: 10,
        status: 1,
      }
      collectionOfOpinions(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // this.surveyList = res.rows
          this.surveyList = res.data
          this.$nextTick(() => {
            this.generateAllQRCodes();
          });
        })
    },
    generateQRCode(id) {
      QRCode.toCanvas(this.$refs.qrCanvas, id, (error) => {
        if (error) console.error(error);
        console.log("QR code generated!");
      });
      // QRCode.toCanvas(this.$refs.qrCanvas1, this.qrText, (error) => {
      //   if (error) console.error(error);
      //   console.log("QR code generated!");
      // });
      // QRCode.toCanvas(this.$refs.qrCanvas2, this.qrText, (error) => {
      //   if (error) console.error(error);
      //   console.log("QR code generated!");
      // });
    },
    generateAllQRCodes() {
      this.surveyList.forEach((item, index) => {
        if (item.surveyId) {
          this.generateQRCode(item.surveyId, index);
        }
      });
    },
    // 修改generateQRCode方法，接收index参数
    generateQRCode(text, index) {
      let qrText = 'https://rdtest.rd.gz.cn/idblz/#/preview-erweima?surveyId=' + text + '&isYL=false';
      const canvasRef = 'qrCanvas_' + index;
      if (this.$refs[canvasRef] && this.$refs[canvasRef][0]) {
        QRCode.toCanvas(this.$refs[canvasRef][0], qrText, (error) => {
          if (error) {
            console.error("生成二维码失败:", error);
            return;
          }
          // console.log(`二维码${index}生成成功!`);
        });
      } else {
        // console.warn(`Canvas元素${index}未找到`);
      }
    },
    checkedItem(obj) {
      console.log("选中", obj);
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/fonts/YouSheBiaoTiHei.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansCN';
  src: url('@/assets/fonts/SourceHanSansCN-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansCN';
  src: url('@/assets/fonts/SourceHanSansCN-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

.basicInformation-page {
  width: 100%;
  /*height: calc(100vh - 500px);*/
  height: 105%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;
  overflow: auto;

  /* Webkit浏览器滚动条样式 */
  &::-webkit-scrollbar {
    height: 8px; // 滚动条高度
    background: rgba(0, 25, 63, 0.2); // 轨道背景
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg,
        rgba(0, 25, 63, 0) 0%,
        rgba(10, 108, 222, 0.8) 50%,
        rgba(0, 25, 63, 0) 100%);
    border-radius: 4px;
    border: 1px solid rgba(10, 108, 222, 0.5); // 添加边框增强科技感
    /*box-shadow: 0 0 5px rgba(78, 153, 248, 0.5); // 发光效果*/
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg,
        rgba(0, 25, 63, 0) 0%,
        rgba(10, 150, 255, 0.9) 50%,
        rgba(0, 25, 63, 0) 100%);
  }

  .basicInformation-main_content {
    width: 100%;
    // margin-top: 80px;
    margin-bottom: 25px;
    position: relative;
    z-index: 9;

    .content_top {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 8px 52px 0 32px;

      img {
        width: 113px;
        height: 121.86px;
      }

      div {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 50px;
        color: #ffffff;
        line-height: 59px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .content_bottom {
      margin-top: 46px;
      width: 3499px;
      height: 444px;
      display: flex;
      flex-direction: row;
      background: linear-gradient(90deg,
          rgba(0, 25, 63, 0) 0%,
          rgba(10, 108, 222, 0.47) 53%,
          rgba(0, 25, 63, 0) 100%);
      border-radius: 0px 0px 0px 0px;

      .content_bottom_left {
        width: 516px;
        margin-left: 136px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .text {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          margin-top: 22px;
          text-shadow: 0 0 10px rgb(78, 153, 248);
        }
      }

      .line {
        width: 13px;
        height: 433px;
        margin: 0 95px 0 114px;
        background: url("@/assets/image/line.png") no-repeat;
        background-size: contain;
      }

      .content_bottom_right {
        padding: 75px 158px 88px 0;
        width: 2722px;
        height: 280px;
        font-family: SourceHanSansCN, "Source Han Sans CN";
        // font-weight: 500;
        font-size: 40px;
        color: #ffffff;
        line-height: 65px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        // 字间距1px
        letter-spacing: 1px;
      }
    }
  }

  .qrcode {
    margin-top: 22px;
    width: 189.59px;
    height: 189.59px;
    background: url("@/assets/image/qrcode.png") no-repeat;
    background-size: 100% 100%;
    padding: 60px;

    canvas {
      width: 189.59px !important;
      height: 189.59px !important;
      border-radius: 0px 0px 0px 0px;
    }
  }
}
</style>
