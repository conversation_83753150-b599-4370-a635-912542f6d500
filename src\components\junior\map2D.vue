<template>
  <div ref="loginTimes"></div>
</template>
<script>
// 修改src\components\mapJson下的文件名来更换地址
// import geoJson from "../mapJson/440184.json";
import * as echarts from "echarts";
export default {
  name: "wordCloud",
  props: {
    // 标题
    data: {
      type: Array,
      default: () => [],
    },
    code: {
      type: String,
      default: "",
    },
    areaName: {
      type: String,
      default: "",
    },
    mapAddress: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      chart: null,
      geoJson: null
    };
  },
  mounted() {
    console.log("Map2D mounted, code:", this.code);
    // 如果code存在就直接初始化，否则等待watch触发
    if (this.code) {
      this.loadGeoJson(this.code);
    }
  },
  watch: {
    code: {
      handler(newVal, oldVal) {
        console.log("Code changed from", oldVal, "to", newVal);
        if (newVal && newVal !== oldVal) {
          // 清理之前的图表
          if (this.chart) {
            this.chart.dispose();
            this.chart = null;
          }
          // 重新加载地图数据
          this.loadGeoJson(newVal);
        }
      },
      immediate: true
    }
  },
  methods: {
    loadGeoJson(areaCode) {
      // 检查areaCode是否有效
      if (!areaCode) {
        console.warn("areaCode为空，跳过地图初始化");
        return;
      }
      
      try {
        // Webpack 会将 ../mapJson/${areaCode}.json 打包为动态模块
        const geoData = require(`../mapJson/${areaCode}.json`);
        this.geoJson = geoData;
        // 添加控制台输出，显示加载的区域代码及数据
        console.log("成功加载地理数据，区域代码：", areaCode, "数据内容：", this.geoJson);
        this.getIint();
      } catch (error) {
        console.error("加载 JSON 失败:", error);
      }
    },
    getIint() {
      // 添加控制台输出，显示地图初始化开始时间
      console.log("地图初始化开始，当前时间：", new Date().toISOString());

      // 获取到ref绑定为loginTimes的DOM节点，以canvas的形式展现在视图层
      let myChart = this.$echarts.init(this.$refs.loginTimes);
      // 保存图表实例
      this.chart = myChart;

      let data = this.geoJson.features.map((item) => {
        return {
          name: item.properties.name,
        };
      });
      const resData = {
        status: [
          { name: "从化区", level: "0" },
        ],
      };

      var convertData = function () {
        var res = [];
        return res;
      };
      const regions = resData.status.map((element) => {
        const { name } = element;
        return {
          name,
          itemStyle: {
            normal: {
              areaColor: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  { offset: 0, color: "rgba(1, 102, 204, 1)" },
                  { offset: 1, color: "rgba(21, 173, 250, 1)" },
                ],
                global: false,
              },
            },
          },
          label: {
            show: true,
            color: '#fff',
            fontSize: 26,
          }
        };
      });
      // 根据 code 判断是否展示当前镇街数据（440184 时展示）
      let toolTipData = [];
      let geoCoordMap = {};
      if (this.code === '440184') {
        // 从化区
        toolTipData = [
          { name: "太平镇" },
          { name: "温泉镇" },
          { name: "鳌头镇" },
          { name: "吕田镇" },
          { name: "良口镇" },
          { name: "街口街道" },
          { name: "江浦街道" },
          { name: "城郊街道" },
          { name: "流溪河林场" },
          { name: "黄龙带水库管理处" },
        ];
        geoCoordMap = {
          太平镇: [113.501494, 23.47145],
          温泉镇: [113.692106, 23.61340],
          鳌头镇: [113.399111, 23.63211],
          吕田镇: [113.920222, 23.85666],
          良口镇: [113.724167, 23.72416],
          街口街道: [113.562222, 23.53333],
          江浦街道: [113.629999, 23.50043],
          城郊街道: [113.550222, 23.64666],
          流溪河林场: [113.792106, 23.75340],
          黄龙带水库管理处: [113.725521, 23.81340],
        };

      } else if (this.code === '440183') {
        // 增城区（需要补充实际镇街数据）
      toolTipData = [
        { name: "荔城街道" },
        { name: "增江街道" },
        { name: "朱村街道" },
        { name: "永宁街道" },
        { name: "新塘镇" },
        { name: "仙村镇" },
        { name: "石滩镇" },
        { name: "中新镇" },
        { name: "小楼镇" },
        { name: "派潭镇" },
        { name: "正果镇" }
      ];
      geoCoordMap = {
        "荔城街道": [113.79579, 23.330497], // 示例经纬度，需校准
        "增江街道": [113.8632, 23.2915], // 示例经纬度，需校准
        "朱村街道": [113.7088, 23.3011], // 示例经纬度，需校准
        "永宁街道": [113.6192, 23.2107], // 示例经纬度，需校准
        "新塘镇": [113.6497, 23.1398], // 示例经纬度，需校准
        "仙村镇": [113.7186, 23.1889], // 示例经纬度，需校准
        "石滩镇": [113.8077, 23.1703], // 示例经纬度，需校准
        "中新镇": [113.6279, 23.3712], // 示例经纬度，需校准
        "小楼镇": [113.7543, 23.3996], // 示例经纬度，需校准
        "派潭镇": [113.7734, 23.5209], // 示例经纬度，需校准
        "正果镇": [113.9245, 23.4218] // 示例经纬度，需校准
      };
      } else if (this.code === '440115') {
        // 南沙区（需要补充实际镇街数据）
            toolTipData = [
          { name: "榄核镇" },
          { name: "东涌镇" },
          { name: "黄阁镇" },
          { name: "大岗镇" },
          { name: "南沙街道" },
          { name: "横沥镇" },
          { name: "珠江街道" },
          { name: "龙穴街道" },
          { name: "万顷沙镇" }
      ];
      geoCoordMap = {
          "榄核镇": [113.3512, 22.8555], // 示例经纬度，需校准
          "东涌镇": [113.4483, 22.8744], // 示例经纬度，需校准
          "黄阁镇": [113.5206, 22.8359], // 示例经纬度，需校准
          "大岗镇": [113.4247, 22.7888], // 示例经纬度，需校准
          "南沙街道": [113.5733, 22.7767], // 示例经纬度，需校准
          "横沥镇": [113.4872, 22.7441], // 示例经纬度，需校准
          "珠江街道": [113.5555, 22.7202], // 示例经纬度，需校准
          "龙穴街道": [113.6622, 22.6656], // 示例经纬度，需校准
          "万顷沙镇": [113.6322, 22.6133] // 示例经纬度，需校准
      };
      } else if (this.code === '440114') {
        // 花都区（需要补充实际镇街数据）
             toolTipData = [
        { name: "梯面镇" },
        { name: "狮岭镇" },
        { name: "花山镇" },
        { name: "花东镇" },
        { name: "赤坭镇" },
        { name: "新华街道" },
        { name: "炭步镇" },
        { name: "雅瑶镇" }
      ];
      geoCoordMap = {
        "梯面镇": [113.2778, 23.5664], // 示例经纬度，需校准
        "狮岭镇": [113.1679, 23.4712], // 示例经纬度，需校准
        "花山镇": [113.2845, 23.4778], // 示例经纬度，需校准
        "花东镇": [113.3803, 23.4611], // 示例经纬度，需校准
        "赤坭镇": [113.0444, 23.4186], // 示例经纬度，需校准
        "新华街道": [113.1866, 23.3901], // 示例经纬度，需校准
        "炭步镇": [113.0855, 23.3293], // 示例经纬度，需校准
        "雅瑶镇": [113.2177, 23.3652] // 示例经纬度，需校准
      };
      } else if (this.code === '440113') {
        // 番禺区（需要补充实际镇街数据）
      toolTipData = [
        { name: "石壁街道" },
        { name: "洛浦街道" },
        { name: "大石街道" },
        { name: "钟村街道" },
        { name: "沙头街道" },
        { name: "小谷围街道" },
        { name: "市桥街道" },
        { name: "东环街道" },
        { name: "大龙街道" },
        { name: "沙湾镇" },
        { name: "桥南街道" },
        { name: "新造镇" },
        { name: "石碁镇" },
        { name: "化龙镇" },
        { name: "石楼镇" },
        { name: "南村镇" }
      ];
      geoCoordMap = {
        "石壁街道": [113.2651, 23.0021],
        "洛浦街道": [113.2908, 23.0415],
        "大石街道": [113.3037, 23.0163],
        "钟村街道": [113.3137, 22.9893],
        "沙头街道": [113.3155, 22.9327],
        "小谷围街道": [113.3893, 23.0521],
        "市桥街道": [113.3666, 22.9466],
        "东环街道": [113.3596, 22.9766],
        "大龙街道": [113.3999, 22.9666],
        "沙湾镇": [113.3122, 22.8991],
        "桥南街道": [113.3755, 22.9155],
        "新造镇": [113.4212, 23.0312],
        "石碁镇": [113.4511, 22.9555],
        "化龙镇": [113.4647, 23.0401],
        "石楼镇": [113.5100, 22.9454],
        "南村镇": [113.3763, 23.0091],
      };
      } else if (this.code === '440112') {
        // 黄埔区（需要补充实际镇街数据）
      toolTipData = [
        { name: "九佛镇" },
        { name: "联和街道" },
        { name: "罗岗街道" },
        { name: "永和街道" },
        { name: "大沙街道" },
        { name: "东区街道" },
        { name: "荔联街道" },
        { name: "鱼珠街道" },
        { name: "文冲街道" },
        { name: "黄埔街道" },
        { name: "红山街道" },
        { name: "穗东街道" },
        { name: "南岗街道" },
        { name: "长洲街道" },
        { name: "夏港街道" }
      ];
      geoCoordMap = {
        "九佛镇": [113.5388, 23.3233], // 示例经纬度，需校准
        "联和街道": [113.4399, 23.1851], // 示例经纬度，需校准
        "罗岗街道": [113.4944, 23.2199], // 示例经纬度，需校准
        "永和街道": [113.5611, 23.1988], // 示例经纬度，需校准
        "大沙街道": [113.4644, 23.1422], // 示例经纬度，需校准
        "东区街道": [113.5088, 23.1511], // 示例经纬度，需校准
        "荔联街道": [113.5420, 23.1300], // 示例经纬度，需校准
        "鱼珠街道": [113.4372, 23.1247], // 示例经纬度，需校准
        "文冲街道": [113.4733, 23.1166], // 示例经纬度，需校准
        "黄埔街道": [113.4488, 23.0911], // 示例经纬度，需校准
        "红山街道": [113.4844, 23.1035], // 示例经纬度，需校准
        "穗东街道": [113.5026, 23.0800], // 示例经纬度，需校准
        "南岗街道": [113.5388, 23.0944], // 示例经纬度，需校准
        "长洲街道": [113.4100, 23.0751], // 示例经纬度，需校准
        "夏港街道": [113.5162, 23.0633] // 示例经纬度，需校准
      };
      } else if (this.code === '440111') {
        // 白云区（需要补充实际镇街数据）
      toolTipData = [
        { name: "钟落潭镇" },
        { name: "人和镇" },
        { name: "江高镇" },
        { name: "太和镇" },
        { name: "均禾街道" },
        { name: "永平街道" },
        { name: "嘉禾街道" },
        { name: "石井街道" },
        { name: "黄石街道" },
        { name: "同和街道" },
        { name: "京溪街道" },
        { name: "新市街道" },
        { name: "棠景街道" },
        { name: "松洲街道" },
        { name: "金沙街道" },
        { name: "同德街道" },
        { name: "三元里街道" },
        { name: "景泰街道" }
      ];
      geoCoordMap = {
        "钟落潭镇": [113.4089, 23.3637], // 调整后
        "人和镇": [113.2921, 23.3378], // 调整后
        "江高镇": [113.2112, 23.3065], // 调整后
        "太和镇": [113.3796, 23.2811], // 调整后
        "均禾街道": [113.2677, 23.2602], // 调整后
        "永平街道": [113.3105, 23.2418], // 调整后
        "嘉禾街道": [113.2766, 23.2233], // 调整后
        "石井街道": [113.2144, 23.2329], // 调整后
        "黄石街道": [113.2815, 23.2104], // 调整后
        "同和街道": [113.3233, 23.2012], // 调整后
        "京溪街道": [113.3244, 23.1861], // 调整后
        "新市街道": [113.2455, 23.2022], // 调整后
        "棠景街道": [113.2466, 23.1811], // 调整后
        "松洲街道": [113.2203, 23.1722], // 调整后
        "金沙街道": [113.1952, 23.1521], // 调整后
        "同德街道": [113.2294, 23.1611], // 调整后
        "三元里街道": [113.2737, 23.1600], // 调整后
        "景泰街道": [113.2688, 23.1729] // 调整后
      };
      } else if (this.code === '440106') {
        // 天河区（需要补充实际镇街数据）
      toolTipData = [
        { name: "凤凰街道" },
        { name: "龙洞街道" },
        { name: "元岗街道" },
        { name: "长兴街道" },
        { name: "新塘街道" },
        { name: "兴华街道" },
        { name: "五山街道" },
        { name: "沙东街道" },
        { name: "林和街道" },
        { name: "石牌街道" },
        { name: "棠下街道" },
        { name: "黄村街道" },
        { name: "珠吉街道" },
        { name: "天河南街道" },
        { name: "天园街道" },
        { name: "车陂街道" },
        { name: "冼村街道" },
        { name: "猎德街道" },
        { name: "员村街道" },
        { name: "前进街道" },
        { name: "沙河街道" }
      ];
      geoCoordMap = {
        "凤凰街道": [113.3885, 23.2119],
        "龙洞街道": [113.3588, 23.2051],
        "元岗街道": [113.3387, 23.1836],
        "长兴街道": [113.3665, 23.1748],
        "新塘街道": [113.4033, 23.1659],
        "兴华街道": [113.3220, 23.1690],
        "五山街道": [113.3455, 23.1584],
        "沙东街道": [113.3104, 23.1629],
        "林和街道": [113.3185, 23.1484],
        "石牌街道": [113.3403, 23.1382],
        "棠下街道": [113.3744, 23.1421],
        "黄村街道": [113.4073, 23.1396],
        "珠吉街道": [113.4244, 23.1359],
        "天河南街道": [113.3185, 23.1322],
        "天园街道": [113.3666, 23.1281],
        "车陂街道": [113.3917, 23.1199],
        "冼村街道": [113.3299, 23.1257],
        "猎德街道": [113.3201, 23.1152],
        "员村街道": [113.3599, 23.1165],
        "前进街道": [113.4073, 23.1124],
        "沙河街道": [113.3033, 23.1522]
      };
      } else if (this.code === '440105') {
        // 海珠区（需要补充实际镇街数据）
      toolTipData = [
        { name: "滨江街道" },
        { name: "海幢街道" },
        { name: "南华西街道" },
        { name: "龙凤街道" },
        { name: "沙园街道" },
        { name: "南石头街道" },
        { name: "素社街道" },
        { name: "江南中街道" },
        { name: "昌岗街道" },
        { name: "新港街道" },
        { name: "赤岗街道" },
        { name: "凤阳街道" },
        { name: "江海街道" },
        { name: "南洲街道" },
        { name: "华洲街道" },
        { name: "琶洲街道" },
        { name: "官洲街道" },
        { name: "瑞宝街道" }
      ];
      geoCoordMap = {
        "滨江街道": [113.2788, 23.1101], // 示例经纬度，需校准
        "海幢街道": [113.2577, 23.1122], // 示例经纬度，需校准
        "南华西街道": [113.2421, 23.1055], // 示例经纬度，需校准
        "龙凤街道": [113.2451, 23.0959], // 示例经纬度，需校准
        "沙园街道": [113.2584, 23.0884], // 示例经纬度，需校准
        "南石头街道": [113.2655, 23.0759], // 示例经纬度，需校准
        "素社街道": [113.2702, 23.1040], // 示例经纬度，需校准
        "江南中街道": [113.2633, 23.0980], // 示例经纬度，需校准
        "昌岗街道": [113.2777, 23.0899], // 示例经纬度，需校准
        "新港街道": [113.2902, 23.099], // 示例经纬度，需校准
        "赤岗街道": [113.3199, 23.1026], // 示例经纬度，需校准
        "凤阳街道": [113.3002, 23.0841], // 示例经纬度，需校准
        "江海街道": [113.3284, 23.0885], // 示例经纬度，需校准
        "南洲街道": [113.3111, 23.0699], // 示例经纬度，需校准
        "华洲街道": [113.3488, 23.0644], // 示例经纬度，需校准
        "琶洲街道": [113.3726, 23.1012], // 示例经纬度，需校准
        "官洲街道": [113.3657, 23.0826], // 示例经纬度，需校准
        "瑞宝街道": [113.2822, 23.0649] // 示例经纬度，需校准
      };
      } else if (this.code === '440104') {
        // 越秀区（需要补充实际镇街数据）
      toolTipData = [
        { name: "矿泉街道" },
        { name: "流花街道" },
        { name: "登峰街道" },
        { name: "六榕街道" },
        { name: "洪桥街道" },
        { name: "建设街道" },
        { name: "华乐街道" },
        { name: "黄花岗街道" },
        { name: "光塔街道" },
        { name: "北京街道" },
        { name: "大塘街道" },
        { name: "农林街道" },
        { name: "梅花村街道" },
        { name: "大东街道" },
        { name: "珠光街道" },
        { name: "东山街道" },
        { name: "人民街道" },
        { name: "白云街道" }
      ];
      geoCoordMap = {
        "矿泉街道": [113.2451, 23.1608], // 向右上偏移
        "流花街道": [113.2542, 23.1488], // 向右上偏移
        "登峰街道": [113.2805, 23.1524], // 向右上偏移
        "六榕街道": [113.2522, 23.1362], // 向右上偏移
        "洪桥街道": [113.2629, 23.1401], // 向右上偏移
        "建设街道": [113.2758, 23.1355], // 向右上偏移
        "华乐街道": [113.2844, 23.1381], // 向右上偏移
        "黄花岗街道": [113.2955, 23.1416], // 向右上偏移
        "光塔街道": [113.2511, 23.1239], // 向右上偏移
        "北京街道": [113.2620, 23.1277], // 向右上偏移
        "大塘街道": [113.2737, 23.1312], // 向右上偏移
        "农林街道": [113.2900, 23.1287], // 向右上偏移
        "梅花村街道": [113.3041, 23.1310], // 向右上偏移
        "大东街道": [113.2803, 23.1259], // 向右上偏移
        "珠光街道": [113.2701, 23.1205], // 向右上偏移
        "东山街道": [113.2964, 23.1209], // 向右上偏移
        "人民街道": [113.2559, 23.1164], // 向右上偏移
        "白云街道": [113.2959, 23.1144] // 向右上偏移
      };
      } else if (this.code === '440103') {
        // 荔湾区（需要补充实际镇街数据）
              toolTipData = [
        { name: "西村街道" },
        { name: "站前街道" },
        { name: "南源街道" },
        { name: "桥中街道" },
        { name: "彩虹街道" },
        { name: "金花街道" },
        { name: "昌华街道" },
        { name: "逢源街道" },
        { name: "龙津街道" },
        { name: "华林街道" },
        { name: "多宝街道" },
        { name: "岭南街道" },
        { name: "沙面街道" },
        { name: "石围塘街道" },
        { name: "花地街道" },
        { name: "茶滘街道" },
        { name: "冲口街道" },
        { name: "海龙街道" },
        { name: "白鹤洞街道" },
        { name: "东漖街道" },
        { name: "东沙街道" },
        { name: "中南街道" }
      ];
      geoCoordMap = {
        "西村街道": [113.2378, 23.1521], // 示例经纬度，需校准
        "站前街道": [113.2445, 23.1456], // 示例经纬度，需校准
        "南源街道": [113.2301, 23.1422], // 示例经纬度，需校准
        "桥中街道": [113.2115, 23.1318], // 示例经纬度，需校准
        "彩虹街道": [113.2356, 23.1348], // 示例经纬度，需校准
        "金花街道": [113.2412, 23.1303], // 示例经纬度，需校准
        "昌华街道": [113.2277, 23.1212], // 示例经纬度，需校准
        "逢源街道": [113.2363, 23.1266], // 示例经纬度，需校准
        "龙津街道": [113.2458, 23.1242], // 示例经纬度，需校准
        "华林街道": [113.2387, 23.1189], // 示例经纬度，需校准
        "多宝街道": [113.2325, 23.1144], // 示例经纬度，需校准
        "岭南街道": [113.2463, 23.1115], // 示例经纬度，需校准
        "沙面街道": [113.2386, 23.1082], // 示例经纬度，需校准
        "石围塘街道": [113.2129, 23.1106], // 示例经纬度，需校准
        "花地街道": [113.2283, 23.1019], // 示例经纬度，需校准
        "茶滘街道": [113.2212, 23.0908], // 示例经纬度，需校准
        "冲口街道": [113.2334, 23.0833], // 示例经纬度，需校准
        "海龙街道": [113.1909, 23.0752], // 示例经纬度，需校准
        "白鹤洞街道": [113.2401, 23.0745], // 示例经纬度，需校准
        "东漖街道": [113.2207, 23.0688], // 示例经纬度，需校准
        "东沙街道": [113.2488, 23.0586], // 示例经纬度，需校准
        "中南街道": [113.2111, 23.0549] // 示例经纬度，需校准
      };
      }

      // 生成散点数据（仅当 code 匹配时有效）
      function scatterData() {
        return toolTipData.map((item) => {
          return [geoCoordMap[item.name][0], geoCoordMap[item.name][1], item];
        });
      }

      let option = {
        backgroundColor: "transparent",
        geo: [
          {
            map: "centerMap",
            aspectScale: 1,
            zoom: 0.53,
            layoutCenter: ["50%", "52%"],
            layoutSize: "190%",
            show: true,
            roam: false,
            label: {
              // 是否显示地区名称
              show: false,
              color: '#fff',
              fontSize: 26,
            },
            itemStyle: {
              normal: {
                borderWidth: 4,
                borderColor: "#fff",
                shadowColor: "rgba(2, 24, 228, 0.6)",
                shadowOffsetY: 15,
                shadowBlur: 15,
                areaColor: "rgba(5,21,35,0.1)",
              },
            },
            regions: regions,
          },
          {
            type: "map",
            map: "centerMap",
            zlevel: -2,
            aspectScale: 1,
            zoom: 0.53,
            layoutCenter: ["50%", "52%"],
            layoutSize: "190%",
            roam: false,
            silent: true,
            itemStyle: {
              normal: {
                borderWidth: 4,
                borderColor: "rgba(2, 24, 228, 1)",
                shadowColor: "rgba(2, 24, 228, 0.6)",
                shadowOffsetY: 0,
                shadowBlur: 0,
                // 底层区域颜色
                areaColor: {
                  type: "radial",
                  x: 0,
                  y: 0.5,
                  r: 0.5,
                  // 鼠标移入的线性渐变颜色
                  colorStops: [
                    { offset: 0, color: "rgba(2, 24, 228, 1)" },
                    { offset: 1, color: "rgba(9, 176, 254, 1)" },
                  ],
                  global: false,
                },
              },
            },
          },
        ],

        series: [
          {
            name: "centerMap",
            type: "map",
            map: "centerMap",
            aspectScale: 1,
            zoom: 0.53,
            showLegendSymbol: true,
            label: {
              normal: {
                show: false, // 隐藏原始地区名称标签
                // textStyle: {
                //   color: "#fff",
                //   fontSize: 32,
                //   // fontWeight: 'bold',
                //   backgroundColor: '#cf4419',
                //   padding: [10, 10],
                //   borderRadius: 5,
                //   shadowColor: 'rgba(0,0,0,0.8)',
                //   shadowBlur: 5,
                //   shadowOffsetX: 2,
                //   shadowOffsetY: 2
                // },
                position: 'inside', // 将标签放在区域内部
                align: 'center', // 居中对齐
                verticalAlign: 'middle', // 垂直居中
                hideOverlap: true, // 关键配置：自动隐藏重叠的标签
                avoidLabelOverlap: true, // 尝试调整标签位置避免重叠
                moveOverlap: 'shiftX', // 重叠时水平移动
                // 新增：格式化标签内容（名称+坐标）
                // formatter: function(params) {
                //   const name = params.name;
                //   const coord = geoCoordMap[name] || [0, 0]; // 从geoCoordMap获取坐标（无数据时默认[0,0]）
                //   // 格式化为 "名称\n经度：xxx 纬度：xxx"
                //   return `${name}\n经度：${coord[0].toFixed(4)} 纬度：${coord[1].toFixed(4)}`;
                // }
              },
              emphasis: {
                // 是否开启鼠标悬浮时的emphasis状态(交互效果)
                show: false,
                // textStyle: {
                //   color: "#fff",
                //   fontSize: 46,
                //   fontWeight: 'bold',
                //   backgroundColor: '#cf4419',
                //   padding: [8, 15],
                //   borderRadius: 8,
                //   shadowColor: 'rgba(0,0,0,0.8)',
                //   shadowBlur: 8,
                //   shadowOffsetX: 3,
                //   shadowOffsetY: 3
                // }
              }
            },
            itemStyle: {
              normal: {
                areaColor: {
                  type: "linear",
                  x: 1200,
                  y: 0,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 1,
                      color: "rgba(0,0,0,0)", // 50% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
                borderColor: "#fff",
                borderWidth: 0.2,
              },
              emphasis: {
                show: true,
                color: "#fff",
                areaColor: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  // 鼠标移入的线性渐变颜色
                  colorStops: [
                    { offset: 1, color: "rgba(204, 165, 0, 0.70)" },
                    { offset: 0, color: "rgba(153, 124, 0, 0)" },
                  ],
                  global: false,
                },
              },
            },
            layoutCenter: ["50%", "52%"],
            layoutSize: "190%",
            markPoint: {
              symbol: "none",
            },
            data: data,
          },
          // 引导线散点系列
          {
            type: "scatter",
            coordinateSystem: "geo",
            geoIndex: 0,
            zlevel: 999,
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: function (params) {
                  return params.data[2].name;
                },
                fontSize: 32,
                color: '#fff',
                backgroundColor: '#cf4419',
                padding: [5, 10],
                borderRadius: 4,
                // 根据地图缩放比例（当前zoom=0.53）校准distance，保证视觉长度统一
                distance: 60 / 0.53, // 原120调整为60/0.53≈113（可根据实际效果微调分子）
              }
            },
            itemStyle: {
              opacity: 1,
              color: '#CF4419',
              borderColor: '#fff',
              borderWidth: 2
            },
            symbol: 'circle',
            symbolSize: 20,
            data: scatterData(),
            markLine: {
              symbol: ['none', 'none'],
              lineStyle: {
                color: '#fff',
                width: 2,
                type: 'solid',
                curveness: 0.2 // 轻微弯曲效果（0-1）
              },
              data: scatterData().map(item => {
                // 定义各区域代码对应的偏移量（可根据需求扩展）
                const codeOffsets = {
                  '440103': 0.012,   // 荔湾区
                  '440184': 0.08,    // 从化区
                  '440183': 0.09,    // 增城区
                  '440111': 0.06,    // 白云区
                  '440113': 0.06,    // 番禺区
                  '440114': 0.08,    // 花都区
                  '440115': 0.07,    // 南沙区
                  '440112': 0.07,    // 黄埔区
                  '440106': 0.03,    // 天河区
                  '440105': 0.03,    // 海珠区
                  '440104': 0.01,    // 越秀区
                  default: 0.1        // 默认偏移量（未匹配时使用）
                };
                // 根据当前code获取偏移量（无匹配时使用default）
                const offset = codeOffsets[this.code] ?? codeOffsets.default;
                return [{
                  coord: [item[0], item[1]], // 起点坐标（散点位置）
                  symbol: 'none'
                }, {
                  coord: [item[0] + offset, item[1]], // 动态偏移量
                  symbol: 'none'
                }]
              })
            }
          },
          // {
          //   type: "scatter",
          //   coordinateSystem: "geo",
          //   geoIndex: 0,
          //   zlevel: 5,
          //   label: {
          //     normal: {
          //       show: true,
          //       formatter: function (params) {
          //         var name = params.data.name;
          //         var text = `{tline|${name}} `;
          //         return text;
          //       },
          //       rich: {
          //         tline: {
          //           color: "#fff",
          //           fontSize: "34px",
          //           fontWeight: 500,
          //         },
          //       },
          //     },
          //     emphasis: {
          //       show: true,
          //     },
          //   },
          //   itemStyle: {
          //     opacity: 1,
          //     fontSize: "34px",
          //     color: "#CF4419",
          //     borderColor: "#fff",
          //     borderWidth: "2px",
          //   },
          //   symbol: "rect",
          //   symbolSize: [150, 60],
          //   symbolOffset: [0, -75],
          //   z: 999,
          //   data: [],
          // },
          {
            name: "Top 5",
            type: "scatter",
            coordinateSystem: "geo",
            data: convertData(toolTipData),
            showEffectOn: "render",
            rippleEffect: {
              scale: 5,
              brushType: "stroke",
            },
            label: {
              normal: {
                formatter: "{b}",
                position: "bottom",
                show: false,
                color: "#fff",
                distance: 10,
              },
            },
            symbol: "triangle",
            symbolSize: [89, 53],
            symbolRotate: 180,
            symbolOffset: [0, -34],
            itemStyle: {
              normal: {
                color: function () {
                  // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，分别表示右,下,左,上。例如（0，0，0，1）表示从正上开始向下渐变；如果是（1，0，0，0），则是从正右开始向左渐变。
                  // 相当于在图形包围盒中的百分比，如果最后一个参数传 true，则该四个值是绝对的像素位置
                  return new echarts.graphic.LinearGradient(
                    0,
                    0.35,
                    0.65,
                    1,
                    [
                      {
                        offset: 1,
                        color: "rgba(210, 74, 24, 1)",
                      },
                      {
                        offset: 0,
                        color: "rgba(195, 221, 251, 1)",
                      },
                    ],
                    false
                  );
                },
                shadowBlur: 15,
                shadowColor: "rgba(0, 153, 255, 0.94)",
              },

              opacity: 1,
            },
            zlevel: 3,
          },
        ],

      };

      myChart.showLoading();
      console.log(myChart.registerMap);
      //   $.getJSON(mapJson, function (geoJson) {
      if (this.code) {
        this.$echarts.registerMap("centerMap", this.geoJson);
      } else {
        this.$echarts.registerMap("centerMap", this.geoJson);
      }
      myChart.hideLoading();

      myChart.setOption(option);

      let _this = this;

      myChart.on("click", function (params) {
        console.log('geoJson')
        console.log(_this.geoJson)
        console.log(params)
        console.log(_this.areaName)

        // _this.$emit("getMapInfo", params, _this.geoJson); // 触发确认事件并传递数据
        // alert(JSON.stringify(params));
        const areaName = _this.areaName + params.name
        console.log(areaName)
        // 跳转逻辑（示例：Vue Router）
        if (areaName) {
          _this.$router.push({
            path: '/liaisonStation',
            query: {
              areaName: areaName,
              administrativeAreaId: _this.code,
              streetTownId: _this.geoJson.features[params.dataIndex].properties.id
            }
          });
        }
      });

      // myChart.on("mouseover", function (params) {
      //   // 只有当鼠标移入地图区域时才触发
      //   console.log(params)
      //   console.log(_this.geoJson)
      //   if (params.componentType === 'series' && params.seriesType === 'map') {
      //     console.log('鼠标移入:', params.name);
      //     _this.$emit("getMapInfo", params, _this.geoJson);

      //     // 可选：添加高亮效果
      //     // myChart.dispatchAction({
      //     //   type: 'highlight',
      //     //   seriesIndex: params.seriesIndex,
      //     //   dataIndex: params.dataIndex
      //     // });
      //   
      // });

      //   });

      // echarts参数设置
      myChart.setOption(option);
    },
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
};
</script>
