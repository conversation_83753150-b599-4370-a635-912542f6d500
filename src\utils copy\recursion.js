/**
 * 递归树形数据工具类
 */

/**
 * 数据过滤。如果叶节点为空，过滤整条链。父节点通过不再判断子节点
 * 例：数据搜索过滤
 * @param {Array} data
 * @param {Function} callbackFn (it, parent, level) => boolean
 * @param {String=}children
 * @return {Array}
 */
export function filterOnceAllRecursion(
  data,
  callbackFn,
  children = "children"
) {
  function walk(target, parent, level) {
    return target
      .map((it) => {
        const bool = callbackFn(it, parent, level);
        // 与 filterAllRecursion 差别, 通过不再判断子节点
        if (bool) return it;
        if (it[children]?.length) {
          const res = walk(it[children], it, level + 1);
          if (!res.length) return undefined;
          return {
            ...it,
            [children]: res,
          };
        }
        return undefined;
      })
      .filter((it) => it);
  }
  return walk(data, null, 1);
}

/**
 * 递归查找匹配项
 * @param {Array}data  树形数据
 * @param {Function}eq (it, parent, level) => boolean   是否查找项
 * @param {String=}children  子级数据
 @return {Object|undefined}首个匹配项
 */
export function findRecursion(data, eq, children = "children") {
  function walk(target, parent, level) {
    for (const it of target) {
      if (eq(it, parent, level)) return it;

      if (Array.isArray(it[children]) && it[children].length) {
        const res = walk(it[children], it, level + 1);
        if (res) return res;
      }
    }
    return undefined;
  }
  return walk(data, null, 1);
}

/**
 * 递归查找匹配项的父级
 * @param {Array}data  树形数据
 * @param {Function}eq (it, parent, level) => boolean   是否查找项
 * @param {String=}children  子级数据
 * @return {Object|undefined}首个匹配项的父级
 */
export function findParentRecursion(data, eq, children = "children") {
  function walk(target, parent, level) {
    for (const it of target) {
      if (eq(it, parent, level)) return parent;

      if (Array.isArray(it[children]) && it[children].length) {
        const res = walk(it[children], it, level + 1);
        if (res) return res;
      }
    }
    return undefined;
  }
  return walk(data, null, 1);
}

/**
 * 递归查找匹配项和它们的父级
 * @param {Array}data  树形数据
 * @param {Function}eq (it, parent, level) => boolean   是否查找项
 * @param {String=}children  子级数据
 * @return {Array|undefined} 首个匹配项和它们的父级
 */
export function findRecursions(data, eq, children = "children") {
  function walk(target, parents, parent, level) {
    for (const it of target) {
      if (eq(it, parent, level)) return [...parents, it];

      if (Array.isArray(it[children]) && it[children].length) {
        const res = walk(it[children], [...parents, it], it, level + 1);
        if (res) return res;
      }
    }
    return undefined;
  }
  return walk(data, [], null, 1);
}

/**
 * 将结构由树级转为平级
 * @param data  树形数据
 * @param children  子级数据
 * @param map 可选择转换处理
 * @return {Array} 平级数据
 */
export function flatRecursion(data, children = "children", map = undefined) {
  const res = [];
  function walk(target, parent, level) {
    target.forEach((it) => {
      const result = map?.(it, parent, level) || it;
      res.push(result);
      if (Array.isArray(it[children]) && it[children].length) {
        walk(it[children], it, level + 1);
      }
    });
  }
  walk(data, null, 1);
  return res;
}

/**
 * 遍历递归数据
 * @param data  树形数据
 * @param callbackFn  回调方法
 * @param children  子级数据
 */
export function forEachRecursion(data, callbackFn, children = 'children') {
  function walk(target, parent, level) {
    target.forEach((it) => {
      callbackFn(it, parent, level)
      if (Array.isArray(it[children]) && it[children].length) {
        walk(it[children], it, level + 1)
      }
    })
  }
  walk(data, null, 1)
}
