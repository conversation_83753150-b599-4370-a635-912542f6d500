<template>
  <div ref="chart" class="chart"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "barChart",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    xAxis: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    if (this.data) {
      this.getIint();
    }
  },
  watch: {
    data: {
      handler() {
        this.getIint();
      },
      deep: true,
    },
  },
  methods: {
    getIint() {
      let myChart = this.$echarts.init(this.$refs.chart);
      var data = this.data;  
      var maxIndex = data.indexOf(Math.max(...data)); // 找到最高点的索引

      var option = { 
        grid: {
          left: 0,
          right: "2%",
          bottom: "10%",
          top: "10%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          formatter: "{b}月统计: {c}", // 提示信息
          textStyle: {
            fontSize: 32,
            fontWeight: "bold",
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.xAxis,
          axisLine: {
            lineStyle: {
              color: "#ccc", // x轴颜色
            },
          },
          axisLabel: {
            color: "#fff", // x轴文字颜色
            fontSize: 30,
            fontWidth: "bold",
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            lineStyle: {
              color: "#ccc", // y轴颜色
            },
          },
          axisLabel: {
            color: "#fff", // y轴文字颜色
            fontSize: 30,
          },
          splitLine: {
            lineStyle: {
              color: "#444", // 网格线颜色
            },
          },
        },
        series: [
          {
            type: "line",
            data: data,
            smooth: true, // 平滑曲线
            lineStyle: {
              color: "#FF7F00", // 折线颜色
              width: 2,
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(227,128, 63, 0.8)" }, // 渐变起始颜色
                { offset: 1, color: "rgba(227,128, 63, 0)" }, // 渐变结束颜色
              ]),
            },
            // markPoint: {
            //   data: [
            //     {
            //       name: "统计",
            //       value: data[maxIndex],
            //       xAxis: maxIndex, // 设置为最高点位置
            //       yAxis: data[maxIndex],
            //       symbol: "circle",
            //       symbolSize: 10,
            //       itemStyle: {
            //         color: "#FF7F00", 
            //       },
            //       label: {
            //         show: true,
            //         formatter: "{b}：{c}",
            //         color: "#FF7F00",
            //         fontSize: 40, // 放大字体
            //         offset: [120, -30], 
            //       },
            //     },
            //   ],
            // }, 
          },
          {
            type: "bar",
            data: data.map((value, index) => (index === maxIndex ? value : 0)), // 只在最高点显示柱子
            barWidth: 10, // 柱子宽度
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(254,248,248, 0.2)" }, // 渐变起始颜色
                { offset: 1, color: "rgba(254,248,248, 1)" }, // 渐变结束颜色
              ]),
            },
            z: -1, // 确保柱子在折线图后面
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
