<template>
  <div ref="chart" class="chart"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "barChart",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    xAxis: {
      type: Array,
      default: () => [],
    },
    displayMode: {
      type: String,
      default: "week", // week | 30days
    },
  },
  mounted() {
    if (this.data) {
      this.getIint();
    }
  },
  watch: {
    data: {
      handler() {
        this.getIint();
      },
      deep: true,
    },
  },
  methods: {
    getIint() {
      let myChart = this.$echarts.init(this.$refs.chart);
      var data = this.data;  
      var maxIndex = data.indexOf(Math.max(...data)); // 找到最高点的索引

      var option = { 
        grid: {
          left: 0,
          right: "2%",
          bottom: this.xAxis.length > 20 ? "20%" : "15%", // 数据点多时增加底部空间
          top: "10%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          formatter: function(params) {
            const value = params[0].value;
            const date = params[0].axisValue;
            return `${date}日统计: ${value}`;
          },
          textStyle: {
            fontSize: 28,
            fontWeight: "bold",
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#0f9eef',
          borderWidth: 1,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.xAxis,
          axisLine: {
            lineStyle: {
              color: "#ccc", // x轴颜色
            },
          },
          axisLabel: {
            color: "#fff", // x轴文字颜色
            fontSize: this.displayMode === "30days" ? 22 : (this.xAxis.length > 15 ? 24 : 30), // 30天模式使用更小字体
            fontWidth: "bold",
            interval: this.displayMode === "30days" ? 3 : (this.xAxis.length > 20 ? 2 : (this.xAxis.length > 15 ? 1 : 0)), // 30天模式间隔显示更多
            rotate: this.displayMode === "30days" ? 45 : (this.xAxis.length > 20 ? 45 : 0), // 30天模式旋转标签
            margin: this.displayMode === "30days" ? 12 : 8,
          },
          axisTick: {
            alignWithLabel: true,
            interval: this.displayMode === "30days" ? 3 : (this.xAxis.length > 20 ? 2 : (this.xAxis.length > 15 ? 1 : 0)),
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            lineStyle: {
              color: "#ccc", // y轴颜色
            },
          },
          axisLabel: {
            color: "#fff", // y轴文字颜色
            fontSize: 30,
          },
          splitLine: {
            lineStyle: {
              color: "#444", // 网格线颜色
            },
          },
        },
        series: [
          {
            type: "line",
            data: data,
            smooth: true, // 平滑曲线
            lineStyle: {
              color: "#0f9eef", // 折线颜色
              width: 2,
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(15, 158, 239, 0.8)" }, // 渐变起始颜色
                { offset: 1, color: "rgba(15, 158, 239, 0)" }, // 渐变结束颜色
              ]),
            },
            markPoint: {
              data: [
                {
                  name: "统计",
                  value: data[maxIndex],
                  xAxis: maxIndex, // 设置为最高点位置
                  yAxis: data[maxIndex],
                  symbol: "circle",
                  symbolSize: 10,
                  itemStyle: {
                    color: "#FFFFFF", 
                  },
                  label: {
                    show: true,
                    formatter: "{c}",
                    color: "#FFFFFF",
                    fontSize: 30, // 放大字体
                    offset: [0,-30], // 向右上方偏移
                  },
                },
              ],
            }, 
          },
          {
            type: "bar",
            data: data.map((value, index) => (index === maxIndex ? value : 0)), // 只在最高点显示柱子
            barWidth: 20, // 柱子宽度
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(28, 61, 104, 1)" }, // 渐变起始颜色
                { offset: 1, color: "rgba(28, 61, 104, 1)" }, // 渐变结束颜色
              ]),
            },
            z: -1, // 确保柱子在折线图后面
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
