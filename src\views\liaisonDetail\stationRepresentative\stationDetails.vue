<template>
  <div class="basicInformation-page">
    <div class="basicInformation-main_left">
      <img class="avatar" :src="baseURL + '/dbxx' + dbInfo.photoUrl" alt="" />
      <div class="text">{{ dbInfo.userName }}</div>
      <div class="text">{{ dbInfo.dbSf }}</div>
      <div class="qrcode">
        <canvas ref="qrCanvas"></canvas>
      </div>
      <img class="bottom" :src="require('@/assets/image/bottom_201.png')" alt="" />
    </div>
    <div class="basicInformation-main_right">
      <div class="header">
        <div class="dbxxxx">代表详细信息</div>
        <div class="btn" @click="$router.go(-1)">返回</div>
      </div>
      <div class="desc-top">
        <el-descriptions style="margin-top: 48px">
          <el-descriptions-item label="姓名">{{ dbInfo.userName }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ (dbInfo.xbDm === '1' ? '男' : '女') }}</el-descriptions-item>
          <el-descriptions-item label="党派">{{ dbInfo.zzmmdm || '无' }}</el-descriptions-item>
          <el-descriptions-item span="2"  label="单位及职务">
            {{ dbInfo.workUnit }}-{{ dbInfo.unty }}
          </el-descriptions-item> 
          <el-descriptions-item label="本年已进社区"><span style="color: rgba(0, 255, 203, 1)">{{
            dbInfo.activityCount}}</span>次</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="tm">代表履职记录</div>
      <div class="desc-bottom">
        <!-- <el-descriptions :colon="false">
          <el-descriptions-item>调研联络站数字化建设工作</el-descriptions-item>
          <el-descriptions-item>参与集中视察工作</el-descriptions-item>
          <el-descriptions-item>市停车场执法调查</el-descriptions-item>
          <el-descriptions-item>
            <div class="tag-cums">
              <div class="dio"></div>
              2024-05-27
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <div class="tag-cums">
              <div class="dio"></div>
              2024-05-27
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <div class="tag-cums">
              <div class="dio"></div>
              2024-05-27
            </div>
          </el-descriptions-item>
        </el-descriptions> -->

        <!-- <el-descriptions :colon="false" :column="2">
          <el-descriptions-item>
            领衔提出“关于垃圾分类的建议”
          </el-descriptions-item>
          <el-descriptions-item>
            参加吕田镇人大代表中心联络站进社区活动
          </el-descriptions-item>
          <el-descriptions-item>
            <div class="tag-cums">
              <div class="dio"></div>
              2024-05-27
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <div class="tag-cums">
              <div class="dio"></div>
              2024-05-27
            </div>
          </el-descriptions-item>
        </el-descriptions> -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import QRCode from "qrcode";
import global_ from "../../../global.vue";
export default {
  name: "basicInformation-index",
  components: {},
  data() {
    return {
      baseURL: global_.basePath_1,
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [{ name: "菜单项1" }, { name: "菜单项2" }],
      qrText: "努力赚钱",
      dbInfo: ''
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() { },
  mounted() {
    this.generateQRCode();
    // 获取传递的参数并解析为对象
    if (this.$route.query.item) {
      this.dbInfo = JSON.parse(this.$route.query.item);
    }
  },
  watch: {},
  methods: {
    checkedItem(obj) {
      console.log("选中", obj);
    },
    generateQRCode() {
      QRCode.toCanvas(this.$refs.qrCanvas, this.qrText, (error) => {
        if (error) console.error(error);
        console.log("QR code generated!");
      });
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
.basicInformation-page {
  width: calc(100%);
  height: calc(100vh - 500px);
  display: flex;
  flex-direction: row;
  justify-content: start;
  // padding: 0 60px 0 80px;
  color: #fff;
  font-size: 50px;
  position: relative;
  z-index: 9;

  .basicInformation-main_left {
    // background: #fff;
    width: 793px;
    // margin-top: 80px;
    margin-bottom: 25px;

    // display: flex;
    // flex-direction: column;
    // justify-content: center;
    .avatar {
      margin: 0 181px;
      margin-top: 72px;
      width: 426px;
      height: 511px;
      border-radius: 0px 0px 0px 0px;
    }

    .text {
      text-align: center;
      margin-top: 62px;
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;

      font-weight: 400;
      font-size: 50px;
      color: #ffffff;
      line-height: 42px;
      letter-spacing: 7px;
      font-style: normal;
      text-transform: none;
    }

    .qrcode {
      width: 215px;
      height: 215px;
      background: url("@/assets/image/qrcode.png") no-repeat;
      background-size: 100% 100%;
      padding: 60px;
      margin: 0 auto;
      margin-top: 176px;

      canvas {
        width: 215px !important;
        height: 215px !important;
        border-radius: 0px 0px 0px 0px;
      }
    }

    .bottom {
      width: 793px;
      height: 223px;
      margin-top: 40px;
    }
  }

  .basicInformation-main_right {
    margin-top: 50px;

    .header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      
      .btn {
        padding: 20px 110px;
        font-family: PingFang SC, PingFang SC;
        font-size: 50px;
        color: #ffffff;
        line-height: 47px;
        letter-spacing: 4px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
        border-radius: 6px 6px 6px 6px;
        height: 56px;
      }

      .dbxxxx {
        background: url("@/assets/image/jjxx_card_bg.png") no-repeat;
        background-size: contain;
        height: 130px;
        width: 430px;
        margin-left: 29px;
        margin-top: -35px;
        padding: 52px 0 0 150px;
        font-family: PingFang SC, PingFang SC;
        // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;

        // font-weight: 600;
        font-size: 50px;
        color: #ffffff;
        line-height: 47px;
        letter-spacing: 4px;
        text-shadow: 0px 2px 3px rgba(51, 158, 188, 0.79);
        text-align: left;
        // font-style: normal;
        font-style: italic;
        text-transform: none;
      }
    }

    ::v-deep .el-descriptions__body {
      padding: 48px 86px;
      background: transparent;
    }

    ::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 50px;
      color: #ffffff;
      line-height: 42px;
      letter-spacing: 7px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    ::v-deep .el-descriptions__body {
      padding-top: 73px;
    }

    ::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
      padding-bottom: 52px;
    }

    .desc-top {
      width: 2602px;
      height: 310px;
      background: url("@/assets/image/llz_jcxx.png") no-repeat;
      background-size: 100% 100%;
    }

    .tm {
      width: 448px;
      height: 60px;
      background: linear-gradient(91deg,
          rgba(22, 103, 232, 0) 0%,
          rgba(0, 119, 255, 0.89) 49%,
          rgba(0, 136, 255, 0) 100%);
      border-radius: 0px 0px 0px 0px;
      font-family: PingFang SC, PingFang SC;
      // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 500;
      font-size: 50px;
      color: #ffffff;
      line-height: 60px;
      text-align: center;
      font-style: italic;
      text-transform: none;
      margin: 58px 43px;
    }

    .desc-bottom {
      width: 2602px;
      height: 867px;
      background: url("@/assets/image/llz_jcxx.png") no-repeat;
      background-size: 100% 100%;

      ::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
        // padding-bottom: 88px;
        margin-bottom: 50px;
      }

    }

    .tag-cums {
      display: flex;
      flex-direction: row;
      align-items: center;

      .dio {
        width: 28px;
        height: 28px;
        background: #8fdcb2;
        border-radius: 14px;
        margin-right: 12px;
      }
    }
  }
}
</style>
