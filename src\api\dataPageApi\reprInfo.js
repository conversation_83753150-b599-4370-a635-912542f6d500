import request from "@/utils/requestTemp";
import qs from "qs";

// ⼈⼤代表信息⼯作视窗-区级统计概览与五级代表该区分布
export async function getDbCountInfoForDistrict(data, token) {
  return request({
    url: "cockpit/getDbCountInfoForDistrict",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    // headers: {
    //   "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    // },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// ⼈⼤代表信息⼯作视窗-正中五级代表统计数
export async function getDbLevelCount(data, token) {
  return request({
    url: "cockpit/getDbLevelCount",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
  });
}

// ⼈⼤代表信息⼯作视窗-市代表年龄分布情况总览
export async function getDbAgeDistribution(data, token) {
  return request({
    url: "cockpit/getDbAgeDistribution",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
     "Content-Type": "application/json", // 保持为表单格式
    },
  });
}

// ⼈⼤代表信息⼯作视窗-市代表学历分布情况总览
export async function getDbDegreeDistribution(data, token) {
  return request({
    url: "cockpit/getDbDegreeDistribution",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json", // 保持为表单格式
    },
  });
}

// ⼈⼤代表信息⼯作视窗-区代表综合情况总览
export async function getQUDbSynthesisInfo(data, token) {
  return request({
    url: "cockpit/getQUDbSynthesisInfo",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json", // 保持为表单格式
    },
  });
}
// ⼈⼤代表信息⼯作视窗-市代表综合情况
export async function getDbSynthesisInfo(data, token) {
  return request({
    url: "cockpit/getDbSynthesisInfo",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持为表单格式
    },
  });
}


// ⼈⼤代表信息⼯作视窗-市代表基本构成(职业构成)
export async function getJobTypeCount(data, token) {
  return request({
    url: "cockpit/getJobTypeCount",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json", // 保持为表单格式
    },
  });
}
// ⼈⼤代表信息⼯作视窗-市代表政治⾯貌构成
export async function getPoliticalStatus(data, token) {
  return request({
    url: "cockpit/getPoliticalStatus",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}