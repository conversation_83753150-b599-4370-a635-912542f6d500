<template>
  <div class="title-right-con">
    <!-- clicktype 0 不展示选择年份 1 不展示进入专题 -->
    <div class="data-picker-box" style="margin-right:130px" v-if="clicktype == '0' || clicktype == '2' || clicktype == '/junior/yajy'">
      <!-- <el-date-picker class="date-picker" prefix-icon="prefix-icon" :clearable="false" v-model="localValue" format="yyyy"
        value-format="yyyy" type="year" @change="handleDateChange" />
      <i class="el-icon-arrow-down"></i> -->
      <!-- <template v-if="clicktype == '2' || clicktype == '/junior/yajy'"> -->
        <!-- Session selection component -->
        <el-select class="date-picker-yajy" v-model="selectedMeetingId" placeholder="请选择" :clearable="true"
          @change="handleSessionChange">
          <el-option v-for="item in periods" :key="item.meetingId" :label="item.meetingSname" :value="item.meetingId">
          </el-option>
        </el-select>
      <!-- </template> -->
      
    </div>

    <div class="data-picker-box" style="margin-right:30px"  v-else-if="clicktype != 1">
      <!-- <el-date-picker class="date-picker" prefix-icon="prefix-icon" :clearable="false" v-model="localValue" format="yyyy"
        value-format="yyyy" type="year" @change="handleDateChange" />
      <i class="el-icon-arrow-down"></i> -->
      <!-- <template v-if="clicktype == '2' || clicktype == '/junior/yajy'"> -->
        <!-- Session selection component -->
        <el-date-picker class="date-picker" prefix-icon="prefix-icon" :clearable="false" v-model="value" format="yyyy"
        value-format="yyyy" type="year" @change="handleDateChange" 
        :picker-options="pickerOptions"/>
      <i class="el-icon-arrow-down"></i>
      <!-- </template> -->
    </div>

    <div class="name-box" @click="onHenldNext" v-if="clicktype != '-1' && clicktype != '/junior/yajy'">
      <span class="name">进入专题</span>
      <img class="icon" :src="require('@/assets/image/right-icon.png')" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'YearPickerComponent',
  props: {
    value: {
      type: String,
      default: '',
    },
    clicktype: {
      type: String,
      default: () => null,
    },
    periods: {
      type: Object,
      default: () => ({}),
    },
    meeting: {
      type: String,
      default: () => null,
    },
  },
  data() {
    return {
      localValue: this.value, // 初始化本地值
      clicktype: this.clicktype,
      periods: JSON.parse(this.periods),
      pickerOptions: {
        disabledDate: (time) => {
          const currentYear = new Date().getFullYear();
          return time.getFullYear() >= currentYear; // 禁用当前年份及之后的年份
        }
      }
    };
  },
  computed: {
    // sortedPeriods() {
    //   return [...this.periods].sort((a, b) => {
    //     return b.meetingSname.localeCompare(a.meetingSname);
    //   });
    // },

    selectedMeetingId: {
      get() {
        return this.meeting; // 从父组件获取当前选中的 meetingId
      },
      set(newVal) {
        console.log(newVal)
        this.$emit("chageSelectMeeting", newVal); // 通知父组件更新
      },
    },
  },
  watch: {
    value(newVal) {
      this.localValue = newVal; // 更新本地值
    },
    // 监听内部值变化，并同步到父组件
    localValue(newVal) {
      // console.log(newVal)
    },
  },
  methods: {
    onHenldNext() {
      this.$emit('henld-next'); // 触发自定义事件
    },
    handleDateChange(newVal) {
      console.log(this.clicktype)
      this.$emit('change-Year', newVal, this.clicktype);
    },
    handleSessionChange(selectedId) {
      const selectedItem = this.periods.find(item => item.meetingId === selectedId);
      if (selectedItem) {
        console.log("选中ID:", selectedItem.meetingId);
        console.log("选中名称:", selectedItem.meetingSname);
        this.$emit('meeting-change', {
          id: selectedItem.meetingId,
          name: selectedItem.meetingSname
        });
      }
    }
  },
};
</script>

<style lang="less" scoped>
.title-right-con {
  display: flex;
  align-items: center;

  .data-picker-box {
    position: relative;
    width: 94px;
    height: 36px;

    .prefix-icon {
      display: none;
    }

    .date-picker-yajy {
      width: 230%;
      height: 100%;
      position: absolute;

      ::v-deep .el-input__inner {
        background: transparent;
        color: #fff;
        font-size: 30px;
        padding: 0 38px 0 8px;
        border: 1px solid #b5b5b5;
        border-radius: 4px;
      }
    }

    .date-picker {
      width: 130%;
      height: 100%;
      position: absolute;

      ::v-deep .el-input__inner {
        background: transparent;
        color: #fff;
        font-size: 30px;
        padding: 0 38px 0 8px;
        border: 1px solid #b5b5b5;
        border-radius: 4px;
      }
    }

    .el-icon-arrow-down {
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
      color: #fff;
      font-size: 16px;
    }
  }

  .name-box {
    display: flex;
    align-items: center;
    cursor: pointer;

    .name {
      /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/
      font-weight: 500;
      font-size: 28px;
      color: #28f1fa;
      margin-left: 20px;
    }

    .icon {
      width: 28px;
    }
  }
}
</style>
