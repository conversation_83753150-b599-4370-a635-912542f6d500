export const url = /^(https?:)?\/{2}/;

// 姓名校验
// /^[\u4e00-\u9fa5]{2,10}$/
// 网址
// /^(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/
// 身份证号
// /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/
// 邮箱编码
// /^[1-9][0-9]{5}$/
// 固定电话
// /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/
// 手机号码
// /^1(3|4|5|6|7|8|9)\d{9}$/
// 手机或固话
// /^((0\d{2,3}-?\d{7,8})|(1[3465789]\d{9}))$/
// 正整数含0
// /^[+]{0,1}(\d+)$/
// 正整数不含0
// /^[1-9]\d*$/.test(str)
// 正数 含 1位小数
// /^\d+(\.\d{1,1})?$/
