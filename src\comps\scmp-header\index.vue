<template>
  <div class="scmp-header">
    <div class="left" v-if="!isHomePage">
      <div style="cursor: pointer;" @click="goHome">
        <img :src="require('@/assets/header/mingcute_back-line.png')" alt="" />
        返回首页
      </div>
    </div>
    <div class="left" @click="goHome" v-else></div>
    <img class="title" :src="require('@/assets/header/header_title.png')" alt="" />
    <div class="right">
      {{ currentTime }} {{ currentDate }} {{ currentDay }}
      <span v-if="weatherData">| {{ weatherData.weather }} 温度 {{ weatherData.temperature }}℃</span>
    </div>
  </div>
</template>

<script>
import store from "@/stores";
export default {
  name: "scmp-header",
  data() {
    return {
      currentTime: '',
      currentDate: '',
      currentDay: '',
      weatherData: null,
      timer: null
    }
  },
  mounted() {
    this.updateDateTime()
    this.timer = setInterval(this.updateDateTime, 1000)

    // this.fetchWeatherData()
  },
  computed: {
    isHomePage() {
      console.log(this.$route.path)
      return this.$route.path === "/region";  // 判断当前路由是否是 login
    },
  },
  methods: {
    // 返回首页
    goHome() {
      // 本地调用
      // this.$router.push('/region');
      // store.gee
      // debugger
      // this.$router.push(store.getters.defaultRoute);
      const currentPath = this.$route.path;
      if (currentPath.includes('/perform') || currentPath.includes('/popularWill')) {
        this.$router.go(-1); // 返回上一页
      } else {
        // 携带当前路由的query参数跳转
        this.$router.push({
          path: '/region',
          // query: this.$route.query
        });
      }
    },
    // 获取时间日期
    updateDateTime() {
      const now = new Date()

      // Format time (HH:MM:SS)
      this.currentTime = now.toLocaleTimeString('zh-CN', { hour12: false })

      // Format date (YYYY-MM-DD)
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      this.currentDate = `${year}-${month}-${day}`

      // Format day (星期X)
      const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.currentDay = days[now.getDay()]
    },
    // 获取天气、气温
    async fetchWeatherData() {
      try {
        // Replace with your actual weather API call
        // const response = await yourWeatherApiCall()
        // this.weatherData = {
        //   weather: response.weather,
        //   temperature: response.temp
        // }

        // For now, we'll set it to null so weather won't show
        this.weatherData = null
      } catch (error) {
        console.error('Failed to fetch weather data:', error)
        this.weatherData = null
      }
    }
  }
};
</script>

<style scoped lang="less">
.scmp-header {
  background: url("@/assets/header/topbar.png") no-repeat;
  background-size: cover;
  width: 100%;
  height: 175px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;

  // height: 8%;
  .left {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 40px;
    color: #ffffff;
    line-height: 47px;
    letter-spacing: 3px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding: 20px 45px;

    img {
      width: 41px;
      height: 41px;
    }
  }

  .title {
    position: absolute;
    left: 50%;
    right: 50%;
    transform: translate(-50%, 0);
    top: 36px;
  }

  .right {
    font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 40px;
    color: #ffffff;
    line-height: 27px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding: 30px 15px;
  }
}
</style>
