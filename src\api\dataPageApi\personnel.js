import request from "@/utils/requestTemp";
import qs from "qs";

// ⼈事任免-种类详情
export async function getPersonnelDetail(data, token) {
  return request({
    url: "cockpit/personnelAppoint/detail",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
  });
}
// ⼈事任免-种类详情
export async function getPersonnelQuDetail(data, token) {
  return request({
    url: "cockpit/personnelAppoint/quDetail",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
  });
}

// ⼈⼤代表⼯作视窗-本年度市本级⼈事任免情况总览
export async function getpersonnelStatistics(data, token) {
  return request({
    url: "cockpit/personnelAppoint/statistics",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}

// ⼈⼤代表⼯作视窗-本年度区本级⼈事任免情况总览
export async function getpersonnelQuStatistics(data, token) {
  return request({
    url: "cockpit/personnelAppoint/quStatistics",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}

