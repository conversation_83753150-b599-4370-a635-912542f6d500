<template>
  <div class="liaisonStation-page">
    <div class="liaisonStation-main_center">
      <div class="center-calendar">
        <ScmpCard cardName="民意处理情况详情" backgroundImage="card_bg2" :rightPicture="false">
          <div slot="main" class="con-bottom-box" v-loading="loading" element-loading-text="数据加载中..."
            element-loading-background="rgba(0, 0, 0, 0.5)">
            <div class="header">
              <div class="btn" @click="$router.go(-1)">返回</div>
            </div>

            <!-- 议案建议信息展示区域 -->
            <div class="proposal-container">
              <!-- 标题区域 -->
              <!-- <div class="proposal-header">
                <h2>建议号：{{ proposalData.proposalNum }}</h2>
                <div class="status-tag">
                  <el-tag :type="getStatusTagType(proposalData.status)">{{ getStatusText(proposalData.status)
                    }}</el-tag>
                </div>
              </div> -->

              <!-- 基本信息区域 -->
              <div class="basic-info">
                <el-descriptions :column="2" border>
                  <!-- 第一行 -->
                  <el-descriptions-item label="提出群众姓名">
                    {{ proposalData.name || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系方式">
                    {{ proposalData.mobile | mobileFilter }}
                  </el-descriptions-item>

                  <!-- 第二行 -->
                  <el-descriptions-item label="身份证号码">
                    {{ proposalData.idNo | idNoFilter }}
                  </el-descriptions-item>
                  <el-descriptions-item label="反映时间">
                    {{ proposalData.createTime || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第三行 -->
                  <el-descriptions-item label="编号">
                    {{ proposalData.issueNum || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="反映主题">
                    {{ proposalData.title || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第四行 -->
                  <el-descriptions-item label="反映具体内容" :span="2">
                    {{ proposalData.content || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第五行 -->
                  <el-descriptions-item label="联系代表">
                    <span v-for="(db, index) in proposalData.liaisonStationDbs" :key="index">
                      {{ db.userName }}{{ index < proposalData.liaisonStationDbs.length - 1 ? '、' : '' }} </span>
                  </el-descriptions-item>
                  <el-descriptions-item label="联络站">
                    {{ proposalData.liaisonStationName || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第六行 -->
                  <el-descriptions-item label="联络站管理员">
                    {{ proposalData.stationAgentName || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="管理员电话">
                    {{ proposalData.stationAgentPhoneNumber || '暂无数据' }}
                  </el-descriptions-item>

                    <el-descriptions-item label="反映具体内容">
                    {{ proposalData.content || '暂无数据' }}
                  </el-descriptions-item>
                </el-descriptions>

              </div>

              <!-- 操作按钮区域 -->
              <!-- <div class="action-buttons">
                <el-button type="primary" @click="downloadProposal">下载建议纸</el-button>
                <el-button @click="viewContent">查看正文</el-button>
                <el-button @click="uploadAttachment">重新上传正文附件</el-button>
              </div> -->
            </div>
          </div>
        </ScmpCard>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getInfo
} from "@/api/dataPageApi/region";
export default {
  name: "ProposalDetail",
  components: {
    ScmpCard: () => import("@/comps/scmp-YAJYCard"),
    ScmpTable: () => import("@/comps/scmp-table"),
    ScmpPagination: () => import("@/components/pagination/index.vue"),
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      dataList: [],
      dataColum: [],
      listTitle: '',
      majorLastTime: '', // 示例数据
      minorLastTime: '', // 示例数据
      proposalData: {},
      loading: false,
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.dataList.slice(start, end);
    }
  },
  created() {
    if (this.$route.query.title) {
      this.listTitle = this.$route.query.title;
    }
    if (this.$route.query.item) {
      this.dataList = JSON.parse(this.$route.query.item);
    }
    if (this.$route.query.colum) {
      this.dataColum = JSON.parse(this.$route.query.colum);
    }
    this.getInfo(this.$route.query.id)
  },
  mounted() {
  },
  // 在script部分添加过滤器
  filters: {
    mobileFilter(value) {
      return value ? value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '暂无数据'
    },
    idNoFilter(value) {
      return value ? value.replace(/(\d{6})\d{8}(\w{4})/, '$1********$2') : '暂无数据'
    }
  },
  methods: {
    getInfo(id) {
      this.loading = true; // 开始加载，显示转圈
      const params = {
        id: id
      }
      getInfo({ id: id }).then(res => {
        this.proposalData = res.data
      })
        .catch(error => {
          console.error('请求失败', error);
          this.$message.error('数据加载失败');
        })
        .finally(() => {
          this.loading = false; // 加载完成，隐藏转圈
        });
    },
    filterProposalType(type) {
      const typeMap = {
        '1': '建议',
        '2': '议案',
        '3': '批评意见'
      };
      return typeMap[type] || '暂无数据';
    },
    filterProposalContentType(type) {
      const typeMap = {
        'JYFL01': '经济建设',
        'JYFL02': '政治建设',
        'JYFL03': '文化建设',
        'JYFL04': '社会建设',
        'JYFL05': '生态文明建设'
      };
      return typeMap[type] || '暂无数据';
    },
    getStatusText(status) {
      const statusMap = {
        10: '草稿',
        20: '校核中',
        21: '分类中',
        22: '初审中',
        30: '复审中',
        40: '提出分办意见中',
        41: '不予立案审核中',
        42: '分办审核中',
        43: '分办复核中',
        45: '不予立案确认中',
        50: '签收中',
        60: '答复中',
        70: '待评价',
        80: '已评价',
        90: '办结',
      };
      return statusMap[status] || '未知状态';
    },
    getStatusTagType(status) {
      const typeMap = {
        10: 'info',
        20: 'warning',
        21: 'warning',
        22: 'warning',
        30: 'warning',
        40: 'warning',
        41: 'warning',
        42: 'warning',
        43: 'warning',
        45: 'warning',
        50: 'warning',
        60: 'warning',
        70: 'warning',
        80: 'success',
        90: 'success',
      };
      return typeMap[status] || 'info';
    },
    downloadAttachment(attachment) {
      console.log('下载附件:', attachment);
      this.$message.success(`开始下载附件: ${attachment.attName}`);
    },
    previewAttachment(attachment) {
      console.log('预览附件:', attachment);
      this.$message.info(`预览附件: ${attachment.attName}`);
    },
    downloadProposal() {
      console.log('下载建议纸');
      this.$message.success('开始下载建议纸');
    },
    viewContent() {
      console.log('查看正文');
      this.$message.info('查看正文');
    },
    uploadAttachment() {
      console.log('上传附件');
      this.$message.info('打开上传附件对话框');
    },
    handleCurrentChange(val) {
      console.log('当前页:', val);
    },
    handleSizeChange(val) {
      console.log('每页条数:', val);
    }
  }
};
</script>

<style lang="less" scoped>
.liaisonStation-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .header {
    position: absolute;
    right: 60px;
    top: 40px;

    .btn {
      padding: 10px 38px;
      font-family: PingFang SC, PingFang SC;
      /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/
      font-weight: 600;
      font-size: 40px;
      color: #ffffff;
      line-height: 47px;
      letter-spacing: 4px;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
      border-radius: 6px 6px 6px 6px;
      height: 56px;
      cursor: pointer;
    }
  }

  .liaisonStation-main_center {
    flex: 1;
    margin-left: -200px;

    .center-calendar {
      height: 1430px;
      margin-top: 25px;
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      padding-left: 178px;
    }
  }
}

.liaisonStation-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
}

.con-bottom-box {
  height: 1300px;
  padding: 40px 80px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 议案建议信息样式 */
.proposal-container {
  flex: 1;
  overflow-y: auto;
  color: #fff;

  .proposal-header {
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
    margin-bottom: 30px;

    h2 {
      margin: 0;
      font-size: 36px;
      color: #fff;
      font-weight: 500;
    }

    .status-tag {
      margin-left: 40px;

      .el-tag {
        font-size: 28px;
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
      }
    }
  }

  .basic-info {
    margin-bottom: 30px;

    ::v-deep .el-descriptions {
      background: rgba(0, 0, 0, 0.2);

      .el-descriptions__header {
        display: none;
      }

      .el-descriptions-item__label {
        color: #7edcfb;
        font-size: 32px;
        height: 150px;
      }

      .el-descriptions-item__content {
        color: #fff;
        font-size: 32px;
      }

      .el-descriptions__body {
        background: transparent;
      }

      .el-descriptions__table {
        border: 1px solid rgba(126, 220, 251, 0.3);
      }

      .el-descriptions__table td,
      .el-descriptions__table th {
        border-bottom: 1px solid rgba(126, 220, 251, 0.3);
        border-right: 1px solid rgba(126, 220, 251, 0.3);
      }
    }
  }

  .status-info {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(126, 220, 251, 0.3);
    border-radius: 4px;

    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 32px;
      color: #fff;
    }

    .status-item {
      margin-bottom: 15px;
      font-size: 28px;

      label {
        color: #7edcfb;
        margin-right: 15px;
      }

      span {
        color: #fff;
      }
    }
  }

  .attachment-area {
    margin-bottom: 30px;

    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 32px;
      color: #fff;
    }

    ::v-deep .el-table {
      background: transparent;
      color: #fff;
      font-size: 26px;

      th {
        background: rgba(90, 211, 251, 0.3) !important;
        color: #fff;
        font-size: 28px;
      }

      tr {
        background: rgba(0, 0, 0, 0.2) !important;
      }

      td {
        border-bottom: 1px solid rgba(126, 220, 251, 0.3);
      }

      .el-table__empty-block {
        background: rgba(0, 0, 0, 0.2);
      }

      .el-link {
        font-size: 26px;
      }

      .el-button {
        font-size: 24px;
        padding: 8px 12px;
      }
    }
  }

  .action-buttons {
    text-align: center;
    margin-top: 40px;

    .el-button {
      margin: 0 20px;
      font-size: 28px;
      padding: 12px 30px;

      &--primary {
        background: rgba(10, 255, 255, 0.21);
        border-color: #33fefe;
        color: #33fefe;
      }

      &--default {
        background: rgba(57, 205, 255, 0.12);
        border-color: #7edcfb;
        color: #fff;
      }
    }
  }
}

.basic-info {
  ::v-deep .el-descriptions {
    .el-descriptions-item__label {
      background: rgba(90, 211, 251, 0.2);
      color: #7edcfb;
      font-weight: bold;
      text-align: center;
      font-size: 36px; 
      padding: 24px 32px; // 增加内边距
    }

    .el-descriptions-item__content {
      background: rgba(16, 42, 66, 0.4);
      color: #fff;
      padding-left: 15px;
    }

    .el-descriptions__body {
      background: rgba(16, 42, 66, 0.6);
      font-size: 36px; 
      padding: 24px 32px; // 增加内边距
      line-height: 1.8; // 增加行高
    }

    .el-descriptions__table {
      border: 1px solid rgba(126, 220, 251, 0.5);
    }

    td,
    th {
      border-color: rgba(126, 220, 251, 0.3) !important;
      border-width: 2px; // 加粗边框
      padding: 32px; // 单元格内边距增大
      min-width: 400px; // 最小列宽增加
    }

    .el-descriptions__body {
      font-size: 40px;  // 整体字号增大
      padding: 30px 40px;  // 容器内边距增大
    }
  }
}
</style>

<!-- <template>
 

</template> -->