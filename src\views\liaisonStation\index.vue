<template>
  <div class="liaisonStation-page">
    <div class="liaisonStation-main_left">
      <div class="liaisonStation-main_left_top">
        <img :src="require('@/assets/image/tow-c.png')" alt="" />
        <div class="text">
          <!-- 需要动态展示 -->
          <div>{{ areaName }}</div>
          <div>各级人大代表驻站人数</div>
        </div>
      </div>

      <!-- 联络站列表容器 -->
      <div class="liaisonStation-scroll-container" @wheel="handleWheel">
        <!-- 联络站列表 -->
        <div class="liaisonStation-main_left_bottom" v-for="(item, index) in limitedLiaisonStationList" :key="index"
          :class="{ 'active-card': selectedStationId === item.id }" @click="selectStation(item)">
          <div class="title">
            {{ item.name }}<img :src="require('@/assets/image/three_d.png')" alt="" />
          </div>
          <div class="main">
            <div class="main_item">
              <div class="label">地 址：</div>
              <div class="content">{{ item.address }}</div>
            </div>
            <div class="main_item">
              <div class="label">站 长：</div>
              <div class="content">{{ item.stationAgentName }}</div>
            </div>
            <div class="main_item" style="padding-bottom: -30px;">
              <div class="label">联络人：</div>
              <div class="content">{{ item.contactName }}</div>
            </div>
            <div class="enter-button" @click.stop="goToLiaisonStationDetail(item.name, item.id)">
              <span style="font-size: 30px;">进入</span>
              <img :src="require('@/assets/image/three_d.png')" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="liaisonStation-main_semiarc"></div>
    <div class="liaisonStation-main_center">
      <div class="center-top">
        <ScmpSpot class="subkey" :spotName="areaName" dropDownTitle="切换专题" :column="spotDownColumn"
  :dropDownList="dropDownList" @checkedItem="checkedItem"
  :style="{'font-size': areaName.length > 14 ? '36px' : '40px'}">
</ScmpSpot>
      </div>
      <div class="center-card">
        <div class="card">
          <div class="header" style="font-size: 38px;">全国人大代表</div>
          <div class="num"><span>{{ country }}</span>人</div>
        </div>
        <div class="card">
          <div class="header">省人大代表</div>
          <div class="num"><span>{{ province }}</span>人</div>
        </div>
        <div class="card">
          <div class="header">市人大代表</div>
          <div class="num"><span>{{ city }}</span>人</div>
        </div>
        <div class="card">
          <div class="header">区人大代表</div>
          <div class="num"><span>{{ administrativeArea }}</span>人</div>
        </div>
        <div class="card">
          <div class="header">镇人大代表</div>
          <div class="num"><span>{{ streetTown }}</span>人</div>
        </div>
      </div>
      <div class="center-calendar">
        <div class="Conditions">
          <div class="left">
            <span class="left-text">联络站活动列表</span>
          </div>
          <!-- 原日历代码 -->
          <!-- <div class="right">
            <div class="time">{{ currentMonth }}</div>
            <div class="time-select">
              <div class="data-picker-box">
                <el-date-picker class="date-picker"
                  style="width: 180px; height: 100px; font-size: 36px; margin-top: 0px;" prefix-icon="prefix-icon"
                  :clearable="false" v-model="yearValue1" placeholder="选择年份" format="yyyy" value-format="yyyy"
                  type="year" />
                <i class="el-icon-arrow-down"></i>
              </div>
              <div class="data-picker-box">
                <el-date-picker class="date-picker"
                  style="width: 180px; height: 100px; font-size: 36px; margin-top: 0px;" prefix-icon="prefix-icon"
                  :clearable="false" v-model="monthValue1" placeholder="选择月份" format="MM" value-format="MM"
                  type="month" />
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
          </div> -->
        </div>
        <!-- 原日历代码 -->
        <!-- <el-calendar v-model="date">
          <template slot="dateCell" slot-scope="{ date, data }">
            <div class="dateCell-top">
              <div class="left">
                {{ data.day.split("-").slice(2).join("-") }}
              </div>
              <div class="right" v-if="isToday(date)">今天</div>
            </div>
            <div class="dateCell-bottom">
              <el-tooltip effect="dark" placement="top" v-if="getCalendarData(date).length > 0">
                <template #content>
                  <div style="max-width: 300px;">
                    <p v-for="(item, index) in getCalendarData(date)" :key="index">
                      {{ item.calendarTitle }}
                      <span v-if="item.description"> - {{ item.description }}</span>
                    </p>
                  </div>
                </template>
                <span class="event-count">
                 当天 {{ getCalendarData(date).length }} 个活动
                </span>
              </el-tooltip>
            </div>
          </template>
        </el-calendar> -->
        
        <!-- 左右两列布局容器 -->
        <div class="calendar-content-wrapper">
          <!-- 左侧活动列表展示 -->
          <div class="left-activity-section">
            <div class="activity-list-container">
              <div class="activity-list-scroll" v-if="calendarData.length > 0">
                <div 
                  class="activity-item" 
                  v-for="(item, index) in calendarData" 
                  :key="index"
                  @click="handleActivityClick(item)"
                >
                  <div class="activity-date">
                    <div class="date-day">{{ formatDay(item.dateTime) }}</div>
                    <div class="date-month">{{ formatMonth(item.dateTime) }}</div>
                  </div>
                  <div class="activity-content">
                    <div class="activity-title">{{ item.liaisonStationName }}</div>
                    <div class="activity-time">{{ formatTime(item.dateTime) }}</div>
                    <div class="activity-desc" v-if="item.description">{{ item.description }}</div>
                    <div class="activity-type" v-if="item.activityType">{{ item.activityType }}</div>
                  </div>
                  <div class="activity-status">
                    <span class="status-badge" :class="getStatusClass(item)">
                      {{ getStatusText(item) }}
                    </span>
                  </div>
                </div>
              </div>
              
              <!-- 无数据提示 -->
              <div class="no-data" v-else>
                <div class="no-data-icon">📅</div>
                <div class="no-data-text">{{ currentMonth }}暂无活动安排</div>
              </div>
            </div>
          </div>
          
          <!-- 右侧预留区域 -->
          <div class="right-placeholder-section">
      <ScmpCard cardName="问题提交统计情况" style="margin-top:-200px">
        <div slot="main" class="snapshot-main_left_warp">
          <div class="main_left_main_top">
            <div class="main_content_main">
              <div class="chart-container">
                <div class="chart-box" ref="chartBox">
                  <BarChartTop :data="barChartDataTop" :xAxis="xAxisListTop" ref="barChart" />
                  <div class="chart-num">
                    <div v-for="(item, index) in barChartDataTop" :key="index" class="num-item">
                      {{ item }}
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </ScmpCard>

      <!-- 本周扫码反映问题情况 -->
      <ScmpCard cardName="本周扫码反映问题情况" style="margin-top: 20px">
        <div slot="main" class="week-problem-section">
          <div class="week-chart-container">
            <AreaChart :data="weekAreaChartData" :xAxis="weekXAxisList" />
          </div>
        </div>
      </ScmpCard>


          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getLiaisonStationDbCount,
  getLiaisonStationList,
  selectByMap,
  getLiaisonStationListByJSC,
} from "@/api/dataPageApi/liaisonStation";
import {
  getMyselfItemIdentifyCode
} from "@/api/dataPageApi/levelRole";
import {
  getCountMonthOnlineLiaisonStation,
  getOnlineLiaisonStationWeek
} from "@/api/dataPageApi/popularWill";
import store from "../../stores";
import moment from "moment";
export default {
  name: "liaisonStation-index",
  components: {
    ScmpSpot: () => import("@/comps/scmp-spot"),
    BarChartTop: () => import("@/components/snapshot/bar-chart-top2.vue"),
    ScmpCard: () => import("@/comps/scmp-card"),
    AreaChart: () => import("@/views/popularWill/charts/area-chart.vue"),
  },
  data() {
    return {
      yearValue1: String(new Date().getFullYear()),
      monthValue1: String(new Date().getMonth() + 1),
      dayValue1: String(new Date().getDate()),
      date: new Date(),
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],
      currentMonth: this.getCurrentMonth(),
      token: '',
      path: '',
      administrativeArea: '',
      city: '',
      country: '',
      province: '',
      streetTown: '',
      liaisonStationList: [],
      calendarData: [],
      areaName: '',
      administrativeAreaId: 0,
      streetTownId: 0,
      selectedStationId: null, // 存储选中的联络站ID
      currentPage: 0, // 当前页码，用于滚动
      itemsPerPage: 4, // 每页显示的联络站数量
      isScrolling: false, // 是否正在滚动，用于节流
      // 图表数据
      barChartDataTop: [],
      xAxisListTop: [],
      // 本周扫码反映问题情况数据
      weekAreaChartData: [],
      weekXAxisList: [],
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  async created() {
    if (this.$route.query.areaName) {
      this.areaName = this.$route.query.areaName + '联络站工作视窗';
    }
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.streetTownId = this.$route.query.streetTownId;

    // 改造，如果没有传递参数，则去代表后台获取分级分权角色绑定的所属区、所属街道
    if (this.administrativeAreaId == undefined || this.administrativeAreaId == '') {
      await getMyselfItemIdentifyCode({ mainIdentifyCode: 'SJJSC' }).then((res) => {
        let data = res.data;
        this.administrativeAreaId = data[0].administrativeAreaId;
        this.streetTownId = data[0].streetTownId;
        this.areaName = data[0].administrativeAreaName + data[0].streetTownName + '联络站工作视窗';
      });
    }
    this.token = this.$checkToken();
    this.path = this.$route.path
    // 先获取联络站列表
    this.getLiaisonStationList(this.yearValue1, this.administrativeAreaId, this.streetTownId)
      .then(() => {
        // 如果有联络站数据，默认选中第一个
        if (this.liaisonStationList.length > 0) {
          this.selectedStationId = this.liaisonStationList[0].id;
          // 获取选中联络站的相关数据
          this.getLiaisonStationDbCount(this.yearValue1, this.administrativeAreaId, this.streetTownId);
          this.selectByMap(this.yearValue1, this.administrativeAreaId, this.streetTownId);
          // 初始化图表数据
          this.initChartData();
          // 初始化本周扫码反映问题情况数据
          this.initWeekData();
        }
      });
  },
  mounted() { },
  watch: {
    // 监听年份变化
    yearValue1(newVal) {
      this.reloadCalendarData();
    },
    // 监听月份变化
    monthValue1(newVal) {
      this.reloadCalendarData();
    },
    // // 监听选中联络站变化
    // selectedStationId(newVal) {
    //   if (newVal) {
    //     this.refreshChartData();
    //   }
    // }
  },
  computed: {
    limitedLiaisonStationList() {
      const start = this.currentPage * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.liaisonStationList.slice(start, end);
    },
    // 计算最大页数
    maxPage() {
      return Math.ceil(this.liaisonStationList.length / this.itemsPerPage) - 1;
    },
    // 判断是否可以向上滚动
    canScrollUp() {
      return this.currentPage > 0;
    },
    // 判断是否可以向下滚动
    canScrollDown() {
      return this.currentPage < this.maxPage;
    }
  },
  methods: {
    // 格式化日期 - 日
    formatDay(dateTime) {
      if (!dateTime) return '';
      return moment(dateTime).format('DD');
    },
    
    // 格式化日期 - 月
    formatMonth(dateTime) {
      if (!dateTime) return '';
      return moment(dateTime).format('MM月');
    },
    
    // 格式化时间
    formatTime(dateTime) {
      if (!dateTime) return '';
      const time = moment(dateTime);
      const hour = time.get('hour');
      const timeStr = time.format('HH:mm');
      const period = hour >= 12 ? '下午' : '上午';
      return `${period} ${timeStr}`;
    },
    
    // 获取状态样式类
    getStatusClass(item) {
      // 根据时间判断状态
      if (!item.dateTime) return 'status-default';
      
      const now = moment();
      const itemTime = moment(item.dateTime);
      const endTime = moment(item.endTime || item.dateTime).add(2, 'hours'); // 假设活动持续2小时
      
      if (now.isAfter(endTime)) {
        return 'status-completed';
      } else if (now.isBetween(itemTime, endTime)) {
        return 'status-ongoing';
      } else if (now.isBefore(itemTime)) {
        return 'status-pending';
      }
      return 'status-default';
    },
    
    // 获取状态文本
    getStatusText(item) {
      const statusClass = this.getStatusClass(item);
      switch(statusClass) {
        case 'status-completed': return '已完成';
        case 'status-ongoing': return '进行中';
        case 'status-pending': return '待开始';
        default: return '计划中';
      }
    },
    
    // 处理活动点击
    handleActivityClick(item) {
      console.log('点击了活动:', item);
      // 这里可以添加跳转到活动详情页的逻辑
      // 例如：this.$router.push({ path: '/activity-detail', query: { id: item.id } });
    },

    // 跳转联络站详情
    goToLiaisonStationDetail(name, id) {
      // ...其他参数处理
      this.$router.push({
        path: '/liaisonDetail/basicInformation',
        query: {
          ...this.$route.query, // 保留原有query参数
          // areaName,
          areaName: name, // 复杂对象可以序列化
          liaisonStationId: id // 复杂对象可以序列化
        }
      });
    },

    // 选中联络站卡片
    selectStation(item) {
      this.selectedStationId = item.id;
      // 调用API接口获取数据
      // this.getLiaisonStationDbCount(this.yearValue1, this.administrativeAreaId, this.streetTownId);
      // this.selectByMap(this.yearValue1, this.administrativeAreaId, this.streetTownId);
    },
    getCalendarData(value) {
      const date = moment(value).format("YYYY-MM-DD");
      return this.calendarData
        .filter(
          (it) =>
            it.dateTime && date == moment(it.dateTime).format("YYYY-MM-DD")
        )
        .map((it) => {
          const dateTime = moment(it.dateTime);
          return {
            ...it,
            calendarTitle:
              (dateTime.get("hour") > 12 ? "下午" : "上午") +
              dateTime.format("H时m分,") +
              it.liaisonStationName,
          };
        });
    },
    getCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();  // 获取当前年份
      const month = now.getMonth() + 1; // 获取当前月份（注意：getMonth() 返回 0-11，因此需要加 1）
      return `${year}年${month}月`;
    },
    getLiaisonStationDbCount(data, administrativeAreaId, streetTownId) {
      const params = {
        year: data,
        administrativeAreaId: administrativeAreaId,
        streetTownId: streetTownId,
        // liaisonStationId: this.selectedStationId // 添加选中的联络站ID
      }
      getLiaisonStationDbCount(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.administrativeArea = res.data.administrativeArea
          this.city = res.data.city
          this.country = res.data.country
          this.province = res.data.province
          this.streetTown = res.data.streetTown
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getLiaisonStationList(data, administrativeAreaId, streetTownId) {
      const params = {
        pageNum: 1,
        pageSize: 999,
        // 先展示从化区吕田镇
        administrativeAreaId: administrativeAreaId == '440184' ? '440184' : administrativeAreaId,
        streetTownId: streetTownId == '37647' ? '440184107' : streetTownId,
      }
      return getLiaisonStationListByJSC(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.liaisonStationList = res.rows
          return res; // 返回结果以支持链式调用
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
          return Promise.reject(error); // 返回rejected promise以支持链式调用
        });
    },

    selectByMap(data, administrativeAreaId, streetTownId) {
      const params = {
        administrativeAreaId: null,
        endTime: `${this.yearValue1}-${String(this.monthValue1).padStart(2, '0')}-${new Date(this.yearValue1, this.monthValue1, 0).getDate()}`,
        startTime: `${this.yearValue1}-${String(this.monthValue1).padStart(2, '0')}-01`,
        streetTownId: null,
        administrativeAreaId: administrativeAreaId == '440184' ? '440184' : administrativeAreaId,
        streetTownId: streetTownId == '37647' ? '440184107' : streetTownId,
        // liaisonStationId: this.selectedStationId
      };
      selectByMap(params)
        .then(res => {
          this.calendarData = res.rows;
          this.date = new Date(this.yearValue1, this.monthValue1 - 1, 1); // 更新日历显示的月份
        })
        .catch(error => {
          console.error('请求失败', error);
        });
    },


    checkedItem(obj) {
      console.log("选中", obj);
    },

    isToday(dateString) {
      const today = new Date();
      const givenDate = new Date(dateString);
      
      // 检查是否为当前年月日
      return today.getFullYear() === givenDate.getFullYear() && 
             today.getMonth() === givenDate.getMonth() &&
             today.getDate() === givenDate.getDate();
    },

    // 处理鼠标滚轮事件
    handleWheel(event) {
      // 阻止默认滚动行为
      event.preventDefault();

      // 如果联络站数量不超过每页显示数量，则不需要滚动
      if (this.liaisonStationList.length <= this.itemsPerPage) {
        return;
      }

      // 如果正在滚动中，则忽略此次滚动事件（节流）
      if (this.isScrolling) {
        return;
      }

      // 根据滚轮方向决定是向上还是向下滚动
      // deltaY > 0 表示向下滚动，deltaY < 0 表示向上滚动
      if (event.deltaY > 0 && this.canScrollDown) {
        // 向下滚动
        this.isScrolling = true;
        this.currentPage++;
        this.updateSelectedStation();

        // 300ms后重置滚动状态，与CSS过渡时间一致
        setTimeout(() => {
          this.isScrolling = false;
        }, 300);
      } else if (event.deltaY < 0 && this.canScrollUp) {
        // 向上滚动
        this.isScrolling = true;
        this.currentPage--;
        this.updateSelectedStation();

        // 300ms后重置滚动状态，与CSS过渡时间一致
        setTimeout(() => {
          this.isScrolling = false;
        }, 300);
      }
    },

    // 更新选中的联络站
    updateSelectedStation() {
      // 如果当前选中的联络站不在可见范围内，则选中当前页的第一个
      const visibleStations = this.limitedLiaisonStationList;
      if (!visibleStations.find(station => station.id === this.selectedStationId) && visibleStations.length > 0) {
        this.selectStation(visibleStations[0]);
      }
    },
    // 新增统一刷新数据方法
    // 判断当前选择的年月是否为当月
    isCurrentMonth() {
      const today = new Date();
      return this.yearValue1 === today.getFullYear() && 
             this.monthValue1 === today.getMonth() + 1;
    },
    
    reloadCalendarData() {
      // 重新获取日历数据
      this.selectByMap(this.yearValue1, this.administrativeAreaId, this.streetTownId);
      
      // 如果不是当前年月，则隐藏"今天"标识
      if (!this.isCurrentMonth()) {
        this.hasTodayEvent = false;
      }
      
      // 重新获取联络站统计数据（可选，根据业务需求）
      if (this.selectedStationId) {
        this.getLiaisonStationDbCount(this.yearValue1, this.administrativeAreaId, this.streetTownId);
      }
      
      // 重新加载图表数据
      this.refreshChartData();

      // 重新加载本周数据
      this.getWeekData(this.administrativeAreaId, this.streetTownId, this.yearValue1);
    },

    // 初始化图表数据
    initChartData() {
      this.getChartData(this.administrativeAreaId, this.streetTownId, this.yearValue1);
    },

    // 获取图表数据
    getChartData(administrativeAreaId, streetTownId, year) {
      const params = {
        streetTownId: streetTownId,
        administrativeAreaId: administrativeAreaId,
        // liaisonStationId: this.selectedStationId,
      }
      
  
      
      // 不清空柱状图数据，保持原数据直到新数据准备好
      getCountMonthOnlineLiaisonStation(params)
        .then(res => {
          console.log('图表数据获取成功:', res);
          let newBarChartDataTop = [];
          let newXAxisListTop = [];
          if (res && res.data && Array.isArray(res.data)) {
            // 构建 month-num 映射
            const monthNumMap = {};
            res.data.forEach(item => {
              const m = Number(item.month);
              monthNumMap[m] = item.num || 0;
            });
            // 按 1~12 月填充数据，缺失补0
            for (let i = 1; i <= 12; i++) {
              newXAxisListTop.push(`${i}`);
              newBarChartDataTop.push(monthNumMap[i] !== undefined ? monthNumMap[i] : 0);
            }
          } else {
            console.warn('数据格式不正确:', res);
            // 显示默认数据
            for (let i = 1; i <= 12; i++) {
              newBarChartDataTop.push(0);
              newXAxisListTop.push(`${i}`);
            }
          }
          // 数据准备好后再整体赋值，防止抖动
          this.barChartDataTop = newBarChartDataTop;
          this.xAxisListTop = newXAxisListTop;
        })
        .catch(error => {
          console.error('获取图表数据失败:', error);
          // 错误情况下显示默认数据
          let newBarChartDataTop = [];
          let newXAxisListTop = [];
          for (let i = 1; i <= 12; i++) {
            newBarChartDataTop.push(0);
            newXAxisListTop.push(`${i}`);
          }
          this.barChartDataTop = newBarChartDataTop;
          this.xAxisListTop = newXAxisListTop;
        })
        .finally(() => {
      
        });
    },

    // 刷新图表数据
    refreshChartData() {
      this.getChartData(
        this.administrativeAreaId,
        this.streetTownId,
        this.yearValue1
      );
    },

    // 初始化本周扫码反映问题情况数据
    initWeekData() {
      this.getWeekData(this.administrativeAreaId, this.streetTownId, this.yearValue1);
    },

    // 获取本周扫码反映问题情况数据
    getWeekData(administrativeAreaId, streetTownId, year) {
      const params = {
        year: year,
        administrativeAreaId: administrativeAreaId,
        streetTownId: streetTownId,
      }

      getOnlineLiaisonStationWeek(params)
        .then(res => {
          console.log('本周扫码反映问题情况数据获取成功:', res);
          if (res && res.data && Array.isArray(res.data)) {
            // 按日期排序
            const sortedData = res.data.sort((a, b) => {
              return new Date(a.dayName) - new Date(b.dayName);
            });
console.log(sortedData,"111111111111111111111111111111111111")
            this.weekXAxisList = sortedData.map(item => item.dayName);
            this.weekAreaChartData = sortedData.map(item => Number(item.num));
          } else {
            console.warn('本周数据格式不正确:', res);
            // 显示默认数据
            this.weekXAxisList = [];
            this.weekAreaChartData = [];
          }
        })
        .catch(error => {
          console.error('获取本周扫码反映问题情况数据失败:', error);
          // 错误情况下显示默认数据
          this.weekXAxisList = [];
          this.weekAreaChartData = [];
        });
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
::v-deep .scmp-spot {
  width: 1290px !important;
  
  .title {
    left: 43% !important;
    font-size: inherit !important;
    white-space: normal !important;
    word-break: break-word !important;
  }
}

.liaisonStation-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .liaisonStation-main_left {
    width: 761px;
    height: 1860px;
    // background: #fff;
    margin-top: 54px;
    margin-bottom: 25px;
    position: relative;

    .liaisonStation-scroll-container {
      position: relative;
      height: calc(100% - 300px);
      /* 减去顶部区域的高度 */
      overflow: hidden;
      /* 隐藏溢出内容 */
      transition: all 0.3s ease;
      /* 添加平滑过渡效果 */
    }

    .liaisonStation-main_left_top {
      display: flex;
      flex-direction: row;
      margin-top: 150px;
      align-items: flex-end;
      margin-bottom: 118px;
      padding-left: -150px;
      width: 800px;

      img {
        position: absolute;
        left: -90px;
        width: 160px;
        height: 166px;
        margin-right: 50px;
        margin-bottom: 20px;
      }

      .text {
        margin-left: 80px;
        font-family: PangMenZhengDao, PangMenZhengDao;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 46px;
        color: #ffffff;
        line-height: 70px;
        text-shadow: 0px 0px 9px #158eff;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .liaisonStation-main_left_bottom {
      width: 741px;
      height: 286px;
      background: url("@/assets/image/xsb.png") no-repeat;
      background-size: cover;
      margin-bottom: 75px;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active-card {
        background: linear-gradient(90deg, rgba(255, 215, 0, 0.15) 0%, rgba(218, 165, 32, 0.6) 100%), url("@/assets/image/xsb.png") no-repeat;
        background-size: cover;
        /*transform: scale(1.02);*/
        position: relative;
        z-index: 10;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 18px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
        border: 3px solid #b18c3f;

        .title {
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .main_item .label,
        .main_item .content {
          color: #ffffff;
          font-weight: 500;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .enter-button {
          background: linear-gradient(90deg, rgba(255, 215, 0, 0.8) 0%, rgba(218, 165, 32, 0.9) 100%);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);

          &:hover {
            background: linear-gradient(90deg, rgba(255, 215, 0, 0.9) 0%, rgba(218, 165, 32, 1) 100%);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
          }
        }
      }
    }

    .title {
      padding-left: 55px;
      height: 42px;
      line-height: 52px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 30px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      text-transform: none;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 800px;
    }

    img {
      width: 55px;
      height: 13px;

      vertical-align: middle;
      margin-left: 17px;
    }

    .main {
      padding: 27px 0 0 91px;
      display: flex;
      flex-direction: column;
      position: relative;

      .main_item {
        margin-top: 7px;
        display: flex;
        flex-direction: row;
        justify-content: start;

        .label {
          width: 20%;
          height: 42px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #ffffff;
          line-height: 35px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .content {
          flex: 1;
          height: auto;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #ffffff;
          line-height: 35px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          word-wrap: break-word;
          white-space: normal;
          display: -webkit-box;
          /* 必须结合 -webkit-box 使用 */
          -webkit-box-orient: vertical;
          /* 垂直排列子元素 */
          -webkit-line-clamp: 2;
          line-clamp: 2;
          /* 限制显示的行数 */
          overflow: hidden;
          /* 隐藏溢出内容 */
          text-overflow: ellipsis;
          /* 用省略号表示溢出内容 */
        }
      }

      .enter-button {
        position: absolute;
        right: 40px;
        bottom: -20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(90deg, rgba(0, 89, 255, 0.26) 0%, rgba(1, 25, 66, 0.8) 100%);
        border: 1px solid rgba(59, 134, 255, 0.27);
        border-radius: 8px;
        padding: 8px 20px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(90deg, rgba(0, 89, 255, 0.4) 0%, rgba(1, 25, 66, 0.9) 100%);
          box-shadow: 0 0 10px rgba(41, 241, 250, 0.5);
        }

        span {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          margin-right: 10px;
        }

        img {
          width: 40px;
          height: 10px;
          vertical-align: middle;
        }
      }
    }
  }


  .liaisonStation-main_semiarc {
    margin-left: -85px;
    width: 320px;
    height: 1860px;
    background: url("@/assets/image/light_semi.png") no-repeat;
    background-size: 100% 100%;
  }

  .liaisonStation-main_center {
    flex: 1;
    margin-left: -200px;
    // width: 1992px;
    // position: relative;
    // left: 50%;
    // transform: translateX(-50%);

    .center-top {
      display: flex;
      flex-direction: row;
      justify-content: center;
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      line-height: 25px;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      position: relative;

      .subkey {
        position: absolute;
        left: 430px;
      }
    }

    .center-card {
      margin-left: 25px;
      margin-top: 160px;
      height: 329px;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: end;

      .card {
        width: 575px;
        height: 100%;
        background: url("@/assets/image/bg_r_r.png") no-repeat;
        background-size: contain;
        position: relative;
        z-index: 99;

        .header {
          height: 53px;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 46px;
          color: #ffffff;
          line-height: 48px;
          text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
          -webkit-text-stroke: 0px rgba(255, 255, 255, 0.11);
          text-align: left;
          font-style: normal;
          text-transform: none;
          -webkit-text-stroke: 0px rgba(255, 255, 255, 0.11);
          padding: 67px 0 0 66px;
        }

        .num {
          padding: 12px 0 0 66px;
          // font-family: DIN, DIN;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          // font-weight: bold;
          font-weight: 400;
          font-size: 46px;
          color: #ffffff;
          line-height: 35px;
          -webkit-text-stroke: 1px rgba(255, 255, 255, 0.11);
          font-style: normal;
          text-transform: none;
          -webkit-text-stroke: 1px rgba(255, 255, 255, 0.11);

          span {
            margin-right: 21px;
            font-family: DIN-MediumItalic, DIN-MediumItalic;
            font-weight: 400;
            font-size: 50px;
            color: #29f1fa;
            line-height: 59px;
            text-shadow: 0px 0px 7px rgba(255, 179, 179, 0.71);
            -webkit-text-stroke: 0px #ffffff;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #ca320b 100%),
              linear-gradient(90deg, #f6ffff 0%, #29f1fa 100%),
              linear-gradient(270.00000009864743deg,
                rgba(255, 144, 62, 0) 0%,
                #fbe947 56%);
            -webkit-text-stroke: 0px #ffffff;
            -webkit-background-clip: text;
            /* WebKit 浏览器专用属性，使背景剪切至文本 */
            background-clip: text;
            /* 标准属性 */
          }
        }
      }
    }

    .center-calendar {
      // width: 2912px;
      height: 1430px;
      margin-top: -65px;
      background: url("@/assets/image/semi-arc.png") no-repeat;
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      padding-left: 178px;

      .Conditions {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .left {
          width: 598px;
          height: 121px;
          padding: 65px 0 0 168px;
          background: url("@/assets/image/rl-name.png") no-repeat;
          background-size: contain;
          font-family: PingFang SC, PingFang SC;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 600;
          font-size: 52px;
          color: #ffffff;
          line-height: 47px;
          letter-spacing: 4px;
          text-shadow: 0px 2px 3px rgba(51, 158, 188, 0.79);
          text-align: center;
          font-style: normal;
          text-transform: none;

          .left-text {
            margin-left: -50px;
          }
        }

        .right {
          margin-top: 50px;
          width: 1865px;
          height: 71px;
          background: linear-gradient(257deg,
              rgba(22, 80, 106, 0) 0%,
              rgba(3, 31, 126, 0.81) 57%,
              rgba(0, 81, 162, 0.63) 100%);
          border-radius: 0px 0px 0px 0px;
          border: 4px solid rgba(59, 134, 255, 0.27);
          display: flex;
          flex-direction: row;
          justify-content: start;
          align-items: center;

          .time {
            font-family: Poppins, Poppins;
            font-weight: 500;
            font-size: 30px;
            color: #ffffff;
            line-height: 35px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin: 0 45px;
          }

          .time-select {
            display: flex;
            flex-direction: row;
          }

          .data-picker-box {
            margin-right: 100px;
            position: relative;
            width: 124px;
            height: 66px;

            .prefix-icon {
              display: none;
            }

            .date-picker {
              width: 100%;
              height: 100%;
              position: absolute;

              ::v-deep .el-input__inner {
                background: transparent;
                color: #fff;
                font-size: 20px;
                padding: 0 38px 0 8px;
                border: 1px solid #b5b5b5;
                border-radius: 4px;
              }
            }

            .el-icon-arrow-down {
              position: absolute;
              right: -10px;
              top: 50%;
              transform: translateY(-50%);
              color: #fff;
              font-size: 24px;
            }
          }
        }
      }
      
      // 左右两列布局样式
      .calendar-content-wrapper {
        display: flex;
        width: 100%;
        height: calc(100% - 200px);
        margin-top: 20px;
        gap: 20px;
      }
      
      .left-activity-section {
        flex: 1;
        max-width: 50%;
        height: 100%;
      }
      
      .right-placeholder-section {
        flex: 1;
        max-width: 50%;
        height: 100%;
        // 可以在这里添加右侧内容的样式
      }
      
      // 活动列表加边框
      .activity-list-container {
        height: 98.4%;
        padding: 0px;
        overflow: hidden;
        border: 4px solid rgba(59,134,255,0.27);
        border-radius: 16px;
        margin-left: 10px; // 往右移动
        margin-right: 10px; // 往左移动
        margin-bottom: 50px; // 下边框距离下方内容
        width: 95%; // 控制整体宽度
      }
      
      .activity-list-scroll {
        height: 100%;
        overflow-y: auto;
        padding-right: 10px;
        
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: rgba(59, 134, 255, 0.5);
          border-radius: 3px;
          
          &:hover {
            background: rgba(59, 134, 255, 0.7);
          }
        }
      }
      
      .activity-item {
        background: linear-gradient(90deg, rgba(0, 89, 255, 0.15) 0%, rgba(1, 25, 66, 0.25) 100%);
        border: 1px solid rgba(59, 134, 255, 0.3);
        border-radius: 12px;
        padding: 15px 20px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 80px;
        
        &:hover {
          background: linear-gradient(90deg, rgba(0, 89, 255, 0.25) 0%, rgba(1, 25, 66, 0.4) 100%);
          border-color: rgba(59, 134, 255, 0.6);
          transform: translateX(3px);
          box-shadow: 0 4px 12px rgba(0, 89, 255, 0.2);
        }
        
        .activity-date {
          width: 70px;
          text-align: center;
          margin-right: 15px;
          flex-shrink: 0;
          
          .date-day {
            font-family: DIN-MediumItalic, DIN-MediumItalic;
            font-size: 50px;
            color: #29f1fa;
            line-height: 1;
            font-weight: bold;
            text-shadow: 0px 0px 5px rgba(41, 241, 250, 0.5);
          }
          
          .date-month {
            font-family: PingFang SC, PingFang SC;
            font-size: 40px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 2px;
          }
        }
        
        .activity-content {
          flex: 1;
          min-width: 0;
          
          .activity-title {
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-size: 38px;
            color: #ffffff;
            margin-bottom: 4px;
            font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .activity-time {
            font-family: PingFang SC, PingFang SC;
            font-size: 40px;
            color: #29f1fa;
            margin-bottom: 3px;
          }
          
          .activity-desc {
            font-family: PingFang SC, PingFang SC;
            font-size: 30px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.3;
            max-height: 36px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
          }
          
          .activity-type {
            font-family: PingFang SC, PingFang SC;
            font-size: 30px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 2px;
          }
        }
        
        .activity-status {
          margin-left: 10px;
          flex-shrink: 0;
          
          .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 35px;
            font-weight: 500;
            white-space: nowrap;
            
            &.status-completed {
              background: rgba(103, 194, 58, 0.2);
              color: #67c23a;
              border: 1px solid rgba(103, 194, 58, 0.4);
            }
            
            &.status-ongoing {
              background: rgba(230, 162, 60, 0.2);
              color: #e6a23c;
              border: 1px solid rgba(230, 162, 60, 0.4);
              animation: pulse 2s infinite;
            }
            
            &.status-pending {
              background: rgba(144, 147, 153, 0.2);
              color: #909399;
              border: 1px solid rgba(144, 147, 153, 0.4);
            }
            
            &.status-default {
              background: rgba(64, 158, 255, 0.2);
              color: #409eff;
              border: 1px solid rgba(64, 158, 255, 0.4);
            }
          }
        }
      }
      
      .no-data {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        
        .no-data-icon {
          font-size: 48px;
          margin-bottom: 15px;
          opacity: 0.6;
        }
        
        .no-data-text {
          font-family: PingFang SC, PingFang SC;
          font-size: 30px;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }
}

@keyframes pulse {
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.liaisonStation-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
  /* 设置透明度 */
}

::v-deep .el-calendar__header {
  display: none;
}

::v-deep .el-calendar {
  background: transparent;
  margin-top: -55px;
}

::v-deep .el-calendar-table thead th {
  padding: 45px 0;
  font-family: Poppins, Poppins;
  font-weight: 500;
  font-size: 30px;
  color: #ffffff;
  line-height: 35px;
  font-style: normal;
  text-transform: none;
  text-align: center;
}

::v-deep .el-calendar-table thead th:before {
  font-family: Poppins, Poppins;
  font-weight: 500;
  font-size: 30px;
  color: #ffffff;
  line-height: 35px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  content: "周";
}

::v-deep .el-calendar-table .el-calendar-day {
  height: 189px;
  background: linear-gradient(180deg, #030814 0%, rgba(0, 89, 255, 0.26) 100%),
    rgba(1, 25, 66, 0.8), rgba(0, 0, 0, 0.2);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid rgba(218, 220, 224, 0.6);
}

.dateCell-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 12px;

  .left {
    font-family: Poppins, Poppins;
    font-weight: 500;
    font-size: 40px;
    color: #ffffff;
    line-height: 47px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .right {
    width: 116px;
    height: 48px;
    background: #ffffff;
    border-radius: 11px 11px 11px 11px;
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 30px;
    color: #0612c0;
    line-height: 35px;
    font-style: normal;
    text-transform: none;
  }
}

.dateCell-bottom {
  height: 90px;
  font-family: Poppins, Poppins;
  font-weight: 500;
  font-size: 30px;
  color: #ffffff;
  line-height: 35px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  align-items: center;

  .rich {
    width: 20px;
    height: 20px;
    background: #8fdcb2;
    border-radius: 10px;
  }
}

::v-deep .el-calendar-table td.is-selected {
  background: rgba(6, 17, 216, 0.84);
}

/* ghw mod */
::v-deep .el-calendar-day:hover {
  background-color: #f0f0f0;

  .event-text,
  .left {
    color: #000 !important;
  }

  .event-count {
    color: #000 !important;
  }
}

.el-calendar-day {
  &:hover {
    .event-count {
      color: #000 !important;
    }
  }
}

::v-deep .el-tooltip.event-count {
  color: inherit;
  &:hover {
    color: #000 !important;
  }
}

::v-deep .el-tooltip__popper {
  color: inherit;
  .el-tooltip__popper__content {
    &:hover {
      color: #000 !important;
    }
  }
}

.dateCell-bottom {
  height: 100px;
  max-height: calc(286px * 4);
  overflow-y: auto;
  max-height: 500px; // 设置一个合适的最大高度
  /* 固定高度 */
  overflow-y: auto;
  /* 超出内容滚动 */
}

.events {
  list-style: none;
  padding: 0;
  margin: 0;
}

.events li {
  margin-bottom: 4px;
}

.event-item {
  display: flex;
  align-items: center;
}

.rich {
  flex-shrink: 0;
  /* 确保绿点不会被挤压 */
  width: 8px;
  height: 8px;
  background: #67c23a;
  /* 绿色圆点 */
  border-radius: 50%;
  /* 圆形 */
  margin-right: 8px;
}

.event-text {
  flex: 1;
  /* 占据剩余空间 */
  font-size: 14px;
  color: #fff;
  /* 文字颜色 */
  overflow: hidden;
  /* 超出部分隐藏 */
  max-width: 90%;
  /* 限制最大宽度 */
}

/* 图表相关样式 */
.chart-container {
  position: relative;
  width: 100%;
  height: 520px;
  
  .chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    
    .loading-text {
      color: #666;
      font-size: 14px;
    }
  }
  
    .chart-box {
      width: 100%;
      height: 100%;
      position: relative;
      padding-top: 30px;
      .chart-num {
        position: absolute;
        top: 0px;
        left: 0;
        width: 100%;
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        pointer-events: none;
        z-index: 10;
      }
      .num-item {
        color: #fff;
        font-size: 30px;
        font-weight: bold;
        text-align: center;
        width: 8.33%;
        /* 12个柱子，100/12 */
        text-shadow: 0 2px 12px #000;
      }
    }
}

/* 本周扫码反映问题情况样式 */
.week-problem-section {
  width: 100%;
  height: 100%;

  .week-chart-container {
    width: 100%;
    height: 400px;
    padding: 20px;
  }
}
</style>
