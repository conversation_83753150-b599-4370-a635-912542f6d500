import request from "@/utils/requestTemp";
import qs from "qs";

// 代表履职情况⼯作视窗-今⽇活动情况
export async function getDailyDutyActivityInfo(data, token) {
  return request({
    url: "cockpit/getDailyDutyActivityInfo",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
  });
}

// 获取活动详情
export async function getDutyActiveById(id) {
  return request({
    url: `/dutyActive/getById`,
    method: "get",
    params: { id },
  });
}

// 代表履职情况⼯作视窗-代表履职积分情况列表
export async function getDutyAppraiseList(params,data) {
  return request({
    url: "cockpit/getDutyAppraiseList",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    params: params,  // 请求数据
  });
}

// 代表履职情况⼯作视窗-履职电⼦档案
export async function getDutyElectronicArchivesList(params,data) {
  return request({
    url: "cockpit/getDutyElectronicArchivesList",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    params: params,  // 请求数据
  });
}

// 代表履职情况⼯作视窗-年度⽉度活动次数
export async function getDutyInfoNumForMonthAndYear(data, token) {
  return request({
    url: "cockpit/getDutyInfoNumForMonthAndYear",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
  });
}

// 代表履职情况⼯作视窗-每⽉活动排名
export async function getMonthDutyForDistrict(data, token) {
  return request({
    url: "cockpit/getMonthDutyForDistrict",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    // headers: {
    //   "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    // },                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
  });
}

// 代表履职情况⼯作视窗-今年排名情况
export async function getYearDutyForDistrict(data, token) {
  return request({
    url: "cockpit/getYearDutyForDistrict",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
  });
}

// 代表履职情况⼯作视窗-今年活动列表
export async function getYearDutyForDistrictList(data, params) {
  return request({
    url: "cockpit/getYearDutyForDistrictList",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    params: params,  // 请求数据
  });
}

// 代表履职情况⼯作视窗-月活动列表
export async function getMonthDutyForDistrictList(data, params) {
  return request({
    url: "cockpit/getMonthDutyForDistrictList",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    params: params,  // 请求数据
  });
}


