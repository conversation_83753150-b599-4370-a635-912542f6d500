import Vue from "vue";
console.log(Vue.prototype, "VueVueVueVue");
const state = {
  GLOBAL: "",
  titleText: {
    text: "平台门户",
    text1: "",
    text2: "",
    text3: "",
    text4: "",
    menuData: { menuId: "", systemId: "", hidden: "" },
  },
};
// hidden=1 是隐藏菜单 0是显示的
const mutations = {
  GLOBAL: (state, text) => {
    console.log("1111111111111111111111111111", text);
    state.titleText.GLOBAL = text;
  },
  menuData: (state, text) => {
    state.titleText.menuData = text;
  },
  title: (state, text) => {
    state.titleText.text = text;
  },
  title1: (state, text) => {
    state.titleText.text1 = text;
  },
  title2: (state, text) => {
    state.titleText.text2 = text;
  },
  title3: (state, text) => {
    state.titleText.text3 = text;
  },
  title4: (state, text) => {
    state.titleText.text4 = text;
  },
};
const actions = {
  GLOBALs({ commit }, text) {
    commit("GLOBAL", text);
  },
  menuDatas({ commit }, text) {
    commit("menuData", text);
  },
  breadcrumb({ commit }, text) {
    commit("title", text);
  },
  breadcrumb1({ commit }, text) {
    commit("title1", text);
  },
  breadcrumb2({ commit }, text) {
    commit("title2", text);
  },
  breadcrumb3({ commit }, text) {
    commit("title3", text);
  },
  breadcrumb4({ commit }, text) {
    commit("title4", text);
  },
};
export default { state, mutations, actions };
