<template>
  <div ref="chart" class="chart"></div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "barChart",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    xAxis: {
      type: Array,
      default: () => [],
    },
    tooltip: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    // 初始化视图
    if (this.data) {
      this.getIint(this.data);
    }
  },
  watch: {
    data: {
      handler(val) {
        this.getIint(val);
      },
      deep: true,
    },
  },
  methods: {
    getIint() {
      let myChart = this.$echarts.init(this.$refs.chart);

      // 修改排序方式为降序
      let echartData = [...this.data].sort((a, b) => b.name.localeCompare(a.name));
      console.log(echartData)
      // 使用排序后的数据
      let xAxisData = echartData.map((v) => v.month);
      let yAxisData1 = echartData.map((v) => v.value1);
      let yAxisData2 = echartData.map((v) => v.value2);
      console.log(xAxisData)
      console.log(yAxisData1)
      console.log(yAxisData2)

      let bgdata = [];
      echartData.map((item) => {
        bgdata.push(parseInt(item.value1 + item.value2) + 100);
      });
      let maxxAxis = Math.max.apply(null, bgdata); //设置x轴最大值

      let option = {
        tooltip: {
          show: this.tooltip,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(77, 77, 77, 0.9)',
          borderColor: 'rgba(77, 77, 77, 0.9)',
          textStyle: {
            color: '#fff',
            fontSize: 32,
            fontWeight: 'bold'
          },
          padding: [15, 20],
          formatter: (params) => {
            let str = `<div style="font-size: 28px;font-weight: bold;margin-bottom: 12px;">${echartData[params[0].dataIndex].name}</div>`;
            params.forEach(item => {
              if (item.seriesName !== "最长背景") {
                const color = item.seriesName === "未办结" ? "rgba(255, 0, 4, 1)" : "rgba(54, 240, 151, 1)";
                str += `<div style="margin: 8px 0;font-size: 24px;font-weight: bold;display: flex;align-items: center;">
                  <span style="display:inline-block;width:16px;height:16px;background-color:${color};margin-right:10px;border-radius:2px;"></span>
                  ${item.seriesName}：${item.value}
                </div>`;
              }
            });
            return str;
          }
        },
        legend: {
          orient: "horizontal",
          left: "auto",
          right: "0%",
          top: "0%",
          icon: "rect",
          selectedMode: false,
          itemWidth: 20,
          itemHeight: 20,
          itemGap: 30,
          textStyle: {
            color: "#FFFFFF",
            fontSize: "24px",
            padding: [0, 0, 0, 8]
          },
          data: ["未办结", "已办结"]
        },
        grid: {
          // left: "3%",
          left: "0%",
          right: "0%",
          // top: "4%",
          bottom: "0%",
          // height: (12 * 80) + "px",
          containLabel: true,
        },
        yAxis: {
          type: "category",
          triggerEvent: true,
          data: xAxisData,
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: "#FFFFFF",
            fontSize: "30",
            interval: 0,
            margin: 20,  // 关键：直接增加标签的外边距
            // rotate: rotate//文字旋转角度
          },
          axisTick: {
            show: false,
            alignWithLabel: true,
            lineStyle: {
              color: "#0C4F81",
              type: "solid",
            },
          },
        },
        xAxis: {
          type: "value",
          max: maxxAxis,
          nameTextStyle: {
            color: "#4F88BD",
            padding: [0, 0, 0, 0],
            fontSize: 30,
            fontFamily: "Microsoft YaHei",
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "#0C4F81",
            },
          },
          axisLabel: {
            show: false, //
            color: "#4F88BD",
            fontSize: "30",
            formatter: "{value}",
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: "dotted",
              color: "#0C4F81",
            },
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: "未办结",
            type: "bar",
            barMaxWidth: 46,
            barMinWidth: 46,
            barWidth: 46,
            stack: "Ad",
            barGap: "-100%", // 使得柱子之间有重叠
            barCategoryGap: "50%", // 类目间的间隔
            emphasis: {
              focus: "series",
            },
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  // position: 'top',
                  fontSize: "30",
                  color: "#ffffff",
                },
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: "rgba(255, 177, 178, 1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(255, 0, 4, 1)",
                  },
                ]),
                borderRadius: [24, 0, 0, 24], //柱设置为圆角
              },
            },
            data: yAxisData1,
          },
          {
            name: "已办结",
            type: "bar",
            barMaxWidth: 46,
            barMinWidth: 46,
            barWidth: 46,
            stack: "Ad",
            emphasis: {
              focus: "series",
            },
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  // position: 'top',
                  fontSize: "30",
                  color: "#ffffff",
                },
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(54, 240, 151, 1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(54, 240, 151, 1)",
                  },
                ]),
                borderRadius: [0, 24, 24, 0], //柱设置为圆角
              },
            },
            data: yAxisData2,
          },
          {
            // 为了处理markline
            name: "最长背景",
            type: "bar",
            barMaxWidth: 46,
            barMinWidth: 46,
            barWidth: 46,
            color: "transparent",
            showInLegend: false,  // 在图例中隐藏此系列
            data: bgdata,
          },
        ],
      };

      myChart.clear();
      myChart.setOption(option);
    },
  },
};
</script>
<style lang="less" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
