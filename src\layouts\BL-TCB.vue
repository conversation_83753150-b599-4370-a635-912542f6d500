<template>
  <div class="main">
    <div class="loading-mask" v-if="loading">
      <div class="loading-spinner">
        <!-- <i class="el-icon-loading"></i> -->
        <span>Loading...</span>
      </div>
    </div>
    <el-container class="main_container">
      <el-header height="175px">
        <ScmpHeader> </ScmpHeader>
      </el-header>
      <el-main>
        <div class="main_main">
          <router-view :key="key" />
          <el-footer height="78px"></el-footer>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapState } from "vuex";
export default {
  name: "BLTCB",
  components: {
    ScmpHeader: () => import("@/comps/scmp-header"),
  },
  data() {
    return {
      activeIdx: "",
      resizeTimer: null,
      loading: true,
    };
  },
  computed: {
    ...mapState("auth/menu", {
      menuArray: "menu",
    }),
    ...mapGetters("auth/user", [
      "userNameGetter",
      "userLoginNameGetter",
      "userMobilePhoneGetter",
      "userPhotoGetter",
    ]),
    key() {
      return new Date().getTime();
    },
    // isRegionOrJunior() {
    //   return this.$route.path === "/region" || this.$route.path === "/junior";
    // }
  },
  created() {
    // 显示加载蒙板
    this.loading = true;
    this.resize();
  },
  mounted() {
    const resizeHandler = () => {
      clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.resize();
        this.loading = false;
      }, 1000); // 防抖时间从100ms调整为300ms
    };

    window.addEventListener('resize', resizeHandler);
    // 等待DOM更新完成后获取高度
    this.$nextTick(() => {
      // 延迟调用以确保所有内容都已渲染
      setTimeout(() => {
        this.resize();
        // 隐藏加载蒙板
        this.loading = false;
      }, 1000);
    });
  },
  destroyed() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.resize);
    // 清除定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
  },
  methods: {
    ...mapActions("auth/user", {
      actLogout: "ACT_Logout",
    }),
    async resize() {
      this.loading = true; // 开始计算时显示加载状态
      await this.$nextTick();

      return new Promise(resolve => {
        requestAnimationFrame(() => {
          // 获取实际内容高度
          const mainContainer = document.querySelector('.main_container');
          const mainMain = document.querySelector('.main_main');
          // const routerView = mainMain?.querySelector('router-view');

          // 获取各种高度值，包括router-view内部元素的高度
          const allContentHeights = Array.from(mainContainer?.children || []).map(el => el.offsetHeight);
          const maxContentHeight = Math.max(0, ...allContentHeights);

          // 获取各种高度值
          const docHeight = Math.max(
            document.documentElement.scrollHeight,
            document.documentElement.offsetHeight,
            document.documentElement.clientHeight,
            maxContentHeight + 175 + 78 // header高度 + footer高度
          );

          console.log('文档总高度:', docHeight);
          console.log('容器高度:', mainContainer?.offsetHeight);
          console.log('内容区高度:', mainMain?.offsetHeight);
          console.log('内容元素高度列表:', allContentHeights);

          // 系统整体缩放
          const cliWidth = document.documentElement.clientWidth || document.body.clientWidth;
          const cliHeight = document.documentElement.clientHeight || document.body.clientHeight;
          const contW = 3840;
          const contH = mainContainer?.offsetHeight - 20; // 使用实际文档高度并添加偏移量

          const w = cliWidth / contW;
          const h = cliHeight / contH;

          const appDom = document.querySelector("#app");
          if (appDom) {
            appDom.style.transform = `scale(${w},${h})`;
            appDom.style.transformOrigin = "top left";
            appDom.style.width = contW + "px";
          }
        });
      }).finally(() => {
        // setTimeout(() => this.loading = false, 100);
        this.loading = false; // 计算完成后隐藏
      });
    }
  },
};
</script>

<style lang="less" scoped>
.main {
  width: 100%;
  height: 100%;
}

.main_container {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative; // 添加相对定位

  .loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 1); // 白色背景
    z-index: 9999; // 原z-index为9999，增大为10000以确保最顶层显示
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;

    .loading-spinner {
      font-size: 2rem !important;
      // width: 500px;
      // height: 500px;
      color: #0f99ed;
    }
  }

  @keyframes rotating {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  // background: rgb(6, 16, 33);
  background: linear-gradient(180deg,
    #030814 0%,
    rgba(0, 89, 255, 0.26) 100%,
  );

  border-radius: 0px 0px 0px 0px;

  // .main_backboard {
  // }
  .el-header {
    padding: 0;
  }

  .el-main {
    margin-top: -62px;
    padding: 0 16px 16px;
    background: linear-gradient(360deg,

        rgba(1, 25, 66, 0.81) 0%,
        rgba(0, 0, 0, 0.64) 100%);
  }

  .main_main {
    padding: 62px 24px 0px;
    background: url("@/assets/bg_6border.png") no-repeat;
    background-size: 100% 100%;

    .el-footer {
      background: url("@/assets/footer_bottom.png") no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
