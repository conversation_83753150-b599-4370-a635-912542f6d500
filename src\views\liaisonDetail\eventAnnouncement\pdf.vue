<template>
  <div>
      <Loading
      size="30px"
      style="margin-top: 40px;
                 z-index: 9999;
                 position: absolute;
                 left: 41%;
                 top: 4%;"
      v-if="loadings && !fail"
    >加载中...</Loading>
      <div class="pdf">
        <!-- <pdf
          v-for="currentPage in numPages"
          :key="currentPage"
          :src="pdfsrc"
          :page="currentPage"
          @loaded="loadPdfHandler"
        ></pdf> -->
        
        <div class="pdf-box">
    <h2>PDFObject 插件</h2>
    <div ref="pdf"></div>
  </div>  
      </div>

  </div>
</template>
 
<script>
// import { Loading } from "vant";
// import pdf from 'vue-pdf'
import pdf from 'pdfobject';
import request from "@/utils/requestTemp"; // Corrected import path
export default {
  name: 'pdf-perview',
  components: {
    pdf
  },
  data() {
    return {
      loadings: true,
      fail: false,
      viewVisible: true,
      pdfsrc: '',
      numPages: 1,
      currentPage: 1, // pdf文件页码
      pageCount: 1 // pdf文件总页数
    }
  },
  mounted() {
  },
  created() {
    document
      .querySelector('meta[name="viewport"]')
      .setAttribute('content', 'width=device-width,initial-scale=1.0')
    const pdfsrc = this.$route.query.filePath;
    // this.getPdfUrl(pdfsrc)
    this.embedPDF("https://pdfobject.com/pdf/sample-3pp.pdf")
  },
  beforeRouteLeave (to, from, next) {
    // 离开页面取消双指缩放
    document
      .querySelector('meta[name="viewport"]')
      .setAttribute(
        'content',
        'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'
      )
    next()
  },
  methods: {
    embedPDF(pdfsrc) {
      const options = {
    width: "600px",
    height: "500px",
  };
      // replace 'example.pdf' with your actual PDF file path
      pdf.embed(pdfsrc, this.$refs.pdfContainer, options);
    },
    loadPdfHandler() {
      this.pdfsrc.promise.then(pdf => {
      // 获取pdf文件相关信息，页码等
        this.numPages = pdf.numPages
      })
      this.currentPage = 1 // 加载的时候先加载第一页
      this.loadings = false
      // 强制刷新
      this.$forceUpdate()
    },
    getPdfUrl(filePath) {
      let url = "/file/view";
      let that = this;
      request({
        url: url,
        method: "get",
        params: { file: filePath },
        responseType: "blob"
      }).then(res => {
        if (res.data.code != undefined && res.data.code == 500) {
        }
        console.log(res,"*************************************")
        // let blob = new Blob([res.data], { type: "application/pdf" });
        // const fileUrl = URL.createObjectURL(blob);
        // this.pdfsrc = pdf.createLoadingTask({ url: fileUrl })
        // this.loadPdfHandler()
      });
    },
  }
}
</script>
 
<style scoped>
.pdf-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
</style>