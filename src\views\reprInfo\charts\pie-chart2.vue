<template>
  <div ref="chart" class="chart"></div>
</template>

<script>
export default {
  name: "barChart",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    if (this.data) {
      this.getIint();
    }
  },
  watch: {
    data: {
      handler() {
        this.getIint();
      },
      deep: true,
    },
  },
  methods: {
    getIint() {
      let myChart = this.$echarts.init(this.$refs.chart);

      var option = {
        tooltip: {
          trigger: "item",
          textStyle: {
            fontSize: 32,
            fontWeight: "bold",
          },
        },
        legend: {
          orient: "vertical", // 垂直排列
          right: "right", // 距离容器右侧的距离
          top: "middle", // 垂直居中 
          itemGap: 50, // 设置图例项之间的间距
          textStyle: {
            fontSize: 40,
            color: "#fff",
          },
        },
        series: [
          { 
            type: "pie",
            radius: [110, 170],
            center: ["35%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10, 
              borderWidth: 10,
            },
            label: {
              show: false,
              position: "center",
            }, 
            labelLine: {
              show: false,
            },
            // data: [
            //   { value: 777, name: "中共党员" },
            //   { value: 777, name: "民主党派成员" },
            //   { value: 777, name: "群众" },
            //   { value: 777, name: "无党派人士" }, 
            // ],
            data:this.data
          },
        ],
      };
      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.chart {
  width: 100%;
  height: 75%;
}
</style>
