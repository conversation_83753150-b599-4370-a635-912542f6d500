<template>
  <el-dialog :title="title"
             :visible.sync="dialogFormVisible"
             width="1200px"
             @close="close">
    <el-form v-if="flag == 1"
             label-width="80px"
             :label-position="labelPosition"
             ref="forgetpwdform"
             :model="forgetpwdform"
             :rules="rules">
      <el-row>
        <el-col>
          <el-form-item class="wdLabel"
                        prop="userAccount"
                        label="账号：">
            <el-input class="new-btn new-fgbtn"
                      placeholder="请输入账号"
                      v-model="forgetpwdform.userAccount"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item class="wdLabel"
                    prop="userPassword"
                    label="新密码：">
        <el-input type="password"
                  placeholder="请输入新密码"
                  class="new-btn new-fgbtn"
                  v-model="forgetpwdform.userPassword"
                  minlength="6"></el-input>
      </el-form-item>
      <el-form-item class="wdLabel"
                    prop="repeatpassword"
                    label="确认密码：">
        <el-input type="password"
                  placeholder="请输入新密码"
                  class="new-btn new-fgbtn"
                  v-model="forgetpwdform.repeatpassword"
                  minlength="6"></el-input>
      </el-form-item>

      <el-form-item prop="code"
                    class="login-form-admin wdLabel"
                    label="验证码：">
        <el-input v-model.trim="forgetpwdform.code"
                  autocomplete="off"
                  autocapitalize="off"
                  placeholder="请输入验证码"
                  tabindex="3"
                  type="text"
                  style="width: 50%; float: left;"
                  @keyup.enter.native="handleLogin"
                  class="new-btn new-fgbtn" />
        <div class="captcha_code"
             style="width: 80px; margin: 6px 5px; float: left; margin-left: 6%;">
          <img :src="imgData"
               @click="changeCode" />
        </div>
      </el-form-item>

      <el-form-item prop="mobile"
                    class="login-form-admin wdLabel"
                    label="手机号：">
        <el-input v-model.trim="forgetpwdform.mobile"
                  autocomplete="off"
                  autocapitalize="off"
                  placeholder="请输入手机号"
                  tabindex="3"
                  type="text"
                  style="width: 50%; float: left;"
                  class="new-btn new-fgbtn" />

        <div class="captcha_code"
             style="width: 80px; margin: 6px 5px; float: left; margin-left: 6%;">
          <el-button class="smsButton"
                     style="border-radius: 10px;"
                     @click="sendcodeMsg"
                     :disabled="disab">{{ getCode }}</el-button>
        </div>
      </el-form-item>
      <el-form-item class="wdLabel"
                    prop="smscode"
                    label="短信验证码：">
        <el-input type="password"
                  placeholder="请输入短信验证码"
                  class="new-btn new-fgbtn"
                  v-model="forgetpwdform.smscode"
                  number
                  maxlength="6"></el-input>
      </el-form-item>
    </el-form>

    <template slot="footer">
      <el-button type="default"
                 style="margin-right: 15px"
                 @click="close">取消</el-button>
      <el-button type="primary"
                 @click="handleSubmit">提交</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import { getCaptcha } from "@/api/captcha";
import { doUpdata, validateAccount, sendMsg } from "@/api/forget";
export default {
  name: "TableEdit",
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  data() {
    return {
      imgData: "",
      getCode: "发送验证码",
      sendcode: "发送验证码",
      disab: false,
      forgetpwdform: {
        userAccount: "",
        mobile: "",
        userPassword: "",
        repeatpassword: "",
        code: "",
        key: "",
        smscode: "",
      },

      isGeting: false,
      count: 120,

      rules: {
        userAccount: [
          {
            required: true,
            message: "账号不能为空",
            trigger: "blur",
          },
        ],
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
        ],
        userPassword: [
          {
            required: true,
            message: "新密码不能为空",
            trigger: "blur",
          },
        ],
        repeatpassword: [
          {
            required: true,
            message: "重置密码不能为空",
            trigger: "blur",
          },
        ],
        code: [
          {
            required: true,
            message: "图形验证码不能为空",
            trigger: "blur",
          },
        ],
        smscode: [
          {
            required: true,
            message: "短信验证码不能为空",
            trigger: "blur",
          },
        ],
      },

      calendId: "",
      tempImg: "",
      name: "imgUpload",
      upLoadUrl:
        process.env.VUE_APP_BASE_API + "/api/v1/meetingSeating/newimpseatexcle",
      action: process.env.VUE_APP_BASE_API + "/api/v1/meetingFile/fileUpload",
      photoContent: {
        meetingId: "",
        relId: "",
        type: "0",
        encrypted: "false",
      },
      title: "忘记密码",
      dialogFormVisible: false,
      imgLimit: 10,
      productImgs: [],
      isMultiple: false,
      dialogImageUrl: "",
      dialogVisible: false,
      seatingListShow: false,
      scrollHeight: "0px",
      elementLoadingText: "正在加载...",
      flag: 1,
      labelPosition: "right", // 表单标签对齐方式
    };
  },
  created() {
    this.isUpdate = 1;
    this.scrollHeight = window.innerHeight + "px";
  },
  mounted() {
    if ("production" !== process.env.NODE_ENV) {
      // this.loginForm.userName = "yuanzhuoyi";
      // this.loginForm.password = "666666";
    }
    setTimeout(() => {
      this.animateShow = true;
    });
    // 得到验证码图片
    // this.changeCode();
  },
  methods: {
    handleShow(row) {
      this.forgetpwdform.userAccount = "";
      this.forgetpwdform.code = "";
      this.forgetpwdform.key = "";
      this.forgetpwdform.mobile = "";
      this.forgetpwdform.smscode = "";
      this.forgetpwdform.repeatpassword = "";
      this.changeCode();
      this.dialogFormVisible = true;
    },
    close() {
      this.dialogFormVisible = false;
    },
    validateAcc() {
      var that = this;
      var account = that.forgetpwdform.userAccount;
      var code = that.forgetpwdform.code;
      var key = that.forgetpwdform.key;
      const data2 = {
        account: that.forgetpwdform.userAccount,
        code: that.forgetpwdform.code,
        key: that.forgetpwdform.key,
      };
      if (account == "") {
        this.$message.error("请输入账号");
        return;
      } else if (code.length != 4) {
        this.$message.error("请输入正确的图形验证码");
        return;
      }
      validateAccount(data2).then(() => {
        console.log("验证成功");
        this.flag = 2;
      });
    },
    handleSubmit() {
      // 密码正则
      var reg =
        /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\d$@$!%*?&.#$%^&*()<>?_+{}]{8,20}$/;

      var pass1 = this.forgetpwdform.userPassword;
      var pass2 = this.forgetpwdform.repeatpassword;
      const data2 = {
        account: this.forgetpwdform.userAccount,
        mobile: this.forgetpwdform.mobile,
        password: this.forgetpwdform.userPassword,
        smscode: this.forgetpwdform.smscode,
      };
      this.$refs["forgetpwdform"].validate((valid) => {
        if (valid) {
          if (pass1 != pass2) {
            this.$message.error("两次密码不一致");
            return;
          }

          if (!reg.test(pass1)) {
            this.$message.error("密码必须长度8至20位，包含数字和字母");
            return;
          }

          doUpdata(data2).then(() => {
            this.dialogFormVisible = false;
            this.$message.success("保存成功");

            //this.goBackFront();
          });
        }
      });
    },

    changeCode() {
      let that = this;
      getCaptcha().then((res) => {
        if (res.code == 200) {
          that.forgetpwdform.key = res.data.key;

          let file = res.data.image; // 把整个base64给file
          var type = "image/png"; // 定义图片类型（canvas转的图片一般都是png，也可以指定其他类型）
          let conversions = that.base64ToBlob(file, type); // 调用base64转图片方法----vue写法
          // window.URL = window.URL || window.webkitURL; //blob对象转换为blob-url
          var url = window.URL.createObjectURL(conversions);
          that.imgData = url;
        }
      });
    },
    /**
     * base64转blob
     * 原理：利用URL.createObjectURL为blob对象创建临时的URL
     */
    base64ToBlob(urlData, type) {
      let arr = urlData.split(",");
      let mime = arr[0].match(/:(.*?);/)[1] || type;
      // 去掉url的头，并转化为byte
      let bytes = window.atob(arr[1]);
      // 处理异常,将ascii码小于0的转换为大于0
      let ab = new ArrayBuffer(bytes.length);
      // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
      let ia = new Uint8Array(ab);
      for (let i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ab], {
        type: mime,
      });
    },
    sendcodeMsg() {
      var that = this;
      var account = that.forgetpwdform.userAccount;
      var code = that.forgetpwdform.code;
      var key = that.forgetpwdform.key;
      var mobile = that.forgetpwdform.mobile;

      const data2 = {
        account: that.forgetpwdform.userAccount,
        mobile: that.forgetpwdform.mobile,
        code: that.forgetpwdform.code,
        key: that.forgetpwdform.key,
      };
      if (account == "") {
        this.$message.error("请输入账号");
        return;
      } else if (code == "") {
        this.$message.error("请输入图形验证码");
        return;
      } else if (mobile.length != 11) {
        this.$message.error("请输入正确的手机号");
        return;
      }

      var countDown = setInterval(() => {
        if (this.count < 1) {
          this.isGeting = false;
          this.disab = false;
          this.getCode = "获取验证码";
          this.count = 120;
          clearInterval(countDown);
        } else {
          this.isGeting = true;
          this.disab = true;
          this.getCode = this.count-- + "s后重发";
        }
      }, 1000);

      sendMsg(data2).then(() => {});
    },
  },
};
</script>
<style scoped>
.el-dialog__header {
  padding: 38px;
  padding-bottom: 10px;
}

.wdLabel {
  width: 80%;
  margin-right: 20px;
}

.smsButton {
  height: 43px;
  border-radius: 5px;
  background-color: #1890ff;
  color: white;
  width: 120px;
}

.el-form-item__label {
  width: 200px !important;
}

.new-fgbtn {
  width: 50% !important;
}

.wdLabel .el-form-item__error {
  left: 129px;
}

.codeGeting {
  background: #cdcdcd;
  border-color: #cdcdcd;
}
</style>