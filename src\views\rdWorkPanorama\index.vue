<template>
  <div class="region-page">
    <div class="region-left">
      <ScmpCard :rightPicture="false" backgroundImage="card_bg1" cardName="代表分布">
        <div slot="titleRight">
          <TimeNext :value="yearValue1" :clicktype="clicktype = '1'" @change-Year="changeYear"
            @henld-next="handleNext('reprInfo')" />
        </div>
        <div slot="main" class="region-left-top">
          <div class="left_main1">
            <img class="title-img" :src="require('@/assets/image/quanshizonglan.png')" alt="" />
            <div class="left_main1_con">
              <div class="zongshu">
                <div class="zongshu-name">代表总数</div>
                <el-statistic group-separator="," :value="cityOverview.allNum" class="zongshu-num"></el-statistic>
                <img class="zongshu-img" :src="require('@/assets/image/region-left-1.png')" alt="" />
              </div>
              <img class="zongshu-divider" :src="require('@/assets/image/region-left-divider.png')" alt="" />
              <div class="fenxiang">
                <img class="fenxiang-img" :src="require('@/assets/image/region-left-2.png')" alt="" />
                <div class="fenxiang-con">
                  <div class="fenxiang-item item1">
                    <div class="item-name">全国</div>
                    <div class="item-num">
                      {{ cityOverview.country }}<span class="unit">人</span>
                    </div>
                  </div>
                  <div class="fenxiang-item item2">
                    <div class="item-name">省</div>
                    <div class="item-num">
                      {{ cityOverview.province }}<span class="unit">人</span>
                    </div>
                  </div>
                  <div class="fenxiang-item item3">
                    <div class="item-name">市</div>
                    <div class="item-num">
                      {{ cityOverview.city }}<span class="unit">人</span>
                    </div>
                  </div>
                  <div class="fenxiang-item item4">
                    <div class="item-name">区</div>
                    <div class="item-num">
                      {{ cityOverview.administrativeArea }}<span class="unit">人</span>
                    </div>
                  </div>
                  <div class="fenxiang-item item5">
                    <div class="item-name">镇</div>
                    <div class="item-num">
                      {{ cityOverview.streetTown }}<span class="unit">人</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
      <ScmpCard :rightPicture="false" backgroundImage="card_bg1" cardName="本年度市本级议案建议总览" class="region-left-con-wrap">
        <div slot="titleRight">
          <TimeNext :value="yearValue1" @henld-next="handleNext('motion')" :clicktype="clicktype = '2'"
            @change-Year="changeYear" />
        </div>
        <div slot="main" class="region-left-con">
          <div class="main_left_main">
            <div class="left-con-t">
              <div class="left-con-t-item" v-for="(item, index) in threeCardList" :key="index">
                <div class="item-num">{{ item.number }}</div>
                <div class="item-name">{{ item.name }}(件)</div>
              </div>
            </div>
            <div class="left-con-c">
              <div class="left-con-c-left">
                <img src="@/assets/image/region-left-con2.png" class="text-img" alt="" />
                <div class="text-box">
                  <img src="@/assets/image/dahuiqijian.png" class="text-name" alt="" />
                  <div class="name-num">
                    <span class="text-num">{{ proposal1.total }}</span>
                    <span class="unit">件</span>
                  </div>
                </div>
              </div>
              <img class="divider" :src="require('@/assets/image/region-left-divider.png')" alt="" />
              <div class="left-con-c-item">
                <div class="c-item-test-box">
                  <div class="c-item-test-name">满意率</div>
                  <div class="c-item-test-num">
                    {{ proposal1.satisfy }}<span class="unit">%</span>
                  </div>
                </div>
                <div class="progress-bar">
                  <div class="bar" :style="`width:${proposal1.satisfy}%`"></div>
                  <div class="cursor" :style="`left:calc(${proposal1.satisfy}%)`">
                    <div class="sub"></div>
                  </div>
                </div>
              </div>
              <img class="divider" :src="require('@/assets/image/region-left-divider.png')" alt="" />
              <div class="left-con-c-item">
                <div class="c-item-test-box">
                  <div class="c-item-test-name">答复率</div>
                  <div class="c-item-test-num">
                    {{ proposal1.reply }}<span class="unit">%</span>
                  </div>
                </div>
                <div class="progress-bar green-bar">
                  <div class="bar" :style="`width:${proposal1.reply}%`"></div>
                  <div class="cursor" :style="`left:calc(${proposal1.reply}%)`">
                    <div class="sub"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="left-con-c">
              <div class="left-con-c-left">
                <img src="@/assets/image/region-left-con2.png" class="text-img" alt="" />
                <div class="text-box">
                  <img src="@/assets/image/bihuiqijian.png" class="text-name" alt="" />
                  <div class="name-num">
                    <span class="text-num text-num-bihui">{{
                      proposal2.total
                    }}</span>
                    <span class="unit">件</span>
                  </div>
                </div>
              </div>
              <img class="divider" :src="require('@/assets/image/region-left-divider.png')" alt="" />
              <div class="left-con-c-item">
                <div class="c-item-test-box">
                  <div class="c-item-test-name">满意率</div>
                  <div class="c-item-test-num">
                    {{ proposal2.satisfy }}<span class="unit">%</span>
                  </div>
                </div>
                <div class="progress-bar">
                  <div class="bar" :style="`width:${proposal2.satisfy}%`"></div>
                  <div class="cursor" :style="`left:calc(${proposal2.satisfy}%)`">
                    <div class="sub"></div>
                  </div>
                </div>
              </div>
              <img class="divider" :src="require('@/assets/image/region-left-divider.png')" alt="" />
              <div class="left-con-c-item">
                <div class="c-item-test-box">
                  <div class="c-item-test-name">答复率</div>
                  <div class="c-item-test-num">
                    {{ proposal2.reply }}<span class="unit">%</span>
                  </div>
                </div>
                <div class="progress-bar green-bar">
                  <div class="bar" :style="`width:${proposal2.reply}%`"></div>
                  <div class="cursor" :style="`left:calc(${proposal2.reply}%)`">
                    <div class="sub"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
      <ScmpCard :rightPicture="false" backgroundImage="card_bg1" cardName="本年度市本级人事任免情况总览">
        <div slot="titleRight">
          <TimeNext :value="yearValue1" @henld-next="handleNext('personnel')" :clicktype="clicktype = '3'"
            @change-Year="changeYear" />
        </div>
        <div slot="main" class="region-left-bottom">
          <div class="main_left_main">
            <div class="left-bottom-top">
              <div class="left-bottom-top-item">
                <div class="left-bottom-top-item-name">任职人数</div>
                <div class="left-bottom-top-item-num">
                  {{ appointAndDismiss.appointCount }}
                </div>
              </div>
              <div class="left-bottom-top-item">
                <div class="left-bottom-top-item-name">免职人数</div>
                <div class="left-bottom-top-item-num left-bottom-top-item-num1">
                  {{ appointAndDismiss.removalCount }}
                </div>
              </div>
            </div>
            <div class="left-bottom-con">
              <div class="left-bottom-con-item" v-for="(item, index) in appointAndDismissList" :key="index">
                <div class="item-title">
                  {{ item.categoryName }}
                </div>
                <div class="item-con">
                  <img :src="getImageSrc('region-left-botton-3.png')" class="item-img" />
                  <div class="item-box">
                    <div class="item-text">
                      <div class="item-text-name ren">任</div>
                      <div class="item-text-num ren-num">
                        {{ item.appointCount }}
                      </div>
                      <span class="unit">人</span>
                    </div>
                    <div class="item-text">
                      <div class="item-text-name mian">免</div>
                      <div class="item-text-num mian-num">
                        {{ item.removalCount }}
                      </div>
                      <span class="unit">人</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
    </div>
    <div class="region-content">
      <div class="con">
        <div class="con-top">
          <ScmpSpot spotName="人大代表工作视窗" dropDownTitle="切换专题" :column="spotDownColumn" :dropDownList="dropDownList"
            :path="path" @checkedItem="checkedItem">
          </ScmpSpot>
        </div>
        <!-- <Map2d class="map2d" height="100%" @getMapInfo="getMapInfo" /> -->
      </div>
      <div class="bottom">
        <ScmpCard :rightPicture="false" backgroundImage="card_bg1" cardName="联络站统计" class="region-content-left-wrap">
          <div slot="titleRight" class="region-content-left-slot">
            <img :src="require('@/assets/image/region-content-img1.png')" class="slot-img" alt="" />
            <span class="slot-name">联络站总数</span>
            <span class="slot-num">{{ llzTotal }}</span>
          </div>
          <div slot="main" class="region-content-left">
            <div class="main_content_main">
              <div class="chart-box">
                <div class="chart-num">
                  <span v-for="(item, index) in barChartData" :key="index">
                    {{ item }}
                  </span>
                </div>
                <BarChart :data="barChartData" :xAxis="xAxisList" />
              </div>
            </div>
          </div>
        </ScmpCard>
        <ScmpCard :rightPicture="false" backgroundImage="card_bg1" cardName="民意收集情况统计 " class="region-content-right-wrap">
          <div slot="main" class="region-content-right">
            <div class="main_content_main">
              <div class="content-right-top">
                <div class="content-right-item">
                  <img src="@/assets/image/content-right-img1.png" class="con-item-img" alt="" />
                  <span class="con-right-item-name">意见数</span>
                  <el-statistic group-separator="," :value="contentRight.suggestion"
                    class="con-right-item-num"></el-statistic>
                </div>
                <div class="content-right-item">
                  <img src="@/assets/image/content-right-img2.png" class="con-item-img" alt="" />
                  <span class="con-right-item-name">办结率</span>
                  <el-statistic group-separator="," :value="contentRight.completionRate" suffix="%"
                    class="con-right-item-num"></el-statistic>
                </div>
                <div class="content-right-item">
                  <img src="@/assets/image/content-right-img2.png" alt="" class="con-item-img" />
                  <span class="con-right-item-name">满意率</span>
                  <el-statistic group-separator="," :value="contentRight.satisfactionRate" suffix="%"
                    class="con-right-item-num"></el-statistic>
                </div>
              </div>
              <div class="content-right-con">
                <AreaChart :data="areaChartData" :xAxis="areaxAxisList" />
              </div>
            </div>
          </div>
        </ScmpCard>
      </div>
    </div>
    <div class="region-right">
      <ScmpCard :rightPicture="false" cardName="本年度代表履职情况总览" class="region-right-top-wrap">
        <div slot="titleRight">
          <TimeNext :value="yearValue1" @henld-next="handleNext('perform')" :clicktype="clicktype = '4'"
            @change-Year="changeYear" />
        </div>
        <div slot="main" class="region-right-top">
          <div class="main_right_main">
            <div class="region-right-top-title">
              <div class="circle">
                <div class="dot"></div>
              </div>
              <span class="title">主要履职活动类型及分布情况</span>
              <img src="@/assets/image/region-right-img1.png" class="title-img" alt="" />
            </div>
            <div class="region-right-top-tj">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="top-tj-item">
                    <div class="top-tj-item-name">履职活动总数</div>
                    <div class="num-box">
                      <el-statistic group-separator="," :value="allDutyActivityNum"
                        class="top-tj-item-num"></el-statistic>
                      <span class="unit">次</span>
                    </div>
                    <div></div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="top-tj-item top-tj-item1">
                    <div class="top-tj-item-name">履职活动人数</div>
                    <div class="num-box">
                      <el-statistic group-separator="," :value="allPeopleNum" class="top-tj-item-num"></el-statistic>
                      <span class="unit">人</span>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="region-right-top-con">
              <div class="region-right-top-con-item" v-for="(item, index) in progressBarList" :key="index">
                <div class="item-title">
                  <span class="title">{{ item.name }}
                    <span v-if="index + 1 === progressBarList.length" class="action" @click="toFeature">进入特色专题 <i
                        class="el-icon-caret-right"></i></span></span>
                  <span class="value">{{ item.value }}人/次</span>
                </div>
                <div class="right-progress-bar">
                  <div class="bar" :style="`width:${(Math.min(item.value, 1000) / 1000) * 100}%`"></div>
                  <div class="cursor" :style="`left:calc(${(Math.min(item.value, 1000) / 1000) * 100}% - 21px)`">
                    <div class="sub"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
      <ScmpCard :rightPicture="false" backgroundImage="card_bg1" cardName="本年度市本级代表培训情况总览">
        <div slot="main" class="region-right-bottom">
          <div class="main_right_main">
            <div class="right-bottom-top">
              <div class="name">组织培训次数</div>
              <div class="num">
                <el-statistic group-separator="," :value="studyNum"></el-statistic>
                <span class="unit">次</span>
              </div>
            </div>
            <div class="right-bottom-bottom">
              <div class="name">参与培训人数</div>
              <div class="num">
                <el-statistic group-separator="," :value="studyPeopleNum"></el-statistic>
                <span class="unit">人</span>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
    </div>
    <!-- 地图弹窗 -->
    <map-modal v-if="modalForm.dbNumCount" :visible.sync="dialogVisible" :modalForm="modalForm" :districtName="districtName" />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getDbNumCount,
  getDutyCountAndTypeForYear,
  getDutyStudyForYear,
  getOnlineStationCount,
  getLiaisonStationNumCount,
  getLiaisonInfoForDistrict,
  personnelAppoint
} from "@/api/dataPageApi/region";
import {
  getProposalCount,
} from "@/api/dataPageApi/motion";
import store from "../../stores";
export default {
  name: "region-index",
  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    TimeNext: () => import("@/comps/time-next"),
    BarChart: () => import("@/views/region/charts/bar-chart.vue"),
    AreaChart: () => import("@/views/region/charts/area-chart.vue"),
    Map2d: () => import("@/components/snapshot/map2DLink.vue"),
    MapModal: () => import("@/components/dome/map-modal.vue"),
    ScmpSpot: () => import("@/comps/scmp-spot"),
  },
  data() {
    return {
      yearValue1: String(new Date().getFullYear()),
      cityOverview: {
        total: 0,
        country: 0,
        province: 0,
        city: 0,
        district: 0,
        town: 0,
      },
      // 左中间
      leftMiddleList: [
        {
          name: "代表议案数（件）",
          number: 0,
        },
        {
          name: "代表建议数（件）",
          number: 0,
        },
        {
          name: "供参考数（件）",
          number: 0,
        },
      ],
      // 议案建议
      proposal1: {
        total: 501,
        satisfy: 60,
        reply: 60,
      },
      proposal2: {
        total: 221,
        satisfy: 60,
        reply: 60,
      },
      appointAndDismiss: {
        appointCount: 0,
        removalCount: 0,
      },
      appointAndDismissList: [],
      // 本年度代表履职情况总览
      rightTop: {
        value1: 30205,
        value2: 9205,
      },
      progressBarList: [],
      // 本年度市本级代表培训情况总览
      studyNum: '',
      studyPeopleNum: '',
      // 民意收集情况统计
      contentRight: {
        completionRate: '',
        satisfactionRate: '',
        satisfactionRate: '',
      },
      // 联络站统计
      barChartData: [],
      xAxisList: [],
      llzTotal: 0,
      // 民意收集情况统计
      areaChartData: [],
      areaxAxisList: [
        "01",
        "02",
        "03",
        "04",
        "05",
        "06",
        "07",
        "08",
        "09",
        "10",
        "11",
        "12",
      ],
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],
      // 地图弹窗
      dialogVisible: false,
      modalForm: {},
      token: '',
      path: '',
      dbnameMap: {
          communityScheduleNum: "进社区",
        investigationNum: "集中视察",
        meetingNum: "出席代表大会",
        // memberCommentNum: "“随手拍”提交情况人次",
        memberCommentNum: "随手拍提交情况",
        proposalSuggestionNum: "领衔议案建议",
        studyNum: "学习培训",
        themeMonthNum: "主题活动月",
        otherNum: "其他活动",
      },
      allDutyActivityNum: 0,
      allPeopleNum: 0,
      // 区名
      districtName: '',
      clicktype: '',
      threeCardList: [
        {
          name: "议案数",
          number: 0,
        },
        {
          name: "建议数",
          number: 0,
        },
        {
          name: "供参考建议",
          number: 0,
        },
      ],
      llzTotal: 0,
      studyPeopleNum: 0,
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    this.token = this.$checkToken();
    console.log(this.token)
    console.log(localStorage.getItem('pro__token'))
    // console.log(localStorage.getItem('userId'))
    // console.log(localStorage.getItem('BYUI-VUE-THEME'))
    // const test = localStorage.getItem('BYUI-VUE-THEME')
    // const newtest = JSON.parse(test)
    // console.log(newtest.buttonBackground)
    this.path = this.$route.path
    // if(this.token != '') {
    this.getDbNumCount(this.yearValue1, this.token)
    this.getDutyStudyForYear(this.yearValue1, this.token)
    this.getDutyCountAndTypeForYear(this.yearValue1, this.token)
    this.getOnlineStationCount(this.yearValue1, this.token)
    this.getLiaisonStationNumCount(this.yearValue1, this.token)
    this.personnelAppoint(this.yearValue1, this.token)
    this.getProposalCount(this.yearValue1, this.token)
    // }

  },
  mounted() { },
  watch: {},
  methods: {
    getProposalCount(year) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        isMeeting: 1,
        meeting: '144',
        year: year
      }
      getProposalCount(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 类型映射关系
          const typeMapping = {
            "1": "议案数",
            "2": "建议数",
            "3": "建议数", // 闭会也计入建议数
            "4": "供参考建议"
          };
          // 初始化统计对象
          const result = {
            "议案数": 0,    // 默认议案数为1（如无数据）
            "建议数": 0,
            "供参考建议": 0
          };

          // 遍历原始数据并统计
          Object.entries(res.data).forEach(([type, count]) => {
            const name = typeMapping[type];
            if (name) {
              result[name] += count;
            }
          });

          // 转换为目标格式
          this.threeCardList = Object.entries(result).map(([name, number]) => ({
            name,
            number
          }));
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getDbNumCount(data, token) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      // const params = {
      //   year: data
      // }
      getDbNumCount(token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.cityOverview = res.data

        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    getDutyStudyForYear(data, token) {
      const params = {
        cockpitQuery: '',
        year: data
      }
      console.log(localStorage.getItem('pro__token'))
      this.token = this.$checkToken();
      console.log(this.token)
      console.log(params)
      getDutyStudyForYear(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.studyNum = res.data.studyNum
          this.studyPeopleNum = res.data.studyPeopleNum
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    getDutyCountAndTypeForYear(data, token) {
      const params = {
        cockpitQuery: '',
        year: data
      }
      getDutyCountAndTypeForYear(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.allDutyActivityNum = res.data.allDutyActivityNum
          this.allPeopleNum = res.data.allPeopleNum
          const item = res.data.item
          this.progressBarList = Object.keys(item).map(key => ({
            name: this.dbnameMap[key],   // 获取对应的名称
            value: item[key],     // 获取对应的值
          }));
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getOnlineStationCount(data, token) {
      const params = {
        cockpitQuery: '',
        year: data
      }
      getOnlineStationCount(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.contentRight.completionRate = res.data.completionRate ? parseFloat(res.data.completionRate.replace('%', '')) : 0;
          this.contentRight.satisfactionRate = res.data.satisfactionRate ? parseFloat(res.data.satisfactionRate.replace('%', '')) : 0;
          this.contentRight.suggestion = res.data.suggestion
          const rawData = res.data.items
          console.log(rawData)
          this.areaChartData = new Array(12).fill(0)
          for (const key in rawData) {
            if (rawData.hasOwnProperty(key)) {
              const item = rawData[key];
              const monthIndex = parseInt(item.month, 10) - 1; // 将月份转换为数组索引（0-11）
              this.areaChartData[monthIndex] = parseInt(item.numCount, 10); // 将 numCount 转换为数字
              console.log(this.areaChartData)
            }
          }
          console.log(this.contentRight)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    getLiaisonStationNumCount(data, token) {
      const params = {
        cockpitQuery: '',
        year: data
      }
      getLiaisonStationNumCount(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.llzTotal = res.data.allNum
          // 定义目标顺序
          const targetOrder = [
            "越秀区",
            "海珠区",
            "荔湾区",
            "天河区",
            "白云区",
            "黄埔区",
            "花都区",
            "番禺区",
            "南沙区",
            "从化区",
            "增城区",
          ];
          // 初始化 xAxisList 和 barChartData
          this.xAxisList = [...targetOrder]; // 直接使用目标顺序
          this.barChartData = new Array(targetOrder.length).fill(0); // 初始化 barChartData，默认值为 0
          const rawData = res.data.items
          // 将 rawData 转换为一个以 name 为键的对象，方便快速查找
          const dataMap = rawData.reduce((map, item) => {
            map[item.name] = item.countNum;
            return map;
          }, {});
          console.log('dataMap')
          console.log(dataMap)
          // 根据 targetOrder 填充 barChartData
          this.xAxisList.forEach((name, index) => {
            if (dataMap[name] !== undefined) {
              this.barChartData[index] = dataMap[name];
              console.log(this.barChartData)
            }
          });
          console.log('xAxisList')
          console.log(this.xAxisList)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    personnelAppoint(data, token) {
      const params = {
        cockpitQuery: '',
        year: data
      }
      personnelAppoint(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.appointAndDismissList = res.data.items
          this.appointAndDismiss.appointCount = res.data.appointCount
          this.appointAndDismiss.removalCount = res.data.removalCount
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    getLiaisonInfoForDistrict(data, token) {
      const params = {
        cockpitQuery: '',
        administrativeAreaId: data
      }
      getLiaisonInfoForDistrict(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.modalForm = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    // 修改年份
    changeYear(year, type) {
      console.log(year)
      console.log(type)
      if (type == '1') {
        this.getDbNumCount(year)
      } else if (type == '2') {
        // 暂无接口
        this.getProposalCount(year)
      } else if (type == '3') {
        this.personnelAppoint(year)
      } else {
        this.getDutyCountAndTypeForYear(year)
      }
    },
    checkedItem(path) {
      console.log("选中", path);
      console.log(this.$route.path);
      // this.path = this.$route.path
    },
    getList(value) {
      console.log("查询数据", value);
    },
    handleNext(link) {
      const routeMap = {
        popularWill: '/popularWill',
        region: '/region',
        motion: '/motion',
        snapshot: '/snapshot',
        junior: '/junior',
        liaisonStation: '/liaisonStation',
        reprInfo: '/reprInfo',
        perform: '/perform',
        personnel: '/personnel',
        dome: '/dome'
      };

      if (routeMap[link]) {
        this.$router.push(routeMap[link]);
        console.log("进入专题");
      } else if (link === '') {
        // 处理空链接的情况
      } else {
        // 处理未知链接的情况
        console.warn(`未知的路由链接: ${link}`);
      }
    },
    getImageSrc(imgPath) {
      try {
        return require(`@/assets/image/${imgPath}`);
      } catch (e) {
        console.error("图片路径错误:", imgPath, e);
        return ""; // 返回占位图片路径或空字符串
      }
    },

    toFeature() {
      console.log("进入特色专题");
      this.$router.push('/snapshot');
    },

    getMapInfo(val, administrativeAreaId) {
      console.log('val >>>', val);
      console.log('administrativeAreaId >>>', administrativeAreaId.features[val.dataIndex].properties.adcode);
      // this.modalForm = val
      this.districtName = val.data.name || administrativeAreaId.features[val.dataIndex].properties.name
      console.log(this.districtName)
      this.dialogVisible = true
      this.getLiaisonInfoForDistrict(administrativeAreaId.features[val.dataIndex].properties.adcode, this.token)
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
.region-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 16px;
  position: relative;

  .region-left {
    width: 853px;
    margin-top: 54px;
    margin-bottom: 25px;

    .region-left-top {
      padding: 21px 35px 0px 48px;

      .left_main1 {
        height: 420px;

        .title-img {
          width: 270px;
          height: 70px;
          margin-bottom: 20px;
        }

        .left_main1_con {
          display: flex;
          align-items: center;
          height: calc(100% - 72px);

          .zongshu {
            width: 167px;
            height: 100%;

            .zongshu-name {
              font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
              font-weight: 400;
              font-size: 30px;
              color: #ffffff;
              line-height: 35px;
              text-shadow: 0px 0px 24px rgba(68, 142, 254, 0.71);
              font-style: normal;
              text-transform: none;
              text-align: center;
              margin-bottom: 20px;
            }

            .zongshu-num {
              ::v-deep .number {
                font-family: DIN-BoldItalicAlt, DIN-BoldItalicAlt;
                font-weight: 400;
                font-size: 54px;
                color: #28f1fa;
              }
            }

            .zongshu-img {
              width: 167.84px;
              height: 180.48px;
            }
          }

          .zongshu-divider {
            height: 229px;
            margin: 0 20px 0 40px;
          }

          .fenxiang {
            display: flex;
            position: relative;

            .fenxiang-img {
              width: 240px;
              height: 206.95px;
            }

            .fenxiang-con {
              position: absolute;
              top: 0;
              left: 150px;

              .fenxiang-item {
                display: flex;

                .item-name {
                  width: 62px;
                  height: 32px;
                  margin-right: 15px;
                  border-radius: 4px;
                  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
                  font-weight: 400;
                  font-size: 24px;
                  color: #ffffff;
                  text-align: center;
                }

                .item-num {
                  height: 34px;
                  line-height: 34px;
                  padding-left: 50px;
                  font-family: DIN, DIN;
                  font-weight: bold;
                  font-size: 28px;
                  color: #29f1fa;
                  text-shadow: 0px 4px 4px rgba(41, 241, 250, 0.5);
                  margin-bottom: 9px;
                  box-sizing: border-box;
                  display: flex;
                  align-items: center;

                  .unit {
                    font-weight: bold;
                    font-size: 24px;
                    color: #ffffff;
                    line-height: 28px;
                    text-shadow: 0px 4px 4px #1c527e;
                    margin-left: 16px;
                  }
                }
              }

              .item1 {
                .item-name {
                  background: #023e8a;
                }

                .item-num {
                  background: url("@/assets/image/region-left-bg1.png") no-repeat;

                  background-size: 100% 100%;
                  width: 229px;
                }
              }

              .item2 {
                margin-left: 30px;

                .item-name {
                  background: #0077b6;
                }

                .item-num {
                  background: url("@/assets/image/region-left-bg2.png") no-repeat;

                  background-size: 100% 100%;
                  width: 212px;
                }
              }

              .item3 {
                margin-left: 55px;

                .item-name {
                  background: #0096c7;
                }

                .item-num {
                  background: url("@/assets/image/region-left-bg3.png") no-repeat;

                  background-size: 100% 100%;
                  width: 186px;
                }
              }

              .item4 {
                margin-left: 80px;

                .item-name {
                  background: #00b4d8;
                }

                .item-num {
                  background: url("@/assets/image/region-left-bg4.png") no-repeat;

                  background-size: 100% 100%;
                  width: 166px;
                }
              }

              .item5 {
                margin-left: 105px;

                .item-name {
                  background: #00b4d8;
                }

                .item-num {
                  background: url("@/assets/image/region-left-bg5.png") no-repeat;
                  background-size: 100% 100%;
                  width: 143px;
                }
              }
            }
          }
        }
      }
    }

    .region-left-con-wrap {
      margin: 30px 0;

      .region-left-con {
        .main_left_main {
          height: 500px;
          padding: 20px;
          box-sizing: border-box;

          .left-con-t {
            height: 40%;
            background: url("@/assets/image/region-left-con-bg.png") no-repeat;
            background-size: 100% 58px;
            background-position: center bottom;
            display: flex;
            justify-content: space-between;

            .left-con-t-item {
              width: 30%;
              height: 85%;
              background: url("@/assets/image/region-left-con1.png") no-repeat;
              background-position: center bottom;
              background-size: 175.95px 126.73px;

              .item-num {
                font-family: DIN, DIN;
                font-weight: bold;
                font-size: 40px;
                color: #28f1fa;
                line-height: 47px;
                text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71),
                  0px 0px 5px rgba(66, 158, 219, 0.93);
                text-align: center;
                font-style: normal;
                text-transform: none;
              }

              .item-name {
                margin-top: 10px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 20px;
                color: #ffffff;
                line-height: 23px;
                text-shadow: 0px 0px 23px #010507;
                text-align: center;
                font-style: normal;
                text-transform: none;
              }
            }
          }

          .left-con-c {
            height: 30%;
            background: url("@/assets/image/region-left-con-bg.png") no-repeat;
            background-size: 100% 58px;
            background-position: center bottom;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            padding-bottom: 30px;

            .divider {
              height: 100%;
            }

            .left-con-c-left {
              width: 30%;
              display: flex;
              align-items: center;

              .text-img {
                width: 91px;
                height: 91px;
              }

              .text-box {
                .text-name {
                  width: 111px;
                  height: 30px;
                }

                .name-num {
                  .text-num {
                    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
                    font-weight: 400;
                    font-size: 40px;
                    color: #ffffff;
                    line-height: 47px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    font-weight: bold;
                    /* 设置文字加粗 */
                    background: linear-gradient(180deg,
                        #ffffff 0%,
                        #00d46a 100%);
                    /* 定义渐变颜色 */
                    -webkit-background-clip: text;
                    /* 仅对文字部分应用背景 */
                    -webkit-text-fill-color: transparent;
                    /* 设置文字颜色为透明 */
                  }

                  .text-num-bihui {
                    background: linear-gradient(180deg,
                        #ffffff 0%,
                        #cc765b 100%);
                    -webkit-background-clip: text;
                    /* 仅对文字部分应用背景 */
                    -webkit-text-fill-color: transparent;
                    /* 设置文字颜色为透明 */
                  }

                  .unit {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    font-size: 16px;
                    color: #fff;
                  }
                }
              }
            }

            .left-con-c-item {
              width: 30%;
              padding: 0 20px;

              .c-item-test-box {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20px;

                .c-item-test-name {
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 400;
                  font-size: 24px;
                  color: #ffffff;
                  line-height: 28px;
                }

                .c-item-test-num {
                  font-family: DIN-BlackItalic, DIN-BlackItalic;
                  font-weight: 400;
                  font-size: 30px;
                  color: rgba(255, 255, 255, 0.87);
                  line-height: 35px;

                  .unit {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.64);
                  }
                }
              }

              .progress-bar {
                position: relative;
                width: 100%;
                height: 8px;
                background: rgba(112, 112, 112, 0.23);

                .bar {
                  height: 8px;
                  background: linear-gradient(259deg,
                      #1b7ef2 0%,
                      rgba(27, 126, 242, 0) 100%);
                }

                .cursor {
                  position: absolute;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 24px;
                  height: 24px;
                  background: #161616;
                  border-radius: 50%;
                  border: 1px solid #1b7ef2;
                  display: flex;
                  justify-content: center;
                  align-items: center;

                  .sub {
                    width: 6px;
                    height: 6px;
                    background: #ffffff;
                    box-shadow: 0px 0px 5px 5px rgba(255, 255, 255, 0.44);
                    border-radius: 50%;
                  }
                }
              }

              .green-bar {
                .bar {
                  height: 8px;
                  background: linear-gradient(259deg,
                      #29f1fa 0%,
                      rgba(41, 241, 250, 0) 100%);
                }

                .cursor {
                  border: 1px solid #29f1fa;
                }
              }
            }
          }

          .left-con-b {
            height: 30%;
            background: url("@/assets/image/region-left-con-bg.png") no-repeat;
            background-size: 100% 58px;
            background-position: center bottom;
          }
        }
      }
    }

    .region-left-bottom {
      .main_left_main {
        height: 734px;
        padding: 20px 20px 20px 40px;
        box-sizing: border-box;

        .left-bottom-top {
          height: 150px;
          width: 100%;
          display: flex;
          align-items: center;

          .left-bottom-top-item {
            width: 50%;
            height: 100%;
            background: url("@/assets/image/region-left-botton-1.png") no-repeat;
            background-size: 100% 100%;
            background-position: center bottom;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding-left: 25%;

            .left-bottom-top-item-name {
              margin-bottom: 10px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 600;
              font-size: 28px;
              color: #ffffff;
              line-height: 33px;
              text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
              text-align: left;
              font-style: normal;
              text-transform: none;
              width: 200px;
            }

            .left-bottom-top-item-num {
              width: 200px;
              font-family: DIN-BoldItalic, DIN-BoldItalic;
              font-weight: 400;
              font-size: 30px;
              line-height: 35px;
              text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
              text-align: left;
              font-style: normal;
              text-transform: none;
              background: linear-gradient(90deg, #ffffff 0%, #17a4f3 100%),
                linear-gradient(90deg, #ffffff 0%, #00d46a 100%);
              -webkit-background-clip: text;
              /* 仅对文字部分应用背景 */
              -webkit-text-fill-color: transparent;
              /* 设置文字颜色为透明 */
            }

            .left-bottom-top-item-num1 {
              background: linear-gradient(90deg, #ffffff 0%, #17a4f3 100%),
                linear-gradient(90deg, #ffffff 0%, #17a4f3 100%);
              -webkit-background-clip: text;
              /* 仅对文字部分应用背景 */
              -webkit-text-fill-color: transparent;
              /* 设置文字颜色为透明 */
            }
          }
        }

        .left-bottom-con {
          height: 500px;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: flex-start;

          .left-bottom-con-item {
            width: 33%;
            height: 30%;

            .item-title {
              box-sizing: border-box;
              width: 100%;
              height: 70px;
              line-height: 80px;
              text-align: left;
              padding-left: 60px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 600;
              font-size: 24px;
              color: #ffffff;
              text-shadow: 0px 2px 3px rgba(51, 158, 188, 0.79);
              font-style: normal;
              text-transform: none;
              background: url("@/assets/image/region-left-botton-2.png") no-repeat;
              background-size: 100% 100%;
            }

            .item-con {
              height: 100px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: url("@/assets/image/region-left-botton-bg.png") no-repeat;
              background-size: 100% 100%;

              .item-img {
                width: 71px;
                height: 71px;
                margin-right: 20px;
              }

              .item-box {
                .item-text {
                  display: flex;
                  align-items: center;

                  .item-text-name {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 600;
                    font-size: 20px;
                    color: #ffffff;
                    line-height: 23px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    margin-right: 20px;
                    position: relative;
                  }

                  .ren::after {
                    content: "";
                    position: absolute;
                    left: -10px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 40px;
                    height: 40%;
                    background: linear-gradient(90deg,
                        transparent,
                        #ceb830,
                        transparent);
                    filter: blur(4px);
                    opacity: 0.8;
                  }

                  .mian::after {
                    content: "";
                    position: absolute;
                    left: -10px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 40px;
                    height: 40%;
                    background: linear-gradient(90deg,
                        transparent,
                        #0094ff,
                        transparent);
                    filter: blur(4px);
                    opacity: 0.8;
                  }

                  .item-text-num {
                    font-family: DIN-BlackItalic, DIN-BlackItalic;
                    font-weight: 400;
                    font-size: 20px;
                    color: #ffffff;
                    line-height: 23px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    background: linear-gradient(90deg,
                        #ffffff 0%,
                        #00d46a 100%);
                    -webkit-background-clip: text;
                    /* 仅对文字部分应用背景 */
                    -webkit-text-fill-color: transparent;
                    /* 设置文字颜色为透明 */
                  }

                  .unit {
                    font-family: DIN, DIN;
                    font-weight: bold;
                    font-size: 16px;
                    color: #ffffff;
                    line-height: 19px;
                    text-align: right;
                    font-style: normal;
                    text-transform: none;
                    margin-left: 8px;
                  }

                  .mian-num {
                    background: linear-gradient(90deg,
                        #ffffff 0%,
                        #17a4f3 100%);
                    -webkit-background-clip: text;
                    /* 仅对文字部分应用背景 */
                    -webkit-text-fill-color: transparent;
                    /* 设置文字颜色为透明 */
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .region-content {
    flex: 1;
    margin-top: 54px;
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0 30px;

    .con {
      flex: 1;
      position: relative;
      background-image: url("@/assets/image/left-con.png"), url("@/assets/image/right-con.png");
      background-position: left center, right center;
      background-repeat: no-repeat;
      background-size: auto calc(100% - 80px), auto calc(100% - 80px);

      .con-top {
        position: absolute;
        top: -50px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 999;
      }

      .map2d {
        width: 100%;
        height: 100%;
      }
    }

    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .region-content-left-wrap {
        width: 50%;
        margin-right: 15px;

        .region-content-left-slot {
          display: flex;
          align-items: center;

          .slot-img {
            width: 63px;
            height: 63px;
          }

          .slot-name {
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 24px;
            color: #ffffff;
            line-height: 28px;
            letter-spacing: 1px;
            text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
            text-align: left;
            font-style: italic;
            text-transform: none;
            margin: 0 10px 0 2px;
          }

          .slot-num {
            font-family: DIN Black, DIN Black;
            font-weight: bold;
            font-size: 26px;
            line-height: 30px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(180deg, #ffffff 0%, #19a1f4 100%);
            -webkit-background-clip: text;
            /* 仅对文字部分应用背景 */
            -webkit-text-fill-color: transparent;
            /* 设置文字颜色为透明 */
          }
        }

        .region-content-left {
          padding: 21px 35px 0px 48px;

          .main_content_main {
            height: 615px;
            box-sizing: border-box;

            .chart-box {
              height: 100%;
              width: 100%;

              .chart-num {
                display: flex;
                justify-content: space-between;
                padding: 0 25px;
                font-size: 20px;
              }
            }
          }
        }
      }

      // 民意收集情况统计
      .region-content-right-wrap {
        margin-left: 15px;
        width: 50%;

        .region-content-right {
          padding: 21px 35px 0px 48px;

          .main_content_main {
            width: 100%;
            height: 615px;

            .content-right-top {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .content-right-item {
                width: 33%;
                display: flex;
                justify-content: space-around;
                align-items: center;

                .con-item-img {
                  width: 63px;
                  height: 63px;
                }

                .con-right-item-name {
                  display: inline-block;
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 600;
                  font-size: 30px;
                  color: #ffffff;
                  letter-spacing: 1px;
                  text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                }

                .con-right-item-num {
                  width: auto;

                  ::v-deep .number,
                  ::v-deep .suffix {
                    font-family: DIN Black, DIN Black;
                    font-weight: bold;
                    font-size: 30px;

                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    background: linear-gradient(180deg,
                        #ffffff 0%,
                        #19a1f4 100%);
                    -webkit-background-clip: text;
                    /* 仅对文字部分应用背景 */
                    -webkit-text-fill-color: transparent;
                    /* 设置文字颜色为透明 */
                  }
                }
              }
            }

            .content-right-con {
              height: calc(100% - 30px);
              width: 100%;
            }
          }
        }
      }
    }
  }

  .region-right {
    width: 839px;
    margin-top: 54px;
    margin-bottom: 25px;

    .region-right-top-wrap {
      margin-bottom: 38px;

      .region-right-top {
        padding: 21px 35px 0px 48px;

        .main_right_main {
          height: 1273px;

          .region-right-top-title {
            display: flex;
            align-items: center;

            .circle {
              width: 30px;
              /* 圆的直径 */
              height: 30px;
              border-radius: 50%;
              background: #062452;
              /* 渐变和背景色 */
              border: 1px solid #15adfa;
              display: flex;

              .dot {
                margin: auto;
                width: 10px;
                /* 圆的直径 */
                height: 10px;
                border-radius: 50%;
                background: #0a3c7c;
                /* 渐变和背景色 */
                border: 2px solid #659ccf;
                box-shadow: 0 0 0 2px #15adfa;
              }
            }

            .title {
              font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
              font-weight: 400;
              font-size: 30px;
              color: #ffffff;
              line-height: 35px;
              text-align: left;
              font-style: normal;
              text-transform: none;
              padding: 0 10px;
            }

            .title-img {
              width: 310px;
            }
          }

          .region-right-top-tj {
            width: 100%;
            margin-top: 20px;

            .top-tj-item {
              height: 186px;
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              justify-content: center;
              background: url("@/assets/image/region-right-bg1.png") no-repeat;
              background-size: contain;
              /* 背景图覆盖整个元素区域 */
              padding-bottom: 30px;
              box-sizing: border-box;

              .top-tj-item-name {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 28px;
                color: #ffffff;
                text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                width: 200px;
                padding-left: 20px;
                box-sizing: border-box;
              }

              .num-box {
                width: 200px;
                padding-left: 20px;
                padding-top: 10px;
                box-sizing: border-box;
                display: flex;
                align-items: flex-end;

                .top-tj-item-num {
                  width: auto;

                  ::v-deep .number {
                    font-family: DIN-BoldItalic, DIN-BoldItalic;
                    font-weight: bold;
                    font-size: 30px;
                    line-height: 35px;
                    text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    background: linear-gradient(180deg, #fff 0%, #00d46a 100%);
                    -webkit-background-clip: text;
                    /* 仅对文字部分应用背景 */
                    -webkit-text-fill-color: transparent;
                    /* 设置文字颜色为透明 */
                  }
                }
              }
            }

            .top-tj-item1 {
              background: url("@/assets/image/region-right-bg2.png") no-repeat;
              background-size: contain;
              /* 背景图覆盖整个元素区域 */
            }
          }

          .region-right-top-con {
            .region-right-top-con-item {
              margin-bottom: 30px;

              .item-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 25px;

                .title {
                  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
                  font-weight: 400;
                  font-size: 30px;
                  color: #ffffff;

                  .action {
                    color: #25e2f4;
                    font-size: 20px;
                    cursor: pointer;
                    margin-left: 10px;
                  }
                }

                .value {
                  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
                  font-weight: 900;
                  font-size: 26px;
                  color: #ffcf00;
                  text-shadow: -1px 0px 18px #ffcf00;
                }
              }

              .right-progress-bar {
                width: 100%;
                height: 30px;
                background: linear-gradient(-90deg,
                    rgba(0, 121, 255, 0.22) 0%,
                    rgba(2, 77, 169, 0.14) 55%,
                    rgba(4, 4, 26, 0) 100%);
                border-radius: 0px 0px 0px 0px;
                position: relative;
                margin-bottom: 25px;
                border-radius: 0 8px 8px 0;

                .bar {
                  max-width: 100%;
                  height: 100%;
                  width: 0;
                  background: linear-gradient(-90deg,
                      #1faede 0%,
                      rgba(14, 96, 178, 0) 100%);
                  border-radius: 0 8px 8px 0;
                }

                .cursor {
                  position: absolute;
                  top: -5px;
                  width: 42px;
                  height: 42px;
                  background: rgba(137, 164, 255, 0.5);
                  box-shadow: 0px 0px 6px 4px rgba(108, 176, 255, 0.65);
                  border-radius: 21px;
                  line-height: 42px;
                  text-align: center;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  z-index: 9;

                  .sub {
                    width: 26px;
                    height: 26px;
                    background: #b8eaff;
                    box-shadow: 0px 0px 5px 1px rgba(255, 255, 255, 0.85);
                    border-radius: 13px;
                  }
                }

                &::after {
                  content: "";
                  position: absolute;
                  right: 0;
                  top: 0;
                  width: 15px;
                  height: 30px;
                  background: #155497;
                  border-radius: 50%;
                }
              }
            }
          }
        }
      }
    }

    .region-right-bottom {
      .main_right_main {
        height: 503px;
        padding: 20px;
        box-sizing: border-box;

        .right-bottom-top {
          width: 100%;
          height: 50%;
          background: url("@/assets/image/region-right-bottom-bg1.png") no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
        }

        .right-bottom-bottom {
          background: url("@/assets/image/region-right-bottom-bg2.png") no-repeat;
          background-size: 100% 100%;
          width: 100%;
          height: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
        }

        .name {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 44px;
          color: #ffffff;
          line-height: 52px;
          text-shadow: 0px 0px 24px rgba(68, 142, 254, 0.71);
          text-align: left;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(90deg, #ffffff 0%, #17a4f3 100%);
          -webkit-background-clip: text;
          /* 仅对文字部分应用背景 */
          -webkit-text-fill-color: transparent;
          /* 设置文字颜色为透明 */
          margin-bottom: 20px;
        }

        .num {
          display: flex;
          align-items: flex-end;

          .el-statistic {
            width: auto;
          }

          ::v-deep .number {
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: bold;
            font-size: 50px;
            color: #ff9347;
            line-height: 59px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }

          .unit {
            font-family: DIN, DIN;
            font-weight: bold;
            font-size: 30px;
            color: #ffffff;
            line-height: 50px;
            text-shadow: 0px 4px 4px #1c527e;
            text-align: right;
            font-style: normal;
            text-transform: none;
            margin-left: 8px;
          }
        }
      }
    }
  }
}

.region-page::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 100%;
  background: url("@/assets/image/map-bg.png") no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  opacity: 0.8;
  /* 设置透明度 */
}
</style>
<style lang="less">
.con-top-popper-class {
  background: #0077b6;
  border: none;
}</style> 