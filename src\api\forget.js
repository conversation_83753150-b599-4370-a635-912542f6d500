import request from "@/utils/request";
import qs from "qs";


export function doUpdata(data) {
    return request({
        url: "/api/v1/manage/user/forgetpassword",
        method: "post",
        data
    });
}

export function validateAccount(data) {
    return request({
        url: "/api/v1/manage/user/validateAccount",
        method: "post",
        data
    });
}

export function sendMsg(data) {
    return request({
        url: "/api/v1/manage/user/sendcodeMsg",
        method: "post",
        data
    });
}