<template>
  <div ref="chart" class="chart"></div>
</template>

<script>
export default {
  name: "bar<PERSON><PERSON>",
  props: {
    type: {
      type: String,
      default: 'age' // 默认显示年龄分布
    },
    data: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      chart: null,
      // ageData: [
      //   { value: 40, name: "30岁以下" },
      //   { value: 38, name: "30岁至40岁" },
      //   { value: 32, name: "40岁至50岁" },
      //   { value: 30, name: "50岁至60岁" },
      //   { value: 28, name: "60岁以上" },
      // ],
      // eduData: [
      //   { value: 60.3, name: "研究生及以上" },
      //   { value: 30.6, name: "大学本科" },
      //   { value: 12.1, name: "大专高职" },
      //   { value: 13.6, name: "中专职高高中" },
      //   { value: 6.6, name: "初中及以下" },
      // ]
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.chart) {
        this.initChart();
      }
    });
  },
  watch: {
    type: {
      handler() {
        this.$nextTick(() => {
          if (this.chart) {
            this.chart.dispose();
          }
          this.initChart();
        });
      }
    }
  },
  methods: {
    initChart() {
      if (!this.$refs.chart) return;
      const currentData = this.type === 'age' ? this.data : this.data;
      console.log('当前数据:', currentData);

      // 计算总和用于验证
      const total = currentData.reduce((sum, item) => sum + item.value, 0);
      console.log('数据总和:', total);

      this.chart = this.$echarts.init(this.$refs.chart);
      const option = {
        legend: {
          orient: "vertical",
          right: "0%",
          top: "middle",
          itemGap: 30,
          itemWidth: 24,
          itemHeight: 24,
          textStyle: {
            fontSize: 24,
            color: '#fff',
            lineHeight: 28,
            verticalAlign: 'middle',
            padding: [0, 0, 0, 8],
            fontWeight: 'normal'
          },
          itemStyle: {
            borderWidth: 0
          },
          icon: 'rect',
          symbolKeepAspect: true,
          align: 'left',
          selectedMode: false,
          textAlign: 'left',
          y: 'middle',
          width: '45%'
        },
        tooltip: {
          show: true,
          formatter: function (params) {
            console.log('tooltip params:', params);
            return params.name + ': ' + params.percent + '%';
          },
          textStyle: {
            fontSize: 24,
            fontWeight: "bold",
          },
        },
        series: [
          {
            name: "Nightingale Chart",
            type: "pie",
            radius: [0, "55%"],
            center: ["36%", "50%"],
            roseType: "radius",
            avoidLabelOverlap: true,
            label: {
              show: true,
              position: 'outside',
              fontSize: 24,
              color: '#fff',
              formatter: function (params) {
                // console.log('label params:', params);
                return params.percent.toFixed(1) + '%';
              },
              alignTo: 'labelLine',
              edgeDistance: '2%',
              overflow: 'none',
              minMargin: 4,
              lineHeight: 28,
              fontWeight: 'normal'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 24,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true,
              length: 5,
              length2: 8,
              maxSurfaceAngle: 80,
              minTurnAngle: 60,
              smooth: true
            },
            itemStyle: {
              borderRadius: 0,
              borderWidth: 2,
              borderColor: '#041c48'
            },
            data: currentData,
          },
        ],
        color: ["#FF5252", "#4CAF50", "#2196F3", "#FFC107", "#9C27B0"],
      };

      this.chart.setOption(option);

      // 添加事件监听以查看交互
      this.chart.on('mouseover', (params) => {
        console.log('mouseover event:', params);
      });
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
};
</script>

<style lang="less" scoped>
.chart {
  width: 100%;
  height: 150%;
  margin-top: -150px;
}
</style>
