<template>
  <div class="scmp-title">
    <div class="title_bg">
      <span>议案建议</span>
    </div>
    <div class="title-form">
      <el-form
        :inline="true"
        :model="seachForm"
        ref="seachForm"
        class="demo-form-inline"
      >
        <!-- 议案动态搜索功能 -->
        <el-form-item label="标题" prop="title">
          <el-input
          style="width:600px"
            v-model="seachForm.title"
            placeholder="请输入标题进行查询"
          ></el-input>
        </el-form-item>
        <el-form-item label="代表姓名"  prop="name">
          <el-input
          style="width:330px"
            v-model="seachForm.name"
            placeholder="请输入代表姓名进行查询"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button type="success" @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "scmp-title",
  data() {
    return {
      seachForm: {
        title: "",
        name: "",
      },
    };
  },
  methods: {
    onSubmit() {
      this.$emit("getList", this.seachForm);
    },
    resetForm() {
      // this.$refs["seachForm"].resetFields();
      this.seachForm =  {
        title: "",
        name: "",
      },
      this.$emit("resetFields", this.seachForm);

    },
  },
};
</script>

<style scoped lang="less">
.scmp-title {
  font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
  font-weight: bold;
  font-size: 34px;
  color: #ffffff;
  line-height: 40px;
  text-shadow: 0px 0px 7px rgba(75, 180, 229, 0.25),
    0px 2px 8px rgba(5, 28, 55, 0.42);
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  flex-direction: row;
  .title_bg {
    position: relative;
    background: url("@/assets/image/title_big.png") no-repeat;
    background-size: 100% 100%;
    width: 890px;
    height: 169px;
    span {
      position: absolute;
      top: 48px;
      left: 122px;
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 60px;
      color: #ffffff;
      line-height: 70px;
      text-shadow: 0px 0px 7px rgba(75, 180, 229, 0.25),
        0px 2px 8px rgba(5, 28, 55, 0.42);
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .title-form {
    flex: 1;
    // 标题自定义样式
    ::v-deep .el-form-item__label {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 30px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    ::v-deep .el-form--inline .el-form-item__content {
      vertical-align: initial;
    }
    ::v-deep .el-input__inner {
      background: rgba(57, 205, 255, 0.12);
      background-image: none;
      border-radius: 4px;
      border: 1px solid #7edcfb;
      box-sizing: border-box;
      display: inline-block;
      height: 52px;
      line-height: 52px;
      outline: 0;
      padding: 0 15px;
      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
      width: 100%;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 26px;
      color: #ffffff;
      font-style: normal;
      text-transform: none;
    }
    .demo-form-inline {
      width: calc(100% + 400px);
      margin-left: -420px;
      margin-top: 85px;
    }
    .el-button--primary {
      background: rgba(10, 255, 255, 0.21);
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #33fefe;

      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 30px;
      color: #33fefe;
      font-style: normal;
      text-transform: none;
    }
    .el-button--success {
      background: rgba(14, 200, 88, 0.22);
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #0ec858;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 30px;
      color: #0ec858;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
