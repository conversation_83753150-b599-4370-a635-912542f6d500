import { getUserInfo } from "@/api/user";
import router from "../../routers";

const state = { defaultRoute: "" };
const mutations = {
  setDefaultRoute: (state, defaultRoute) => {
    state.defaultRoute = defaultRoute;
  },
};
const actions = {
  // 从平台获取首页默认接口
  getDefaultRoutersByPlatform({ commit }) {
    return new Promise((resolve, reject) => {
      getUserInfo().then( res => {
        commit('setDefaultRoute', res.data.menus[0].url);
        router.addRoute({
          path: '/',
          redirect: res.data.menus[0].url
        },)
        resolve();
      })
    });
  }
};
export default { state, mutations, actions };
