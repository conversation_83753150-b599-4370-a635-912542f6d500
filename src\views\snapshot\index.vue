<template>
  <div class="snapshot-page">
    <div class="snapshot-main_left">
      <ScmpCard cardName="事项分类统计">
        <div slot="main" class="snapshot-main_left_warp">
          <div class="main_left_main_top">
            <div class="main_content_main">
              <div class="chart-box">
                <!-- <div class="chart-num">
                  <span v-for="(item, index) in barChartDataTop.slice(0, 11)" :key="index">
                    {{ item }}
                  </span>
                </div> -->
                <BarChartTop :data="barChartDataTop.slice(0, 11)" :xAxis="xAxisListTop.slice(0, 11)" />
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
      <ScmpCard :cardName="`${yearValue1}年每月办理情况`" style="margin-top: 44px">
        <div slot="main" class="snapshot-main_left_warp">
          <div class="main_left_main_center" v-if="hBar.length > 6">
            <HBarChart style="width:100%;height:1040px;" :data="hBar" :tooltip="true" />
          </div>
          <div class="main_left_main_center" v-else>
            <HBarChart style="width:100%;height:90%" :data="hBar" :tooltip="true" />
          </div>
        </div>
      </ScmpCard>
      <ScmpCard :rightPicture="true" :cardName="`事项在${yearValue1}年代表团意见分布情况`" style="margin-top: 44px">
        <!-- <div slot="titleRight">
          <TimeNext :value="yearValue1" @henld-next="handleNext" @change-Year="handleYearChange" />
        </div> -->
        <div slot="main" class="snapshot-main_left_warp">
          <div class="main_left_main_bottom" @wheel="handleWheel" @mousedown="handleMouseDown" @mousemove="handleMouseMove" @mouseup="handleMouseUp" @mouseleave="handleMouseUp">
            <div class="main_content_main">
              <div class="chart-box">
                <!-- <div class="chart-num">
                  <span v-for="(item, index) in barChartData" :key="index">
                    {{ item }}
                  </span>
                </div> -->
                <BarChart :data="barChartData" :xAxis="xAxisList" />
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
    </div>

    <div class="snapshot-main_center">
      <div class="center-top">
        <ScmpSpot :spotName=titleName dropDownTitle="切换专题" :column="spotDownColumn" :dropDownList="dropDownList"
          :path="path" @checkedItem="checkedItem">
        </ScmpSpot>
      </div>
      <div class="center-header">
        <div class="top warp" style="margin-left:20px">
          <div class="title">已办结</div>

          <div class="ybj">{{ completed }}</div>
        </div>
        <div class="top warp">
          <div class="title">未办结</div>

          <div class="wbj">{{ unfinished }}</div>
        </div>
        <!-- ghw mod -->
        <!-- 数据展示需要优化 -->
        <div class="top top_center_stu warp">
          <!-- <div class="tj_center">1</div>
          <div class="fh">,</div>
          <div class="tj_center">1</div>
          <div class="tj_center">1</div>
          <div class="tj_center">1</div> -->
          <div v-for="(digit, index) in formattedData" :key="index">
            <!-- 如果是逗号，使用特定的 class="fh" -->
            <div v-if="digit === ','" class="fh">{{ digit }}</div>
            <!-- 如果是数字，展示数字 -->
            <div v-else class="tj_center">{{ digit }}</div>
          </div>
        </div>
        <div class="top warp">
          <div class="title">今日随手拍</div>
          <div class="sspNumber">{{ dailyNum }}</div>
          <div class="desc">
            代表提出次数
          </div>
        </div>
        <div class="top warp">
          <div class="title">本月随手拍</div>
          <div class="sspNumber">{{ monthNum }}</div>
          <div class="desc">
            代表提出次数
          </div>
        </div>
      </div>
      <div style="width: 100%; height: 1400px; margin-top: 350px;" v-if="administrativeAreaId">
        <Map2d class="map2d" style="width: 100%; height: 100%" :mapAddress="mapAddress" :areaName="areaName"
          :code="administrativeAreaId" @getMapInfo="getMapInfo" />
      </div>
      <div style="width: 100%; height: 1800px; margin-top: 10%;" v-else>
        <MapChart style="width: 100%; height: 1700px" @getMapInfo="getMapInfo"></MapChart>
      </div>

    </div>

    <div class="snapshot-main_right">
      <ScmpCard cardName="代表随手拍满意度评价" pictureMode="2" style="height: 507px">
        <div slot="main" style="width: 100%; height: 100%">
          <div class="snapshot-main_right_top">
            <div class="pie-chart-container pie-chart-container-left">
              <div>
                <div class="pie-chart-container-title">第一次</div>
                <pieChart :chartData="firstChartData" />
              </div>
            </div>
            <div class="pie-chart-container pie-chart-container-right">
              <div>
                <div class="pie-chart-container-title">第二次</div>
                <pieChart :chartData="secondChartData" />
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
      <ScmpCard cardName="今日随手拍事项" pictureMode="2" style="margin-top: 44px">
        <div slot="main">
          <ScmpTable :tableData="deptDictList" :tableColumn="deptDictTableColumn" :rowNum="12" :indicesGrouping="true">
          </ScmpTable>
        </div>
      </ScmpCard>
    </div>
    <!-- 地图弹窗 -->
    <map-modal :visible.sync="dialogVisible" :modalForm="modalForm" :districtName="districtName" />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getMemberCommentHubCount,
  getMemberCommentCommentCategory,
  getMemberCommentCompletedForYear,
  getMemberCommentCountForDistrict,
  getDailyMemberCommentList,
  getMemberCommentSatisfaction,
  getMemberCommentSatisfactionForDistrict,
  // 浮窗
  // getLiaisonInfoForDistrict,
} from "@/api/dataPageApi/snapshot";
import store from "../../stores";
export default {
  name: "snapshot-index",
  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    ScmpTable: () => import("@/comps/scmp-table"),
    ScmpSpot: () => import("@/comps/scmp-spot"),
    TimeNext: () => import("@/comps/time-next2"),
    MapChart: () => import("@/components/snapshot/map2D.vue"),
    Map2d: () => import("@/components/junior/map2D.vue"),
    BarChart: () => import("@/components/snapshot/bar-chart.vue"),
    BarChartTop: () => import("@/components/snapshot/bar-chart-top.vue"),
    HBarChart: () => import("@/components/snapshot/hBar-chart.vue"),
    pieChart: () => import("@/components/snapshot/pie-chart.vue"),
    MapModal: () => import("@/components/dome/map-modal3.vue"),
  },
  data() {
    return {
      yearValue1: String(new Date().getFullYear()),
      // Table配置
      deptDictList: [],
      deptDictTableColumn: [
        { prop: "sort", label: "序号", align: "center" },
        { prop: "userName", label: "姓名" },
        { prop: "commentCategory", label: "类型" },
        { prop: "subject", label: "涉事主题" },
      ],
      // 意见分类统计数据
      barChartDataTop: [],
      xAxisListTop: [],
      // 本年-每月办理情况
      hBar: [
        {
          month: "01月",
          name: "2024-01",
          value1: 264,
          value2: 184,
        },
        {
          month: "02月",
          name: "2024-02",
          value1: 404,
          value2: 164,
        },
        {
          month: "03月",
          name: "2024-03",
          value1: 619.59,
          value2: 354.0,
        },
        {
          month: "04月",
          name: "2024-04",
          value1: 338.01,
          value2: 154.0,
        },
        {
          month: "05月",
          name: "2024-05",
          value1: 619.59,
          value2: 354.0,
        },
        {
          month: "06月",
          name: "2024-06",
          value1: 338.01,
          value2: 154.0,
        },
      ],
      // 2024 年各代表团反映事项分布情况
      barChartData: [],
      xAxisList: [],
      // 单项滑动相关
      currentIndex: 0,
      displayItemCount: 11, // 一次显示的数据项数量
      totalBarChartData: [],
      totalXAxisList: [],
      // 滚动节流相关
      scrollTimer: null,
      isScrolling: false,
      // 鼠标拖拽相关
      isDragging: false,
      dragStartX: 0,
      dragCurrentX: 0,
      dragThreshold: 50, // 拖拽阈值，超过此距离才触发滑动
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [{ name: "菜单项1" }, { name: "菜单项2" }],
      firstChartData: [
        { name: "满意", value: 0 },
        { name: "基本满意", value: 0 },
        { name: "不满意", value: 0 }
      ],
      secondChartData: [
        { name: "满意", value: 0 },
        { name: "基本满意", value: 0 },
        { name: "不满意", value: 0 }
      ],
      // 地图弹窗
      dialogVisible: false,
      modalForm: {},
      token: '',
      path: '',
      completed: '',
      dailyNum: '',
      monthNum: '',
      unfinished: '',
      yearNum: '',
      // 区名
      districtName: '',
      administrativeAreaId: '',
      mapAddress: '',
      areaName: '',
      // 起始时间
      beginTime: '',
      // 结束时间
      endTime: '',
      titleName: ''
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
    formattedData() {
      //判断下是否有千位
      if (this.yearNum.length < 3) {
        return this.yearNum.split(""); // 如果年数小于4位，直接返回数组
      }
      // 将年数分割为数组，并在千位和百位之间插入逗号
      const yearArray = this.yearNum.split("");
      yearArray.splice(1, 0, ","); // 在千位和百位之间添加逗号
      return yearArray;
    }
  },
  created() {
    // 对应区的地图路径
    this.mapAddress = `'../mapJson/${this.$route.query.administrativeAreaId}.json'`
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.titleName = this.$route.query.areaName;
    if (this.titleName) {
      this.titleName =  this.$route.query.areaName + '人大代表随手拍工作视窗'
      // this.titleName = '区人大代表随手拍工作视窗'
    } else {
      // this.titleName = '广州市人大代表随手拍工作视视窗'
      this.titleName = '市人大代表随手拍工作视视窗'
    }
    this.token = this.$checkToken();
    this.path = this.$route.path
    this.yearValue1 = this.$route.query.year
    this.getMemberCommentHubCount(this.yearValue1, this.token)
    this.getMemberCommentCommentCategory(this.yearValue1, this.token)
    this.getMemberCommentCompletedForYear(this.yearValue1, this.token)
    this.getMemberCommentCountForDistrict(this.yearValue1, null, null)
    this.getDailyMemberCommentList(this.yearValue1, this.token)
  },
  async mounted() {
    await this.getMemberCommentSatisfaction(this.yearValue1, this.token)
  },
  watch: {},
  methods: {
    // 单项滑动方法
    updateCurrentDisplayData() {
      // 计算当前显示的数据范围
      const start = this.currentIndex;
      const end = Math.min(start + this.displayItemCount, this.totalBarChartData.length);

      // 更新当前显示的数据
      this.barChartData = this.totalBarChartData.slice(start, end);
      this.xAxisList = this.totalXAxisList.slice(start, end);
    },

    // 向左滑动（显示前一个数据项）
    slidePrev() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
        this.updateCurrentDisplayData();
      }
    },

    // 向右滑动（显示后一个数据项）
    slideNext() {
      const maxIndex = Math.max(0, this.totalBarChartData.length - this.displayItemCount);
      if (this.currentIndex < maxIndex) {
        this.currentIndex++;
        this.updateCurrentDisplayData();
      }
    },
    handleWheel(event) {
      // 阻止默认滚动行为
      event.preventDefault();

      // 如果正在滚动中，直接返回
      if (this.isScrolling) {
        return;
      }

      // 设置滚动状态
      this.isScrolling = true;

      // 获取滚轮方向和强度
      const delta = event.deltaY;

      // 设置滚动阈值，需要更大的滚动量才触发滑动
      const threshold = 50; // 降低阈值，使滑动更灵敏

      if (Math.abs(delta) > threshold) {
        if (delta > 0) {
          // 向下滚动，向右滑动（显示后一个数据项）
          this.slideNext();
        } else {
          // 向上滚动，向左滑动（显示前一个数据项）
          this.slidePrev();
        }
      }

      // 设置冷却时间，防止过快滑动
      setTimeout(() => {
        this.isScrolling = false;
      }, 200); // 缩短冷却时间至200ms
    },

    // 鼠标拖拽事件处理
    handleMouseDown(event) {
      this.isDragging = true;
      this.dragStartX = event.clientX;
      this.dragCurrentX = event.clientX;
      // 阻止默认选择行为
      event.preventDefault();
    },

    handleMouseMove(event) {
      if (!this.isDragging) return;

      this.dragCurrentX = event.clientX;
      const deltaX = this.dragCurrentX - this.dragStartX;

      // 可以在这里添加视觉反馈，比如改变鼠标样式
      if (Math.abs(deltaX) > this.dragThreshold) {
        event.target.style.cursor = 'grabbing';
      }
    },

    handleMouseUp(event) {
      if (!this.isDragging) return;

      const deltaX = this.dragCurrentX - this.dragStartX;

      // 根据拖拽距离和方向决定滑动
      if (Math.abs(deltaX) > this.dragThreshold) {
        if (deltaX > 0) {
          // 向右拖拽，显示前一个数据项
          this.slidePrev();
        } else {
          // 向左拖拽，显示后一个数据项
          this.slideNext();
        }
      }

      // 重置拖拽状态
      this.isDragging = false;
      this.dragStartX = 0;
      this.dragCurrentX = 0;
      event.target.style.cursor = 'default';
    },
    handleYearChange(newDateRange) {
      console.log('接收到日期变化:', newDateRange);
      this.beginTime = newDateRange[0]
      this.endTime = newDateRange[1]
      this.getMemberCommentCountForDistrict(this.yearValue1, newDateRange[0], newDateRange[1])
      // 在这里处理日期变化逻辑
    },
    getMemberCommentHubCount(data, token) {
      const params = {
        // token: token,
        administrativeAreaId: this.administrativeAreaId
      }
      getMemberCommentHubCount(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.completed = res.data.completed
          this.dailyNum = res.data.dailyNum
          this.monthNum = res.data.monthNum
          this.unfinished = res.data.unfinished
          this.yearNum = res.data.yearNum
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getMemberCommentCommentCategory(data, token) {
      const params = {
        // token: token,
        administrativeAreaId: this.administrativeAreaId,
        year: data
      }
      getMemberCommentCommentCategory(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 按 num 从大到小排序
          const sortedData = res.data.sort((a, b) => b.num - a.num);
          sortedData.forEach(item => {
            this.barChartDataTop.push(item.num);
            this.xAxisListTop.push(item.commentCategory);
          });
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getMemberCommentCompletedForYear(data, token) {
      const params = {
        // token: token,
        administrativeAreaId: this.administrativeAreaId,
        year: data
      }
      getMemberCommentCompletedForYear(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          res.data
          this.hBar = res.data.map(item => ({
            month: item.orderMonth.split("-")[1] + "月",  // 提取 "01" 并拼接 "月"
            name: item.orderMonth,
            value1: item.unfinishedNum,
            value2: item.completedNum,
          }));
          console.log('hBar')
          console.log(this.hBar)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getMemberCommentCountForDistrict(data, beginTime, endTime) {
      const params = {
        // token: token,
        year: data,
        administrativeAreaId: this.administrativeAreaId,
        beginTime: beginTime || this.beginTime,
        endTime: endTime || this.endTime
      }
      getMemberCommentCountForDistrict(params)
        .then(res => {
          console.log('getMemberCommentCountForDistrict response:', res);
          const rawData = res.data || [];

          if(params.administrativeAreaId == null || params.administrativeAreaId == undefined){
            // 市级数据 - 使用固定的代表团顺序
            const targetOrder = [
              "越秀代表团",
              "海珠代表团",
              "荔湾代表团",
              "天河代表团",
              "白云代表团",
              "黄埔代表团",
              "花都代表团",
              "番禺代表团",
              "南沙代表团",
              "从化代表团",
              "增城代表团",
            ];
            // 初始化 totalXAxisList 和 totalBarChartData
            this.totalXAxisList = [...targetOrder];
            this.totalBarChartData = new Array(targetOrder.length).fill(0);

            // 将 rawData 转换为一个以 name 为键的对象，方便快速查找
            const dataMap = rawData.reduce((map, item) => {
              map[item.orgName] = item.num;
              return map;
            }, {});

            // 填充数据
            this.totalXAxisList.forEach((orgName, index) => {
              if (dataMap[orgName] !== undefined) {
                this.totalBarChartData[index] = dataMap[orgName];
              }
            });
          } else {
            // 区级数据 - 动态生成
            this.totalXAxisList = rawData.map(item => item.orgName);
            this.totalBarChartData = rawData.map(item => item.num);
          }

          console.log('totalXAxisList:', this.totalXAxisList);
          console.log('totalBarChartData:', this.totalBarChartData);

          // 重置到第一个数据项并更新当前显示数据
          this.currentIndex = 0;
          this.updateCurrentDisplayData();
        })
        .catch(error => {
          console.error('请求失败', error);
        });
    },

    getDailyMemberCommentList(data, token) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        // token: token,
        administrativeAreaId: this.administrativeAreaId
      }
      getDailyMemberCommentList(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.deptDictList = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },


    getMemberCommentSatisfaction(data, token) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        token: token,
        administrativeAreaId: this.administrativeAreaId
      }
      getMemberCommentSatisfaction(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          const fieldNameToChinese = {
            satisfiedNum: "满意",
            generallySatisfiedNum: "基本满意",
            notSatisfiedNum: "不满意"
          };
          this.firstChartData = Object.entries(res.data.first).map(([field, value]) => ({
            name: fieldNameToChinese[field],  // 获取对应的中文名
            value: parseInt(value) || 0          // 字符串转数字
          }));
          console.log(this.firstChartData)

          this.secondChartData = Object.entries(res.data.second).map(([field, value]) => ({
            name: fieldNameToChinese[field],  // 获取对应的中文名
            value: parseInt(value) || 0          // 字符串转数字
          }));
          console.log(this.secondChartData)

        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    // 该接口无数据 是否跟人大代表工作视窗浮窗一致？
    getMemberCommentSatisfactionForDistrict(data, token) {
      const params = {
        administrativeAreaId: data
      }
      getMemberCommentSatisfactionForDistrict(params, token)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.modalForm = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },
    // getLiaisonInfoForDistrict(data, token) {
    //   const params = {
    //     cockpitQuery: '',
    //     administrativeAreaId: data
    //   }
    //   getLiaisonInfoForDistrict(params, token)
    //     .then(res => {
    //       console.log(res);  // 处理返回的数据
    //       this.modalForm = res.data
    //     })
    //     .catch(error => {
    //       console.error('请求失败', error);  // 处理错误
    //     });
    // },



    checkedItem(obj) {
      console.log("选中", obj);
    },
    handleNext() {
      console.log("进入专题");
    },
    getMapInfo(val, administrativeAreaId) {
      console.log('val >>>', val);
      this.dialogVisible = true
      // console.log('administrativeAreaId >>>', administrativeAreaId.features[val.dataIndex].properties.adcode);
      // this.modalForm = val
      this.districtName = val.data.name || administrativeAreaId.features[val.dataIndex].properties.name
      console.log(val.data.name)
      this.getMemberCommentSatisfactionForDistrict(administrativeAreaId.features[val.dataIndex].properties.adcode, this.token)
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
::v-deep .scmp-spot {
  width: 1290px !important;

  .title {
    left: 45% !important;
  }
}

.snapshot-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .snapshot-main_left {
    width: 853px;
    margin-top: 54px;
    margin-bottom: 25px;

    .snapshot-main_left_warp {
      padding: 21px 35px 0px 48px;

      .main_left_main_top {
        height: 355px;

        .main_content_main {
          height: 100%;
          box-sizing: border-box;

          .chart-box {
            height: calc(100% - 30px);
            // height: calc(100% - 0px);
            width: 100%;
            // margin-top: 10%;
            .chart-num {
               display: flex;
              justify-content: space-between;
              // padding: 0 30px;
              font-size: 28px;
            }
          }
        }
      }

      .main_left_main_center {
        height: 535px;
        overflow-y: auto;
        /* 启用垂直滚动 */
        scrollbar-width: none;
        /* Firefox 隐藏滚动条 */
        -ms-overflow-style: none;
        /* IE/Edge 隐藏滚动条 */
      }

      .main_left_main_center::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        background: transparent;
      }

      .main_left_main_bottom {
        height: 495px;
        cursor: grab;
        user-select: none; /* 防止文本选择 */

        &:active {
          cursor: grabbing;
        }

        .main_content_main {
          height: 100%;
          box-sizing: border-box;

          .chart-box {
            height: 95%;
            width: 100%;

            .chart-num {
              display: flex;
              justify-content: space-between;
              // padding: 0 25px;
              font-size: 28px;
              pointer-events: none; /* 防止数字文本干扰拖拽 */
            }
          }
        }
      }
    }
  }

  .snapshot-main_center {
    width: 1992px;
    position: relative;

    .center-top {
      display: flex;
      flex-direction: row;
      justify-content: center;
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      line-height: 25px;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      position: relative;
    }

    .center-header {
      position: absolute;
      left: 49%;
      top: 38px;
      transform: translateX(-50%);
      z-index: 9;
      width: 1848px;
      height: 418px;
      background: url("@/assets/image/center_header.png") no-repeat;
      background-position: center;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      padding: 50px 0 0 0;

      .top {
        margin-top: 150px;
      }

      .warp {
        height: min-content;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 300px;
        display: flex;
        flex-direction: column;

        .title {
          font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;


          font-weight: 400;
          font-size: 40px;
          color: #ffffff;
          line-height: 47px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .ybj {
          margin-top: 22px;
          font-family: DIN, DIN;
          font-weight: 500;
          font-size: 80px;
          color: #12d4ff;
          line-height: 94px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(90deg, #ffffff 0%, #17a4f3 100%);
          -webkit-background-clip: text;
          /* WebKit 浏览器专用属性，使背景剪切至文本 */
          background-clip: text;
          /* 标准属性 */
        }

        .wbj {
          margin-top: 22px;
          font-family: DIN, DIN;
          font-weight: 500;
          font-size: 80px;
          color: #ca320b;
          line-height: 94px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(90deg, #ffffff 0%, #ca320b 100%);
          -webkit-background-clip: text;
          /* WebKit 浏览器专用属性，使背景剪切至文本 */
          background-clip: text;
          /* 标准属性 */
        }

        .sspNumber {
          font-family: DIN, DIN;
          font-weight: 500;
          font-size: 50px;
          color: #00d46a;
          margin: 10px;
          line-height: 59px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(90deg, #ffffff 0%, #00d46a 100%);
          -webkit-background-clip: text;
          /* WebKit 浏览器专用属性，使背景剪切至文本 */
          background-clip: text;
          /* 标准属性 */
        }

        .desc {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 28px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          background: url("@/assets/image/region-left-botton-bg.png") no-repeat;
          background-size: 100% 100%;
        }
      }

      .top_center_stu {
        display: flex;
        flex-direction: row;
        padding: 40px 0 0 0;

        .tj_center {
          width: 84px;
          height: 114px;
          background: rgba(255, 255, 255, 0);
          box-shadow: inset 0px 0px 14px 0px rgba(155, 199, 255, 0.25);
          border-radius: 6px 6px 6px 6px;
          border: 3px solid #ffffff;

          font-family: DIN, DIN;
          font-weight: 500;
          font-size: 90px;
          color: #ffe062;
          line-height: 105px;
          text-shadow: 0px 0px 13px rgba(121, 175, 255, 0.96);
          text-align: center;
          font-style: normal;
          text-transform: none;
          -webkit-background-clip: text;
          /* WebKit 浏览器专用属性，使背景剪切至文本 */
          background-clip: text;
          /* 标准属性 */
          margin: 0 10px;
        }

        .fh {
          font-family: DIN, DIN;
          font-weight: 500;
          font-size: 90px;
          color: #ffe062;
          line-height: 105px;
          text-shadow: 0px 0px 13px rgba(121, 175, 255, 0.96);
          text-align: center;
          font-style: normal;
          text-transform: none;
          -webkit-background-clip: text;
          /* WebKit 浏览器专用属性，使背景剪切至文本 */
          background-clip: text;
          /* 标准属性 */
        }
      }
    }
  }

  .snapshot-main_right {
    width: 839px;
    margin-bottom: 25px;
    margin-top: 54px;

    .snapshot-main_right_top {
      display: flex;
      flex-direction: row;
      width: 100%;
      height: 100%;
      gap: 20px;
      padding: 0 10px;

      .pie-chart-container {
        width: 50%;
        height: 100%;
        position: relative;

        >div {
          display: flex;
          flex-direction: column;
          height: 100%;
          padding-top: 20px;
        }

        .pie-chart-container-title {
          text-align: center;
          font-size: 48px;
          margin-top: 20px;
          color: #fff;
          line-height: 1;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        }

        :deep(.chart) {
          height: calc(100% - 88px) !important;
        }

        &::before,
        &::after {
          content: '';
          position: absolute;
          width: 1px;
          height: 100%;
          top: 0;
          pointer-events: none;
          background-size: 1px 100%;
        }

        &::before {
          left: 0;
          background: url('@/assets/image/line.png') left center/1px 100% no-repeat;
        }

        &::after {
          right: 0;
          background: url('@/assets/image/line.png') right center/1px 100% no-repeat;
        }
      }

      .pie-chart-container-left {
        &::before {
          left: 40px;
        }
      }

      .pie-chart-container-right {
        &::after {
          right: 60px;
        }

      }
    }
  }
}

.snapshot-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/map_bg.png") no-repeat;
  background-position: center;
  background-size: 100% 100%;
  opacity: 0.6;
  /* 设置透明度 */
}
</style>
