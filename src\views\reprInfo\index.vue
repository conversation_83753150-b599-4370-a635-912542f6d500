<template>
  <div class="snapshot-page">
    <div class="snapshot-main_left">
      <ScmpCard :cardName="cardName1">
        <div slot="main" class="snapshot-main_left_warp">
          <div class="main_left_main_top">
            <div class="main_content_main">
              <div class="top-con-box">
                <div class="top-con-box-item">
                  <div class="text-box">
                    <div class="name">连任代表</div>
                    <div class="num">{{ sdbData.sflrbz || 0 }}<span>人</span></div>
                  </div>
                  <div class="zhanbi-box">
                    <span class="name">占比</span>
                    <!-- <i class="el-icon-top"></i> -->
                    <span class="num">{{ sdbData.sflrbzRatio1 || 0 }}%</span>
                  </div>
                </div>
                <div class="top-con-box-item bg2">
                  <div class="text-box">
                    <div class="name">归侨侨眷</div>
                    <div class="num">{{ sdbData.sfgqbz || 0 }} <span>人</span></div>
                  </div>
                  <div class="zhanbi-box">
                    <span class="name">占比</span>
                    <!-- <i class="el-icon-top"></i> -->
                    <span class="num">{{ sdbData.sfgqbzRatio1 || 0 }}%</span>
                  </div>
                </div>
                <div class="top-con-box-item bg3">
                  <div class="text-box">
                    <div class="name">妇女</div>
                    <div class="num">{{ sdbData.fvnum || 0 }} <span>人</span></div>
                  </div>
                  <div class="zhanbi-box">
                    <span class="name">占比</span>
                    <!-- <i class="el-icon-top"></i> -->
                    <span class="num">{{ sdbData.fvRatio1 || 0 }}%</span>
                  </div>
                </div>
                <div class="top-con-box-item bg4">
                  <div class="text-box">
                    <div class="name">少数民族</div>
                    <div class="num">{{ sdbData.ssmz || 0 }} <span>人</span></div>
                  </div>
                  <div class="zhanbi-box">
                    <span class="name">占比</span>
                    <!-- <i class="el-icon-top"></i> -->
                    <span class="num">{{ sdbData.ssmzRatio1 || 0 }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
      <ScmpCard :cardName="cardName2" style="margin-top: 44px">
        <div slot="top" class="switch-buttons">
          <div class="switch-btn" :class="{ active: activeDistType === 'age' }" @click="switchDistType('age')">
            年龄分布情况
          </div>
          <div class="switch-btn" :class="{ active: activeDistType === 'edu' }" @click="switchDistType('edu')">
            学历分布情况
          </div>
        </div>
        <div slot="main" class="snapshot-main_left_warp">
          <div class="main_left_main_bottom">
            <PieChart v-if="chartData.length > 0" :type="activeDistType" :data="chartData" />
          </div>
        </div>
      </ScmpCard>
    </div>

    <div class="snapshot-main_center">
      <div class="center-top">
        <ScmpSpot :spotName=areaName dropDownTitle="切换专题" :column="spotDownColumn" :dropDownList="dropDownList"
          @checkedItem="checkedItem">
        </ScmpSpot>
      </div>
      <div style="width: 100%; height: 1400px" class="snapshot-main_center-con">
        <Map2d class="map2d" :mapAddress="mapAddress" :areaName="areaName" :code="administrativeAreaId"
          @getMapInfo="getMapInfo" v-if="administrativeAreaId" />
        <MapChart style="width: 100%; height: 1380px" @getMapInfo="getMapInfo" v-else></MapChart>

        <div
          style="display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 10px; width: 90%; margin-left: 5%;margin-right: 5%;  margin-bottom: 40px">
          <div class="con-box box1" style="width: 28%; margin-bottom: 20px">
            <div class="name" style="font-size: 36px;">五级人大代表总数</div>
            <div class="num">{{ dbNumCount.allNum || 0 }}<span>人</span></div>
          </div>
          <div class="con-box box2" style="width: 28%; margin-bottom: 20px">
            <div class="name">全国人大代表</div>
            <div class="num">{{ dbNumCount.country }}<span>人</span></div>
          </div>
          <div class="con-box box3" style="width: 28%; margin-bottom: 20px">
            <div class="name">省人大代表</div>
            <div class="num">{{ dbNumCount.province }}<span>人</span></div>
          </div>
          <div class="con-box box4" style="width: 28%">
            <div class="name">市人大代表</div>
            <div class="num">{{ dbNumCount.city }}<span>人</span></div>
          </div>
          <div class="con-box box5" style="width: 28%">
            <div class="name">区人大代表</div>
            <div class="num">{{ dbNumCount.administrativeArea }}<span>人</span></div>
          </div>
          <div class="con-box box6" style="width: 28%">
            <div class="name">镇人大代表</div>
            <div class="num">{{ dbNumCount.streetTown }}<span>人</span></div>
          </div>
        </div>

      </div>
    </div>

    <div class="snapshot-main_right">
      <ScmpCard :cardName="cardName3" pictureMode="2" style="height: 507px">
        <div slot="main" style="width: 100%; height: 100%">
          <div class="snapshot-main_right_top">
            <PieChart1 :pie-data="pieData" :internal-diameter-ratio="0.7" />
          </div>
        </div>
      </ScmpCard>
      <ScmpCard :cardName="cardName4" pictureMode="2" style="margin-top: 44px">
        <div slot="main" class="right-bottom-main">
          <div class="chart-box2">
            <PieChart2 :data="zzmmData" />
          </div>
          <div class="bar-box">
            <div class="right-bottom-item" v-for="(item, index) in progressBarList" :key="index">
              <div class="item-title">
                <span class="title">{{ item.name }} </span>
                <span class="value">{{ item.value }}人</span>
              </div>
              <div class="right-progress-bar">
                <div class="bar" :style="`width:${(item.value / 100) * 100}%`"></div>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>
    </div>
    <!-- 地图弹窗 -->
    <map-modal :visible.sync="dialogVisible" :modalForm="modalForm" :districtName="districtName" />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getDbCountInfoForDistrict,
  getDbLevelCount,
  getDbAgeDistribution,
  getDbDegreeDistribution,
  getQUDbSynthesisInfo,
  getDbSynthesisInfo,
  getJobTypeCount,
  getPoliticalStatus
} from "@/api/dataPageApi/reprInfo";
export default {
  name: "snapshot-index",
  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    ScmpSpot: () => import("@/comps/scmp-spot"),
    // TimeNext: () => import("@/comps/time-next"),
    MapChart: () => import("@/components/snapshot/map2D.vue"),
    Map2d: () => import("@/components/junior/map2D.vue"),
    // BarChart: () => import("@/components/snapshot/bar-chart.vue"),
    // BarChartTop: () => import("@/components/snapshot/bar-chart-top.vue"),
    MapModal: () => import("@/components/dome/map-modal2.vue"),
    PieChart: () => import("./charts/pie-chart.vue"),
    PieChart1: () => import("./charts/pie-chart1.vue"),
    PieChart2: () => import("./charts/pie-chart2.vue"),
  },
  data() {
    return {
      SHICount: 0,
      QUCount: 0,
      yearValue1: String(new Date().getFullYear()),
      activeDistType: 'age',  // 确保这里正确初始化
      // Table配置
      deptDictList: [

      ],
      deptDictTableColumn: [
        { prop: "sort", label: "序号", align: "center" },
        { prop: "deptName", label: "姓名" },
        { prop: "catalogNum", label: "类型" },
        { prop: "mountRate", label: "涉事主题" },
      ],
      // 2024 年各代表团反映事项分布情况
      barChartData: [76, 65, 55, 45, 50, 60, 30, 49, 47, 49, 61],
      xAxisList: [
        "越秀区",
        "海珠区",
        "荔湾区",
        "天河区",
        "白云区",
        "黄埔区",
        "花都区",
        "番禺区",
        "南沙区",
        "从化区",
        "增城区",
      ],
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],

      // 地图弹窗
      dialogVisible: false,
      modalForm: {},

      pieData: [
        // {
        //   name: "基层一线代表（工人、农民、专业技术人员）",
        //   value: 50,
        //   itemStyle: { color: "#8979FF" },
        // },
        // { name: "国有企业负责人", value: 30, itemStyle: { color: "#f46b3a" } },
        // {
        //   name: "非公有制经济人士",
        //   value: 20,
        //   itemStyle: { color: "#3CC3DF" },
        // },
        // { name: "事业单位负责人", value: 20, itemStyle: { color: "#FFAE4C" } },
      ],
      progressBarList: [],
      zzmmData: [
        { value: 0, name: "中共党员" },
        { value: 0, name: "民主党派成员" },
        { value: 0, name: "群众" },
        { value: 0, name: "无党派人士" },
      ],
      dbNumCount: [],
      chartData: [],
      sdbData: [],
      // 区名
      districtName: '',
      areaName: '',
      administrativeAreaId: 0,
      cardName1: '市人大代表综合构成',
      cardName2: '市人大代表年龄与学历分布情况总览',
      cardName3: '市人大代表类别',
      cardName4: '市人大代表政治面貌构成',
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {

    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.SHICount = this.$route.query.SHICount;
    this.QUCount = this.$route.query.QUCount;
    this.areaName = this.$route.query.areaName
    this.mapAddress = `'../mapJson/${this.$route.query.administrativeAreaId}.json'`
    // this.getDbCountInfoForDistrict()

    if (this.areaName) {
      this.areaName = this.$route.query.areaName + '人大代表信息工作视窗'
      this.cardName1 = this.$route.query.areaName + '代表综合情况'
      this.cardName2 = this.$route.query.areaName + '代表年龄与学历分布情况总览'
      this.cardName3 = this.$route.query.areaName + '代表类别'
      this.cardName4 = this.$route.query.areaName + '代表政治面貌构成'

    } else {
      this.areaName = '广州市人大代表信息工作视窗'
    }
    this.getDbLevelCount()
    this.getDbSynthesisInfo()
    this.getPoliticalStatus()
  },
  mounted() {
    this.getDbAgeDistribution('age')
    this.getJobTypeCount()
  },
  watch: {},
  methods: {
    getDbCountInfoForDistrict(data, token) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        administrativeAreaId: this.administrativeAreaId || data || '',
        streetTownId: ''
      }
      getDbCountInfoForDistrict(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.modalForm = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getDbLevelCount(data, token) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        administrativeAreaId: this.administrativeAreaId || '',
      }
      getDbLevelCount(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.dbNumCount = res.data
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },


    getDbAgeDistribution(type) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        administrativeAreaId: this.administrativeAreaId || '',
      }
      getDbAgeDistribution(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.activeDistType = type;
          // this.chartData = res.data
          // if (type == 'age') {
          const ageGroupMapping = {
            ageGroup1: "30岁以下",
            ageGroup2: "30岁至40岁",
            ageGroup3: "40岁至50岁",
            ageGroup4: "50岁至60岁",
            ageGroup5: "60岁以上",
          };
          this.chartData = Object.keys(res.data).map((key) => ({
            value: res.data[key], // 获取对应的值
            name: ageGroupMapping[key], // 获取对应的年龄段名称
          }));
          console.log('chartData')
          console.log(this.chartData)
          // } else {
          // 暂无数据
          // }
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getDbDegreeDistribution(type) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        administrativeAreaId: this.administrativeAreaId || '',
      }
      getDbDegreeDistribution(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.activeDistType = type;
          this.chartData = res.data.map(item => ({
            value: item.num,
            name: item.xlMc
          }));
          console.log('chartData')
          console.log(this.chartData)
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getDbSynthesisInfo(data, token) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        administrativeAreaId: this.administrativeAreaId || '',

      }
      console.log("----------111111-------", this.administrativeAreaId)
      if (this.administrativeAreaId != null && this.administrativeAreaId != '') {
        getQUDbSynthesisInfo(params)
          .then(res => {
            console.log("11111111111111111111111111111111111返回数据", res);  // 处理返回的数据
            this.sdbData = res.data
            //计算占比
            //注意判空
            console.log("this.QUCount", this.QUCount)
            if (this.QUCount == 0) {
              this.QUCount = 1;
            }
            if (res.data.sflrbz) {
              res.data.sflrbzRatio1 = ((res.data.sflrbz / this.QUCount) * 100).toFixed(2);
            }
            if (res.data.sfgqbz) {
              res.data.sfgqbzRatio1 = ((res.data.sfgqbz / this.QUCount) * 100).toFixed(2);
            }
            if (res.data.ssmz) {
              res.data.ssmzRatio1 = ((res.data.ssmz / this.QUCount) * 100).toFixed(2);
            }
            if (res.data.fvnum) {
              res.data.fvRatio1 = ((res.data.fvnum / this.QUCount) * 100).toFixed(2);
            }

          }).catch(error => {
            console.error('请求失败', error);  // 处理错误
          });
      } else {
        getDbSynthesisInfo(params)
          .then(res => {
            console.log(res);  // 处理返回的数据
            this.sdbData = res.data
            //计算占比
            //注意判空
            if (this.SHICount == 0) {
              this.SHICount = 1;
            }
            if (res.data.sflrbz) {
              res.data.sflrbzRatio1 = ((res.data.sflrbz / this.SHICount) * 100).toFixed(2);
            }
            if (res.data.sfgqbz) {
              res.data.sfgqbzRatio1 = ((res.data.sfgqbz / this.SHICount) * 100).toFixed(2);
            }
            if (res.data.ssmz) {
              res.data.ssmzRatio1 = ((res.data.ssmz / this.SHICount) * 100).toFixed(2);
            }
            if (res.data.fvnum) {
              res.data.fvRatio1 = ((res.data.fvnum / this.SHICount) * 100).toFixed(2);
            }
          })
          .catch(error => {
            console.error('请求失败', error);  // 处理错误
          });
      }
    },

    async getJobTypeCount(data, token) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        administrativeAreaId: this.administrativeAreaId || '',
      }
      await getJobTypeCount(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 定义合并规则和颜色
          const mergedCategories = [
            {
              name: "基层一线代表（工人、农民、专业技术人员）",
              includes: ["一线工农", "专业技术人员"],
              color: "#8979FF"
            },
            {
              name: "国有企业负责人",
              includes: ["企业单位负责人"],
              color: "#f46b3a"
            },
            // {
            //   name: "党政领导干部",
            //   includes: ["党政领导干部"],
            //   color: "#3CC3DF"
            // },
            {
              name: "公务员",
              includes: ["公务员"],
              color: "#4682B4"
            },
            {
              name: "非公企业负责人",
              includes: ["非公企业负责人"],
              color: "#fac858"
            },
            {
              name: "事业单位负责人",
              includes: ["事业单位负责人"],
              color: "#FFAE4C"
            },
            {
              name: "其他",
              includes: ["其他", "社会组织负责人", "解放军"],
              color: "#3CC3DF"
            }
          ];

          // 计算合并后的数据
          this.pieData = mergedCategories.map(category => {
            const value = res.data
              .filter(item => category.includes.includes(item.zygcmc))
              .reduce((sum, item) => sum + (item.num || 0), 0); // 确保 item.num 有效
            return {
              name: category.name,
              value: parseInt(value) || 0, // 兜底：如果 value 为 NaN，使用 0
              itemStyle: { color: category.color }
            };
          });
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getPoliticalStatus(data, token) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        administrativeAreaId: this.administrativeAreaId || '',

      }
      getPoliticalStatus(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 定义分类规则
          const categories = [
            {
              name: "中共党员",
              includes: ["01"] // 01-中共党员，09-中国致公党党员
            },
            {
              name: "民主党派成员",
              includes: ["04", "05", "06", "07", "08", "11"] // 04-11 民主党派
            },
            {
              name: "无党派人士",
              includes: ["12"] // 12-无党派民主人士
            },
            {
              name: "群众",
              includes: ["16"] // 10-九三学社社员
            },
          ];
          // 计算各分类的 value
          this.zzmmData = categories.map(category => {
            const value = res.data
              .filter(item => category.includes.includes(item.zzmmDm))
              .reduce((sum, item) => sum + item.num, 0);
            return {
              name: category.name,
              value: value
            };
          });

          const targetParties = [
            "中国国民党革命委员会会员",
            "中国民主同盟盟员",
            "中国民主建国会会员",
            "中国民主促进会会员",
            "中国农工民主党党员",
            "中国致公党党员",
            "九三学社社员",
            "台湾民主自治同盟盟员"
          ];

          this.progressBarList = res.data
            .filter(item => targetParties.includes(item.zzmmMc))
            .map(item => ({
              name: item.zzmmMc,
              value: item.num
            }));
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    checkedItem(obj) {
      console.log("选中", obj);
    },
    getList(value) {
      console.log("查询数据", value);
    },

    handleNext() {
      console.log("进入专题");
    },
    getMapInfo(val, administrativeAreaId) {
      console.log("val >>>", val);
      console.log('administrativeAreaId >>>', administrativeAreaId.features[val.dataIndex].properties.adcode);
      // this.modalForm = val;
      this.districtName = val.data.name || administrativeAreaId.features[val.dataIndex].properties.name
      this.dialogVisible = true;
      this.getDbCountInfoForDistrict(administrativeAreaId.features[val.dataIndex].properties.adcode)
    },
    switchDistType(type) {
      console.log('切换类型:', type);  // 添加调试日志
      // 如果点击的是当前已选中的选项，则直接返回，避免重复触发
      if (this.activeDistType === type) {
        return;
      }
      this.activeDistType = type;
      if (type == 'edu') {
        this.chartData = []
        this.getDbDegreeDistribution('edu')
      } else {
        this.chartData = []
        this.getDbAgeDistribution('age')
      }
      // 强制更新视图
      this.$forceUpdate();
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
::v-deep .scmp-spot {
  width: 1290px !important;

  .title {
    left: 45% !important;
  }
}

.snapshot-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .snapshot-main_left {
    width: 853px;
    margin-top: 54px;
    margin-bottom: 25px;

    .snapshot-main_left_warp {
      padding: 21px 35px 0px 48px;

      .main_left_main_top {
        height: 923px;

        .main_content_main {
          height: 100%;
          box-sizing: border-box;

          .top-con-box {
            height: calc(100% - 20px);
            width: 100%;
            padding: 0 20px;
            box-sizing: border-box;

            .top-con-box-item {
              width: 100%;
              height: 25%;
              background: url("@/assets/image/reprInfo-left1.png") no-repeat;
              background-position: center;
              background-size: 100% 100%;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;

              .text-box {
                padding-bottom: 30px;

                .name {
                  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
                  font-size: 40px;
                  color: #ffffff;
                  line-height: 59px;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                }

                .num {
                  font-family: SiYuanYaHei, SiYuanYaHei;
                  font-size: 40px;
                  color: #28f1fa;
                  line-height: 70px;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);

                  span {
                    font-family: SiYuanYaHei, SiYuanYaHei;
                    //font-weight: bold;
                    margin-left: 10px;
                    font-size: 40px;
                    color: #ffffff;
                    line-height: 19px;
                  }
                }
              }

              .zhanbi-box {
                position: absolute;
                bottom: 60px;
                right: 10px;
                font-size: 40px;

                .name {
                  font-family: SiYuanYaHei, SiYuanYaHei;
                  font-weight: 600;
                  color: #ffffff;
                  line-height: 35px;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                }

                .el-icon-top {
                  font-size: 30px;
                  color: #57d569;
                }

                .num {
                  font-family: SiYuanYaHei, SiYuanYaHei;
                  font-weight: 600;
                  line-height: 20px;
                  text-align: right;
                  font-style: normal;
                  text-transform: none;
                  background: linear-gradient(90.00000000000007deg,
                      #ffffff 0%,
                      #57d569 100%);
                  -webkit-text-stroke: 0px #ffffff;
                  -webkit-background-clip: text;
                  background-clip: text;
                }
              }
            }

            .bg2 {
              background: url("@/assets/image/reprInfo-left2.png") no-repeat;
              background-position: center;
              background-size: 100% 100%;
            }

            .bg3 {
              background: url("@/assets/image/reprInfo-left3.png") no-repeat;
              background-position: center;
              background-size: 100% 100%;
            }

            .bg4 {
              background: url("@/assets/image/reprInfo-left4.png") no-repeat;
              background-position: center;
              background-size: 100% 100%;
            }
          }
        }
      }

      .main_left_main_bottom {
        height: 500px;
        padding: 20px;
        box-sizing: border-box;
      }
    }

    .switch-buttons {
      display: flex;
      justify-content: flex-start;
      gap: 20px;
      margin: 70px 0 20px 48px;

      .switch-btn {
        padding: 4px 30px 10px 30px;
        // 文字倾斜，其它的不倾斜
        font-style: italic;


        font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
        font-size: 28px;
        color: #ffffff;
        cursor: pointer;
        min-width: 200px;
        text-align: center;
        background: url("@/assets/image/perform-left-img2.png") no-repeat center;
        background-size: 100% 100%;
        z-index: 1;
        position: relative;
        user-select: none;

        &.active {
          background: url("@/assets/image/perform-left-img1.png") no-repeat center;
          background-size: 100% 100%;
        }

        &:hover {
          opacity: 0.9;
        }
      }
    }
  }

  .snapshot-main_center {
    width: 1992px;
    position: relative;

    .center-top {
      display: flex;
      flex-direction: row;
      justify-content: center;
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      line-height: 25px;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      position: relative;
    }

    .snapshot-main_center-con {
      position: relative;

      .con-box {
        width: 419px;
        height: 256px;
        // position: absolute;
        background: url("@/assets/image/reprInfo-con-bg1.png") no-repeat bottom center,
          url("@/assets/image/reprInfo-con-bg2.png") no-repeat right;
        background-size: 100% 70%, 50% 90%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-top: 40px;
        padding-left: 40px;
        box-sizing: border-box;

        .name {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 40px;
          color: #ffffff;
          line-height: 41px;
          text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
          text-stroke: 0px rgba(255, 255, 255, 0.11);
          text-align: left;
          font-style: normal;
          text-transform: none;
          -webkit-text-stroke: 0px #ffffff;
          -webkit-background-clip: text;
          /* WebKit 浏览器专用属性，使背景剪切至文本 */
          background-clip: text;
          /* 标准属性 */
        }

        .num {
          display: flex;
          justify-content: space-between;
          margin-top: 5px;
          width: 202px;
          font-family: DIN-MediumItalic, DIN-MediumItalic;
          font-weight: 400;
          font-size: 50px;
          color: #29f1fa;
          line-height: 59px;
          text-shadow: 0px 0px 7px rgba(255, 179, 179, 0.71);
          text-align: center;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(90deg, #ffffff 0%, #ca320b 100%),
            linear-gradient(90deg, #f6ffff 0%, #29f1fa 100%);
          -webkit-background-clip: text;
          /* WebKit 浏览器专用属性，使背景剪切至文本 */
          background-clip: text;
          justify-content: space-evenly;

          /* 标准属性 */
          span {
            color: #fff;
            font-size: 40px;
            margin-top: 5px;
          }
        }
      }

      .box1 {
        top: 11%;
        left: 12%;
      }

      .box2 {
        top: 37%;
        left: 1%;
      }

      .box3 {
        top: 59%;
        left: 12%;
      }

      .box4 {
        top: 14%;
        left: 75%;
      }

      .box5 {
        top: 36%;
        left: 79%;
      }

      .box6 {
        top: 62%;
        left: 74%;
      }
    }
  }

  .snapshot-main_right {
    width: 839px;
    margin-bottom: 25px;
    margin-top: 54px;

    .snapshot-main_right_top {
      width: 100%;
      height: 100%;
      padding: 40px;
      box-sizing: border-box;
      background: url("@/assets/image/Down_Particle.png") no-repeat;
      background-size: 80% 80%;
      background-position: calc(20%) center;
    }
  }

  .right-bottom-main {
    height: 1250px;
    padding-bottom: 90px;
    box-sizing: border-box;

    .chart-box2 {
      margin-top: 20px;
      height: 40%;
      padding: 0 10px;
      box-sizing: border-box;
    }

    .bar-box {
      margin-top: -80px;
      height: 60%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .title-text {
        .title-text-left {
          display: inline-block;
        }

        display: flex;
        align-items: center;
        gap: 0px;
        padding: 0 60px 0 25px;
        font-family: YouSheBiaoTiHei,
        YouSheBiaoTiHei;
        font-size: 36px;
        color: #fff;
        margin-bottom: 20px;
        position: relative;

        .line {
          margin-left: 10px;
          flex: 1;
          height: 0.5px;
          background: linear-gradient(90deg, rgba(19, 118, 230, 0.8), rgba(96, 164, 243, 0.3));
        }
      }
    }

    .right-bottom-item {
      margin-bottom: 15px;
      padding: 0 60px;

      .item-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 5px;

        .title {
          /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/
          font-weight: 400;
          font-size: 40px;
          color: #ffffff;

          .action {
            color: #25e2f4;
            font-size: 40px;
            cursor: pointer;
            margin-left: 10px;
          }
        }

        .value {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 900;
          font-size: 40px;
          color: #ffcf00;
          // text-shadow: -1px 0px 18px #ffcf00;
        }
      }

      .right-progress-bar {
        width: 100%;
        height: 30px;
        background: linear-gradient(-90deg,
            rgba(0, 121, 255, 0.22) 0%,
            rgba(2, 77, 169, 0.14) 55%,
            rgba(4, 4, 26, 0) 100%);
        border-radius: 0px 0px 0px 0px;
        position: relative;

        border-radius: 0 8px 8px 0;

        .bar {
          max-width: 100%;
          height: 100%;
          width: 0;
          background: linear-gradient(90deg,
              rgba(251, 233, 71, 0.1) 0%,
              #fbe947 100%);
          border-radius: 0 8px 8px 0;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            right: 0;
            top: 0;
            width: 10px;
            height: 30px;
            background: #fff;
            border-radius: 50%;
          }
        }

        &::after {
          content: "";
          position: absolute;
          right: 0;
          top: 0;
          width: 15px;
          height: 30px;
          background: #155497;
          border-radius: 50%;
        }
      }
    }
  }
}

.snapshot-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/map_bg.png") no-repeat;
  background-position: center;
  background-size: 100% 100%;
  opacity: 0.6;
  /* 设置透明度 */
}

@keyframes glow {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }

  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

.map2d {
  width: 100%;
  height: 100%;
}
</style>
