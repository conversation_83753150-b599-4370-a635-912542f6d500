const path = require("path");
function resolve(dir) {
  return path.join(__dirname, dir);
}

function mockServer() {
  if (process.env.NODE_ENV === "development") {
    const mockServer = require("./mock/mock-server.js");
    return mockServer;
  } else {
    return "";
  }
}
module.exports = {
  // 基本路径
  publicPath:
    process.env.NODE_ENV === "production" ? "/sjjsc/" : "/",
  // publicPath: '/', // 部署在根目录

  // 输出文件目录
  outputDir: "dist",

  // 静态资源目录 (js, css, img, fonts)
  assetsDir: "assets",

  // 生产环境是否生成 sourceMap 文件
  productionSourceMap: false,

  // CSS 相关选项
  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    extract: false,
    // 开启 CSS source maps?
    sourceMap: false,

    //  less 脚本编译
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
        },
      },
      sass: {
        // sass-loader 打包的时候，会将 /*no*/ /*px*/注释掉
        sassOptions: {
          outputStyle: "expanded",
        },
      },
    },
  },

  // devServer 代理设置
  devServer: {
    host: "0.0.0.0",
    port: 8080,
    https: false,
    hot: true,
    open: true,
    proxy: {
      // 配置跨域处理 可以设置你想要代理的接口
      // "/api": {
      //   target: "http://api.example.com",
      //   changeOrigin: true,
      //   pathRewrite: {
      //     "^/api": "",
      //   },
      // },
      // '/api': {
      //   target: 'https://rdtest.rd.gz.cn',
      //   changeOrigin: true,
      //   pathRewrite: { '^/api': '' },
      // },
      [process.env.VUE_APP_BASE_API]: {
        // target: "http://nfs7md.natappfree.cc",
        // target: "https://rdtest.rd.gz.cn/base",
        target: "https://rdtest.rd.gz.cn/base",
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: "",
        },
        api: {
          target: "http://127.0.0.1:8081/base",
          changeOrigin: true,
          pathRewrite: {
            ["^api"]: "",
          },
        },
      },
    },
    // 该项目版本不适配
    // after: mockServer(),
    onAfterSetupMiddleware: function(devServer) {
      mockServer(); 
    },
    historyApiFallback: true,
  },

  // 插件选项
  pluginOptions: {
    // ...
  },

  // configureWebpack 或 chainWebpack 调整内部webpack配置
  configureWebpack: (config) => {
    if (process.env.NODE_ENV === "production") {
      // 为生产环境修改配置...
    } else {
      // 为开发环境修改配置...
    }
  },

  // 配置 webpack-chain 中的 chainWebpack
  chainWebpack: (config) => {
    // 设置别名
    config.resolve.alias
      .set("@", path.resolve(__dirname, "src"))
      .set("~components", path.resolve(__dirname, "src/components"))
      .set("~views", path.resolve(__dirname, "src/views"));

    // 如果需要，可以在这里修改其他 webpack 配置...
  },

  // 保存时不允许 eslint
  lintOnSave: false,
};
