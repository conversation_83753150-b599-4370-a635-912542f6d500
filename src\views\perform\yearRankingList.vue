<template>
  <div class="liaisonStation-page">
    <div class="liaisonStation-main_center">
      <div class="center-calendar">
        <ScmpCard cardName="履职活动列表" backgroundImage="card_bg2" :rightPicture="false">
          <div slot="main" class="con-bottom-box">
            <div class="header">
              <div class="btn" @click="$router.go(-1)">返回</div>
            </div>
            
            <ScmpTable :tableData="rankingData" :tableColumn="dataColumn" :rowNum="11" @row-click="handleRowClick">
            </ScmpTable>
            <ScmpPagination 
              :total="total" 
              :current-page.sync="currentPage" 
              :page-size.sync="pageSize" 
              @current-change="handleCurrentChange" 
              @size-change="handleSizeChange" />
          </div>
        </ScmpCard>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getYearDutyForDistrictList ,getMonthDutyForDistrictList } from "@/api/dataPageApi/perform";

export default {
  name: "yearRankingList",
  components: {
    ScmpCard: () => import("@/comps/scmp-YAJYCard"),
    ScmpTable: () => import("@/comps/scmp-YAJYTable"),
    ScmpPagination: () => import("@/components/pagination/index.vue"),
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      rankingData: [], // 活动数据列表
      year: 2025,
      administrativeAreaId: '',
      administrativeAreaName: '',
      type: '',
      dataColumn: [
        { prop: 'activityNo', label: '活动编号', width: '400', align: 'center' },
        { prop: 'activityName', label: '活动名称', width: '1000', align: 'center', showOverflowTooltip: true },
        { prop: 'orgName', label: '组织单位', width: '400', align: 'center', showOverflowTooltip: true },
        { prop: 'address', label: '活动地点', width: '400', align: 'center', showOverflowTooltip: true },
        // { prop: 'currStateName', label: '当前状态', width: '400', align: 'center' },
        // { prop: 'activityTypeName', label: '活动类型', width: '400', align: 'center' },
        { prop: 'startTimeFormatted', label: '开始时间', width: '400', align: 'center' },
        { prop: 'endTimeFormatted', label: '结束时间', width: '400', align: 'center' },
        { prop: 'creatorName', label: '创建人', width: '400', align: 'center' },
        // { prop: 'createTimeFormatted', label: '创建时间', width: '400', align: 'center' }
      ],
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"])
  },
  created() {
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.year = this.$route.query.year || 2025;
    this.administrativeAreaName = this.$route.query.administrativeAreaName || ''; // 获取选中的地区名称
    this.type = this.$route.query.type || ''; // 获取活动类型
    this.loadRankingData();
  },
  methods: {
    // 加载活动数据
    loadRankingData() {
      const data = {
        year: this.year,
        administrativeAreaId: this.administrativeAreaId,
        administrativeAreaName: this.administrativeAreaName,
   
      };
        const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize
        };
      
      console.log('请求参数:', data);
      console.log('接口方法:', getYearDutyForDistrictList);
      if(this.type == 'month') {
        getMonthDutyForDistrictList(data,params)
          .then(res => {
            console.log('月活动数据:', res);
            if (res.code == 200 && res.rows) {
              // 处理分页数据结构
              if (Array.isArray(res.rows)) {
              this.rankingData = res.rows.map((item, index) => ({
                ...item,
                startTimeFormatted: this.formatDate(item.startTime),
                endTimeFormatted: this.formatDate(item.endTime),
                createTimeFormatted: this.formatDate(item.createTime),
                address: item.address || '-',
                currStateName: item.currStateName || '-',
                activityTypeName: item.activityTypeName || '-',
                creatorName: item.creatorName || '-',
                activityName: item.activityName || '-',
                orgName: item.orgName || '-',
                activityNo: item.activityNo || '-'
              }));
              
              this.total = res.total || 0;
            } else {
              console.error('数据格式错误: res.rows 不是数组', res);
              this.$message.error('数据格式错误');
            }
          } else {
            console.error('接口返回错误:', res);
            this.$message.error(res.msg || '数据加载失败');
          }
        })
        .catch(error => {
          console.error('请求失败', error);
          console.error('请求参数:', data);
          this.$message.error(`数据加载失败: ${error.message || error}`);
        });
      } else {
        getYearDutyForDistrictList(data, params)
          .then(res => {
            console.log('年度活动数据:', res);
            if (res.code == 200 && res.rows) {
            // 处理分页数据结构
            if (Array.isArray(res.rows)) {
              this.rankingData = res.rows.map((item, index) => ({
                ...item,
                startTimeFormatted: this.formatDate(item.startTime),
                endTimeFormatted: this.formatDate(item.endTime),
                createTimeFormatted: this.formatDate(item.createTime),
                address: item.address || '-',
                currStateName: item.currStateName || '-',
                activityTypeName: item.activityTypeName || '-',
                creatorName: item.creatorName || '-',
                activityName: item.activityName || '-',
                orgName: item.orgName || '-',
                activityNo: item.activityNo || '-'
              }));
              
              this.total = res.total || 0;
            } else {
              console.error('数据格式错误: res.rows 不是数组', res);
              this.$message.error('数据格式错误');
            }
          } else {
            console.error('接口返回错误:', res);
            this.$message.error(res.msg || '数据加载失败');
          }
        })
        .catch(error => {
          console.error('请求失败', error);
          console.error('请求参数:', data);
          this.$message.error(`数据加载失败: ${error.message || error}`);
        });
      }
    },
    
    // 页码改变事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.rankingData = []; // 清空当前数据
      this.loadRankingData();
    },
    
    // 每页条数改变事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; // 重置到第一页
      this.rankingData = []; // 清空当前数据
      this.loadRankingData();
    },
    
    // 获取排名样式类
    getRankClass(rank) {
      if (rank === 1) return 'rank-first';
      if (rank === 2) return 'rank-second';
      if (rank === 3) return 'rank-third';
      return 'rank-normal';
    },

    // 处理表格行点击事件
    handleRowClick(row) {
      console.log('点击行数据:', row);
      // 获取活动ID，可能在 id 或 activityId 字段中
      const activityId = row.id ;
      if (activityId) {
        this.$router.push({
          path: '/dutyActiveDetail',
          query: { id: activityId }
        });
      } else {
        this.$message.warning('该条数据暂无详情信息');
        console.warn('未找到活动ID', row);
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '-';
        
        // 格式化为: 2025-03-13 10:00
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return '-';
      }
    }
  }
};
</script>

<style lang="less" scoped>
.liaisonStation-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .header {
    position: absolute;
    right: 60px;
    top: 40px;

    .btn {
      padding: 10px 38px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 40px;
      color: #ffffff;
      line-height: 47px;
      letter-spacing: 4px;
      background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
      border-radius: 6px 6px 6px 6px;
      height: 56px;
      cursor: pointer;
    }
  }

  .liaisonStation-main_center {
    flex: 1;
    margin-left: -200px;

    .center-calendar {
      height: 1430px;
      margin-top: 25px;
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      padding-left: 178px;
    }
  }
}

.liaisonStation-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
}

.con-bottom-box {
  height: 1300px;
  padding: 40px 80px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
