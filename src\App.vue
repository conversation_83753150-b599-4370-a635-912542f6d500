<template>
  <div>
    <!-- 新增全局加载提示 -->
    <!-- <div v-if="loading" class="global-loading"
      style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 24px; color: #333;">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
      </div>
    </div> -->
    <div class="loading-mask" v-if="loading || $route.meta.loading">
      <div class="loading-spinner">
        <!-- <i class="el-icon-loading"></i> -->
        <p>Loading...</p>
      </div>
    </div>
    <div v-if="!isLoginPage && !isRegionOrJunior" v-cloak id="app">
      <component :is="'BL-' + layout" />
    </div>
    <div v-else-if="isRegionOrJunior" v-cloak id="app">
      <component :is="currentRegionComponent" />
    </div>
    <router-view v-else />
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "App",
  components: {
    "BL-TCB": () => import("./layouts/BL-TCB"),
  },
  data() {
    return {
      loading: false // 新增加载状态
    };
  },
  computed: {
    ...mapState("RouteState", ["fullPath"]),
    ...mapState("layout/ly", ["layout"]),
    isLoginPage() {
      return this.$route.path === "/login";
    },
    pathList() {
      return [
        "/",
        "/region",
        "/junior",
        "/motion",
        "/quMotion",
        "/liaisonStation",
        "/popularWill",
        "/perform",
        "/liaisonDetail/handlingOpinion",
        "/dataTemplatePopularwill",
        "/permission/getDefaultRoutersByPlatform"
      ];
    },
    isRegionOrJunior() {
      return this.pathList.includes(this.$route.path);
    },
    currentRegionComponent() {
      const self = this;
      self.loading = true;
      return this.pathList.includes(this.$route.path)
        ? () => import(/* webpackPreload: true */ "./layouts/BL-TCB").finally(() => {
          // 延迟隐藏loading，避免组件未渲染完成就关闭
          setTimeout(() => self.loading = false, 1000);
          // self.loading = false
        })
        : () => import(/* webpackPreload: true */ "./layouts/BL-TCB2").finally(() => {
          // setTimeout(() => self.loading = false, 200);
          self.loading = false
        });
    }
  },
  mounted() {
    // 如果是首次进入/region页面，显示loading
    if (this.$route.path === '/region' || this.$route.path === '/popularWill' || this.$route.path === '/perform') {
      this.loading = true;
    }

    // 加载这个角色的首页到store
    // this.$store.dispatch("permission/getDefaultRoutersByPlatform")
    //   .finally(() => {
    //     this.loading = false;
    //   });

    if (
      "-ms-scroll-limit" in document.documentElement.style &&
      "-ms-ime-align" in document.documentElement.style
    ) {
      if (window.addEventListener) {
        window.addEventListener(
          "hashchange",
          () => {
            const currentPath = window.location.hash.slice(1);
            console.log('currentPath')
            console.log(currentPath)
            if (this.$route.path !== currentPath) {
              this.$router.push(currentPath);
              // this.$router.push(
              //   {
              //     path: currentPath,
              //     query: this.$route.query
              //   }
              // );
            }
          },
          false
        );
      } else if (window.attachEvent) {
        window.attachEvent("onhashchange", () => {
          const currentPath = window.location.hash.slice(1);
          console.log('currentPath')
          console.log(currentPath)
          if (this.$route.path !== currentPath) {
            this.$router.push(currentPath);
            // this.$router.push(
            //   {
            //     path: currentPath,
            //     query: this.$route.query
            //   }
            // );
          }
        });
      }
    }
  },
};
</script>

<style lang="less">
html {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  vertical-align: baseline;
  overflow-y: hidden;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

body {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  vertical-align: baseline;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

/* 隐藏滚动条，但允许滚动 */

.element {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.element::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 1); // 白色背景
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;


  .loading-spinner {
    font-size: 2rem !important; // 调整图标大小;
    color: #0f99ed;
  }
}
</style>
