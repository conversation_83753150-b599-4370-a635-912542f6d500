import request from "@/utils/requestTemp";
import qs from "qs";

// 某某区各镇联络站情况-各级⼈⼤代表驻站⼈数
export async function getLiaisonStation(data, token) {
  return request({
    url: "cockpit/getLiaisonStation",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
      "Authorization": `Bearer ${token}`,  // 添加 Authorization 头部携带 Token
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
  });
}

// 某某区各镇联络站情况-联络站⽇历
export async function getLiaisonStationCalendar(data, token) {
  return request({
    url: "cockpit/getLiaisonStationCalendar",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

export async function selectByMap(data, token) {
  return request({
    url: "liaisonStationNotice/selectByMap",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}


// 某某区各镇联络站情况-各级⼈⼤代表驻站⼈数
export async function getLiaisonStationDbCount(data, token) {
  return request({
    url: "cockpit/getLiaisonStationDbCount",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 某某区各镇联络站情况-个⼈代表明细
export async function getLiaisonStationDbInfo(data, token) {
  return request({
    url: "cockpit/getLiaisonStationDbInfo",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 某某区各镇联络站情况-驻站代表
export async function getLiaisonStationDb(data, token) {
  return request({
    url: "liaisonStationDb/list",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 某某区各镇联络站情况-活动公告详情
export async function getLiaisonStationDetails(id) {
  return request({
    url: `cockpit/getLiaisonStationDetails/`+id,  // 使用模板字符串正确拼接URL
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    }
  });
}

// 某某区各镇联络站情况-联络站基础信息
export async function getLiaisonStationInfo(data, token) {
  return request({
    url: "cockpit/getLiaisonStationInfo",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 某某区各镇联络站情况-联络站列表
export async function getLiaisonStationList(data, token) {
  return request({
    url: "liaisonStation/list",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 某某区各镇联络站情况-联络站列表
export async function getLiaisonStationListByJSC(data, token) {
  return request({
    url: "cockpit/liaisonStationList",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 某某区各镇联络站情况-活动公告列表
export async function getLiaisonStationNoticeList(data, token) {
  return request({
    url: "cockpit/getLiaisonStationNoticeList",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 某某区各镇联络站情况-意⻅征集
export async function getYjzjListexport(data, token) {
  return request({
    url: "cockpit/getYjzjListexport",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}
// 民意处理情况详情列表
export async function getMylist(data, token) {
  return request({
    url: "station/getMylist",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 获取联络站详情活动公告
export async function getLiaisonStationNoticeJSC(data, token) {
  return request({
    url: "cockpit/selectListByUserId",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 获取联络站详情活动公告
export async function getLiaisonStationNotice(data, token) {
  return request({
    url: "liaisonStationNotice/selectListByUserId",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 意见征集
export async function collectionOfOpinions(data, token) {
  return request({
    url: "cockpit/collectionOfOpinions",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}
export async function getSurveyList(data, token) {
  return request({
    url: "survey/survey/list",  // 更新为目标接口路径
    method: "get",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}
