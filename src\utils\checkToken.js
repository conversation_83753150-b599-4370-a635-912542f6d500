import store from "../stores";
// 判断选择的 token
export function checkToken() {
  // 首先尝试从 localStorage 获取 pro__token
  const proToken = localStorage.getItem('pro__token');
  // console.log(localStorage.getItem('pro__token'))
  console.log(proToken)
  if (proToken) {
    const rdtoken = JSON.parse(proToken);
    return rdtoken.value;  // 返回 token
  } else {
    // 如果没有，从 Vuex store 中获取 accessToken
    return store.getters.accessToken;  // 假设你使用 Vuex 管理状态
  }
}
