/**
 * async-validator 验证封装
 */
import { iShowNumber, isPhone } from "@/utils/validate";

/**
 * async-validator 验证包装
 * @param test{Function}  验证是否通过
 * @param title{String} 验证字段标题
 * @param ruleMessage{String=} 验证不通过说明
 *
 * 示例：const rules = {
 *   message 需要为空，否则提示无法覆盖
 *   phone: { required: true,, validator: wrapValidator(isPhone, "手机号") }
 * }
 */
export function wrapValidator(test, title, ruleMessage) {
  return (rule, value, callback) => {
    // 非必填
    if (
      !rule.required &&
      (value == null ||
        value == undefined ||
        (Array.isArray(value) && !value.length) ||
        (typeof value === "string" && !value))
    ) {
      callback();
      return;
    }

    const message = `${title}不能为空`;
    const ruleMsg = ruleMessage || `无效的${title}`;

    const msg =
      rule.message && typeof rule.message === "string" ? rule.message : message;

    // 必填报错
    if (!value) {
      callback(msg);
      return;
    }

    // 规则校验不通过
    if (!test(value)) {
      callback(ruleMsg || msg);
      return;
    }
    callback();
  };
}

export function phoneValidator(title, ruleMessage) {
  return wrapValidator(isPhone, title || "手机号", ruleMessage);
}

export function showNumberValidator(title, ruleMessage) {
  return wrapValidator(iShowNumber, title, ruleMessage);
}
