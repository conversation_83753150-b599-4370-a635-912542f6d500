<template>
  <div class="liaisonStation-page">
    <div class="liaisonStation-main_center">
      <div class="center-calendar">
        <ScmpCard :cardName="proposalData.proposalTitle" backgroundImage="card_bg2" :rightPicture="false">
          <div slot="main" class="con-bottom-box" v-loading="loading" element-loading-text="数据加载中..."
            element-loading-background="rgba(0, 0, 0, 0.5)">
            <div class="header">
              <div class="btn" @click="$router.go(-1)">返回</div>
            </div>

            <!-- 议案建议信息展示区域 -->
            <div class="proposal-container">
              <!-- 标题区域 -->
              <div class="proposal-header">
                <h2>建议号：{{ proposalData.proposalNum }}</h2>
                <div class="status-tag">
                  <el-tag :type="getStatusTagType(proposalData.status)">{{ getStatusText(proposalData.status)
                    }}</el-tag>
                </div>
              </div>

              <!-- 基本信息区域 -->
              <div class="basic-info">
                <el-descriptions :column="2" border>
                  <!-- 第一行 -->
                  <el-descriptions-item label="届次">
                    {{ proposalData.periodDesc || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="议案建议类型">
                    {{ filterProposalType(proposalData.proposalType) }}
                  </el-descriptions-item>

                  <!-- 第二行 -->
                  <el-descriptions-item label="内容所属类别">
                    {{ filterProposalContentType(proposalData.proposalContentType) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="领衔代表">
                    {{ proposalData.headPer || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第三行 -->
                  <el-descriptions-item label="所在代表团">
                    {{ proposalData.deputyOrg || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="代表本人是否同意公开">
                    {{ proposalData.ifPublice == "0" ? "不" :
                      proposalData.ifPublice == "1" ? "是" :
                        proposalData.ifPublice == "2" ? "已公开" : "暂无数据" }}
                  </el-descriptions-item>

                  <!-- 第四行 -->
                  <el-descriptions-item label="代表建议答复方式">
                    {{ proposalData.codeHaveOrNo == "1" ? "书面" :
                      proposalData.codeHaveOrNo == "2" ? "网上" :
                        proposalData.codeHaveOrNo == "0" ? "只做工作参考用，无需正式答复" : "暂无数据" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="代表与承办单位沟通方式">
                    {{ proposalData.ifContect == "1" ? "需要见面座谈和调研" :
                      proposalData.ifContect == "0" ? "只需电话微信沟通" :
                        proposalData.ifContect == "2" ? "不需要沟通，直接答复" :
                          proposalData.ifContect == "3" ? "工作参考用，不需要正式书面答复" : "暂无数据" }}
                  </el-descriptions-item>

                  <!-- 第五行 -->
                  <el-descriptions-item label="多年多次提出、尚未解决">
                    {{ proposalData.overTheYearsNotResolved == "1" ? "是" :
                      proposalData.overTheYearsNotResolved == "0" ? "否" :
                        proposalData.overTheYearsNotResolved == "2" ? "未详" : "暂无数据" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="意向承办单位" :span="1">
                    {{ proposalData.intentOrg || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第六行 -->
                  <el-descriptions-item label="当前经办人" :span="2">
                    <template v-if="proposalData.yajyOrgList && proposalData.yajyOrgList.length > 0">
                      <span v-for="(item, index) in proposalData.yajyOrgList" :key="index">
                        {{ item.orgName + "(" + item.orgDispname + item.orgDispphone + "); " }}
                      </span>
                    </template>
                    <template v-if="proposalData.yajyOrgOperDTOList && proposalData.yajyOrgOperDTOList.length > 0">
                      <span v-if="proposalData.yajyOrgOperDTOList[0].orgCode == '387'">
                        {{ proposalData.yajyOrgOperDTOList[0].orgName + "(" + proposalData.yajyOrgOperDTOList[0].telNo +
                          ")" }}
                        <br />
                      </span>
                      <span v-else>
                        <span v-for="(item, index) in proposalData.yajyOrgOperDTOList" :key="index">
                          {{ item.operName + "(" + item.telNo + ")" }}
                        </span>
                      </span>
                    </template>
                    <template
                      v-if="proposalData.identityAllocationList && proposalData.identityAllocationList.length > 0">
                      <span v-for="(item, index) in proposalData.identityAllocationList" :key="index">
                        {{ item.orgName + "(" + item.userName + item.mobile + "); " }}
                      </span>
                    </template>
                    <template v-if="!(proposalData.yajyOrgList && proposalData.yajyOrgList.length > 0) &&
                      !(proposalData.yajyOrgOperDTOList && proposalData.yajyOrgOperDTOList.length > 0) &&
                      !(proposalData.identityAllocationList && proposalData.identityAllocationList.length > 0)">
                      暂无数据
                    </template>
                  </el-descriptions-item>

                  <!-- 第七行 -->
                  <el-descriptions-item label="交办日期">
                    {{ proposalData.handleDate || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="办理期限至">
                    主办：{{ majorLastTime ? majorLastTime.substring(0, 10) : '暂无数据' }}
                    <br>
                    会办：{{ minorLastTime ? minorLastTime.substring(0, 10) : '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第八行 -->
                  <el-descriptions-item label="办理情况">
                    {{ proposalData.proposalHandle || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="代表工委是否公开意见">
                    {{ proposalData.xlwPublic == "0" || proposalData.xlwPublic == null ? "未提出意见" :
                      proposalData.xlwPublic == "1" ? "建议公开" :
                        proposalData.xlwPublic == "2" ? "建议不公开" : "暂无数据" }}
                  </el-descriptions-item>

                  <!-- 第九行 -->
                  <el-descriptions-item label="研究室建议是否公开">
                    {{ proposalData.yjsPublic == "0" || proposalData.yjsPublic == null ? "未提出意见" :
                      proposalData.yjsPublic == "1" ? "建议公开" :
                        proposalData.yjsPublic == "2" ? "建议不公开" : "暂无数据" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="建议审核是否公开发布">
                    {{ proposalData.examineXlgw == "0" ? "未审核" :
                      proposalData.examineXlgw == "1" ? "审核公开" :
                        proposalData.examineXlgw == "2" ? "审核不公开" : "暂无数据" }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 操作按钮区域 -->
              <!-- <div class="action-buttons">
                <el-button type="primary" @click="downloadProposal">下载建议纸</el-button>
                <el-button @click="viewContent">查看正文</el-button>
                <el-button @click="uploadAttachment">重新上传正文附件</el-button>
              </div> -->
            </div>
          </div>
        </ScmpCard>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getById,
  getByQuId
} from "@/api/dataPageApi/motion";
export default {
  name: "ProposalDetail",
  components: {
    ScmpCard: () => import("@/comps/scmp-YAJYCard"),
    ScmpTable: () => import("@/comps/scmp-table"),
    ScmpPagination: () => import("@/components/pagination/index.vue"),
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      dataList: [],
      dataColum: [],
      listTitle: '',
      majorLastTime: '', // 示例数据
      minorLastTime: '', // 示例数据
      proposalData: {},
      loading: false,
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.dataList.slice(start, end);
    }
  },
  created() {
    if (this.$route.query.title) {
      this.listTitle = this.$route.query.title;
    }
    if (this.$route.query.item) {
      this.dataList = JSON.parse(this.$route.query.item);
    }
    if (this.$route.query.colum) {
      this.dataColum = JSON.parse(this.$route.query.colum);
    }

    // 判断是否是区级
    if (this.$route.query.areaName) {
      this.getByQuId(this.$route.query.proposalId)
    } else {
      this.getById(this.$route.query.proposalId)

    }
  },
  mounted() {
  },
  methods: {
    getById(id) {
      this.loading = true; // 开始加载，显示转圈
      const params = {
        proposalId: id
      }
      getById({ proposalId: id }).then(res => {
        this.proposalData = res.data
      })
        .catch(error => {
          console.error('请求失败', error);
          this.$message.error('数据加载失败');
        })
        .finally(() => {
          this.loading = false; // 加载完成，隐藏转圈
        });
    },
    getByQuId(id) {
      this.loading = true; // 开始加载，显示转圈
      const params = {
        proposalId: id
      }
      getByQuId({ proposalId: id }).then(res => {
        this.proposalData = res.data
        //格式化时间 交办日期
        if (this.proposalData.handleDate) {
          this.proposalData.handleDate = this.proposalData.handleDate.substring(0, 10);
        }
      })
        .catch(error => {
          console.error('请求失败', error);
          this.$message.error('数据加载失败');
        })
        .finally(() => {
          this.loading = false; // 加载完成，隐藏转圈
        });
    },
    filterProposalType(type) {
      const typeMap = {
        '1': '大会议案',
        '2': '大会建议',
        '3': '闭会建议',
        '4': '供参考建议'
      };
      return typeMap[type] || '暂无数据';
    },
    filterProposalContentType(type) {
      const typeMap = {
        "JYFL01": "法制",
        "JYFL02": "监察和司法",
        "JYFL09": "预算",
        "JYFL03": "经济",
        "JYFL04": "城建环资",
        "JYFL05": "农村农业",
        "JYFL06": "教科文卫",
        "JYFL07": "华侨外事民族宗教",
        "JYFL08": "其他",
        "JYFL10": "社会建设"
      };
      return typeMap[type] || '暂无数据';
    },
    getStatusText(status) {
      // 根据当前会议类型选择对应的状态映射
      const isMeeting = this.$route.query.isMeeting;

      if (isMeeting == 0) {
        // 闭会期间状态映射
        const statusMapClosed = {
          10: '提交',
          20: '校核',
          22: '分类并初审',
          30: '复审',
          33: '研究室意见',
          39: '领导审定',
          40: '分办',
          50: '签收',
          60: '答复',
          70: '评价',
          90: '办结',
        };
        return statusMapClosed[status] || '未知状态';
      } else {
        // 大会期间状态映射
        const statusMap = {
          10: '提交',
          20: '校核',
          21: '预分类',
          22: '初审',
          25: '复审',
          30: '复审',
          40: '分办',
          41: '不予立案审核',
          42: '分办审核中',
          43: '分办复核中',
          45: '不予立案确认',
          50: '签收',
          60: '答复',
          70: '评价',
          90: '办结',
        };
        return statusMap[status] || '未知状态';
      }
    },
    getStatusTagType(status) {
      // 根据状态返回对应的标签类型
      const typeMap = {
        10: 'info',     // 提交
        20: 'warning',  // 校核
        21: 'warning',  // 预分类
        22: 'warning',  // 初审/分类并初审
        25: 'warning',  // 复审
        30: 'warning',  // 复审
        33: 'warning',  // 研究室意见
        39: 'warning',  // 领导审定
        40: 'warning',  // 分办
        41: 'warning',  // 不予立案审核
        42: 'warning',  // 分办审核中
        43: 'warning',  // 分办复核中
        45: 'warning',  // 不予立案确认
        50: 'warning',  // 签收
        60: 'primary',  // 答复
        70: 'warning',  // 评价
        90: 'success',  // 办结
      };
      return typeMap[status] || 'info';
    },
    downloadAttachment(attachment) {
      console.log('下载附件:', attachment);
      this.$message.success(`开始下载附件: ${attachment.attName}`);
    },
    previewAttachment(attachment) {
      console.log('预览附件:', attachment);
      this.$message.info(`预览附件: ${attachment.attName}`);
    },
    downloadProposal() {
      console.log('下载建议纸');
      this.$message.success('开始下载建议纸');
    },
    viewContent() {
      console.log('查看正文');
      this.$message.info('查看正文');
    },
    uploadAttachment() {
      console.log('上传附件');
      this.$message.info('打开上传附件对话框');
    },
    handleCurrentChange(val) {
      console.log('当前页:', val);
    },
    handleSizeChange(val) {
      console.log('每页条数:', val);
    }
  }
};
</script>

<style lang="less" scoped>
.liaisonStation-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .header {
    position: absolute;
    right: 60px;
    top: 40px;

    .btn {
      padding: 10px 38px;
      font-family: PingFang SC, PingFang SC;
      /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/
      font-weight: 600;
      font-size: 40px;
      color: #ffffff;
      line-height: 47px;
      letter-spacing: 4px;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
      border-radius: 6px 6px 6px 6px;
      height: 56px;
      cursor: pointer;
    }
  }

  .liaisonStation-main_center {
    flex: 1;
    margin-left: -200px;

    .center-calendar {
      height: 1430px;
      margin-top: 25px;
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      padding-left: 178px;
    }
  }
}

.liaisonStation-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
}

.con-bottom-box {
  height: 1300px;
  padding: 40px 80px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 议案建议信息样式 */
.proposal-container {
  flex: 1;
  overflow-y: auto;
  color: #fff;

  /* Webkit浏览器滚动条样式 */
  &::-webkit-scrollbar {
    height: 8px; // 滚动条高度
    background: rgba(0, 25, 63, 0.2); // 轨道背景
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg,
        rgba(0, 25, 63, 0) 0%,
        rgba(10, 108, 222, 0.8) 50%,
        rgba(0, 25, 63, 0) 100%);
    border-radius: 4px;
    border: 1px solid rgba(10, 108, 222, 0.5); // 添加边框增强科技感
    /*box-shadow: 0 0 5px rgba(78, 153, 248, 0.5); // 发光效果*/
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg,
        rgba(0, 25, 63, 0) 0%,
        rgba(10, 150, 255, 0.9) 50%,
        rgba(0, 25, 63, 0) 100%);
  }

  .proposal-header {
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
    margin-bottom: 30px;

    h2 {
      margin: 0;
      font-size: 48px;
      color: #fff;
      font-weight: 500;
    }

    .status-tag {
      margin-left: 40px;

      .el-tag {
        font-size: 36px;
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
      }
    }
  }

  .basic-info {
    margin-bottom: 30px;

    ::v-deep .el-descriptions {
      background: rgba(0, 0, 0, 0.2);

      .el-descriptions__header {
        display: none;
      }

      .el-descriptions-item__label {
        color: #7edcfb;
        font-size: 32px;
        height: 100px;
      }

      .el-descriptions-item__content {
        color: #fff;
        font-size: 32px;
      }

      .el-descriptions__body {
        background: transparent;
      }

      .el-descriptions__table {
        border: 1px solid rgba(126, 220, 251, 0.3);
      }

      .el-descriptions__table td,
      .el-descriptions__table th {
        border-bottom: 1px solid rgba(126, 220, 251, 0.3);
        border-right: 1px solid rgba(126, 220, 251, 0.3);
      }
    }
  }

  .status-info {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(126, 220, 251, 0.3);
    border-radius: 4px;

    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 32px;
      color: #fff;
    }

    .status-item {
      margin-bottom: 15px;
      font-size: 28px;

      label {
        color: #7edcfb;
        margin-right: 15px;
      }

      span {
        color: #fff;
      }
    }
  }

  .attachment-area {
    margin-bottom: 30px;

    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 32px;
      color: #fff;
    }

    ::v-deep .el-table {
      background: transparent;
      color: #fff;
      font-size: 26px;

      th {
        background: rgba(90, 211, 251, 0.3) !important;
        color: #fff;
        font-size: 28px;
      }

      tr {
        background: rgba(0, 0, 0, 0.2) !important;
      }

      td {
        border-bottom: 1px solid rgba(126, 220, 251, 0.3);
      }

      .el-table__empty-block {
        background: rgba(0, 0, 0, 0.2);
      }

      .el-link {
        font-size: 26px;
      }

      .el-button {
        font-size: 24px;
        padding: 8px 12px;
      }
    }
  }

  .action-buttons {
    text-align: center;
    margin-top: 40px;

    .el-button {
      margin: 0 20px;
      font-size: 28px;
      padding: 12px 30px;

      &--primary {
        background: rgba(10, 255, 255, 0.21);
        border-color: #33fefe;
        color: #33fefe;
      }

      &--default {
        background: rgba(57, 205, 255, 0.12);
        border-color: #7edcfb;
        color: #fff;
      }
    }
  }
}

.basic-info {
  ::v-deep .el-descriptions {
    .el-descriptions-item__label {
      background: rgba(90, 211, 251, 0.2);
      color: #7edcfb;
      font-weight: bold;
      text-align: center;
      width: 600px;
      min-width: 300px;
    }

    .el-descriptions-item__content {
      background: rgba(16, 42, 66, 0.4);
      color: #fff;
      padding-left: 15px;
    }

    .el-descriptions__body {
      background: rgba(16, 42, 66, 0.6);
    }

    .el-descriptions__table {
      border: 1px solid rgba(126, 220, 251, 0.5);
    }

    td,
    th {
      border-color: rgba(126, 220, 251, 0.3) !important;
    }
  }
}
</style>
