{"name": "data-cockpit", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "babel-eslint": "^10.1.0", "core-js": "^3.8.3", "data-cockpit": "file:", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-ui": "^2.15.14", "fastclick": "^1.0.6", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "lodash": "4.17.15", "mockjs": "^1.1.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "pdfobject": "^2.3.1", "qrcode": "^1.5.4", "style-loader": "4.0.0", "vue": "^2.6.14", "vue-fragment": "^1.5.1", "vue-pdf": "^4.3.0", "vue-pdf-embed": "^1.2.1", "vue-qrcode": "^2.2.2", "vue-router": "^3.6.5", "vuex": "^3.6.2", "vuex-router-sync": "^5.0.0", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "figlet": "^1.5.0", "globby": "^11.0.2", "less": "^4.1.0", "less-loader": "^7.3.0", "prettier": "^2.2.1", "stylelint": "13.2.x", "stylelint-config-css-modules": "2.2.x", "stylelint-config-prettier": "8.0.x", "stylelint-config-recess-order": "2.0.x", "stylelint-config-standard": "20.0.x", "vue-template-compiler": "^2.6.14", "worker-loader": "^3.0.8"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}