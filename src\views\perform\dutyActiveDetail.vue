<template>
  <div class="liaisonStation-page">
    <div class="liaisonStation-main_center">
      <div class="center-calendar">
        <ScmpCard cardName="履职活动详情" backgroundImage="card_bg2" :rightPicture="false">
          <div slot="main" class="con-bottom-box" v-loading="loading" element-loading-text="数据加载中..."
            element-loading-background="rgba(0, 0, 0, 0.5)">
            <div class="header">
              <div class="btn" @click="$router.go(-1)">返回</div>
            </div>

            <!-- 履职活动信息展示区域 -->
            <div class="proposal-container">
              <!-- 基本信息区域 -->
              <div class="basic-info">
                <el-descriptions :column="2" border>
                  <!-- 第一行 -->
                  <el-descriptions-item label="活动名称">
                    {{ dutyActiveData.activityName || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="活动编号">
                    {{ dutyActiveData.activityNo || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第二行 -->
                  <el-descriptions-item label="活动地点">
                    {{ dutyActiveData.address || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="当前状态">
                    {{ dutyActiveData.currentState?.currStateName || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第三行 -->
                  <el-descriptions-item label="活动性质">
                    {{ dutyActiveData.activityNatureName || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="活动类型">
                    {{ dutyActiveData.activityTypeName || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第四行 -->
                  <el-descriptions-item label="活动开始时间">
                    {{ dutyActiveData.startTime || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="活动结束时间">
                    {{ dutyActiveData.endTime || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第五行 -->
                  <el-descriptions-item label="组织单位">
                    {{ dutyActiveData.orgName || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="参与人数">
                    {{ dutyActiveData.attendMembers ? dutyActiveData.attendMembers.length : 0 }} 人
                  </el-descriptions-item>

                  <!-- 第六行 -->
                  <el-descriptions-item label="创建时间">
                    {{ dutyActiveData.createTime || '暂无数据' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="创建人">
                    {{ dutyActiveData.creatorName || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第七行 -->
                  <!-- <el-descriptions-item label="工作人员" :span="2">
                    {{ dutyActiveData.workerDesc || '暂无数据' }}
                  </el-descriptions-item> -->

                  <!-- 第八行 -->
                  <el-descriptions-item label="活动内容" :span="2">
                    {{ dutyActiveData.activityContent || '暂无数据' }}
                  </el-descriptions-item>

                  <!-- 第九行 - 邀请范围描述 -->
                  <!-- <el-descriptions-item label="邀请范围" :span="2">
                    {{ dutyActiveData.inviteRangeDesc || '暂无数据' }}
                  </el-descriptions-item> -->
                </el-descriptions>
              </div>

              <!-- 节次信息表格 -->
              <!-- <div class="attachment-area" v-if="dutyActiveData.dutySections && dutyActiveData.dutySections.length > 0">
                <h3>节次信息</h3>
                <el-table :data="dutyActiveData.dutySections" border>
                  <el-table-column prop="sectionName" label="节次名称" />
                  <el-table-column prop="sectionContent" label="节次内容" />
                  <el-table-column prop="startTime" label="开始时间" />
                  <el-table-column prop="endTime" label="结束时间" />
                  <el-table-column prop="duration" label="持续时间" />
                </el-table>
              </div> -->

              <!-- 参会人员表格 -->
              <!-- <div class="attachment-area" v-if="dutyActiveData.attendMembers && dutyActiveData.attendMembers.length > 0">
                <h3>参会人员</h3>
                <el-table :data="dutyActiveData.attendMembers" border>
                  <el-table-column prop="memberName" label="姓名" />
                  <el-table-column prop="memberType" label="人员类型" />
                  <el-table-column prop="position" label="职务" />
                  <el-table-column prop="phone" label="联系电话" />
                  <el-table-column prop="unit" label="单位" />
                </el-table>
              </div> -->
            </div>
          </div>
        </ScmpCard>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getDutyActiveById } from "@/api/dataPageApi/perform";

export default {
  name: "DutyActiveDetail",
  components: {
    ScmpCard: () => import("@/comps/scmp-YAJYCard"),
  },
  data() {
    return {
      dutyActiveData: {},
      loading: false,
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    const id = this.$route.query.id;
    if (id) {
      this.fetchDutyActiveData(id);
    }
  },
  methods: {
    // 获取履职活动详情
    fetchDutyActiveData(id) {
      this.loading = true; // 开始加载，显示加载中
      getDutyActiveById(id)
        .then(res => {
          console.log('活动详情:', res);
          if (res.code === '0000') {
            // 将获取的数据填充到展示区域
            this.dutyActiveData = res.data || {};
          } else {
            this.$message.error(res.msg || '获取活动详情失败');
          }
        })
        .catch(error => {
          console.error('获取活动详情失败:', error);
          this.$message.error('获取活动详情失败');
        })
        .finally(() => {
          this.loading = false; // 结束加载
        });
    }
  }
};
</script>

<style lang="less" scoped>
.liaisonStation-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;

  .header {
    position: absolute;
    right: 60px;
    top: 40px;

    .btn {
      padding: 10px 38px;
      font-family: PingFang SC, PingFang SC;
      /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/
      font-weight: 600;
      font-size: 40px;
      color: #ffffff;
      line-height: 47px;
      letter-spacing: 4px;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg, #055cff 0%, #062e78 100%);
      border-radius: 6px 6px 6px 6px;
      height: 56px;
      cursor: pointer;
    }
  }

  .liaisonStation-main_center {
    flex: 1;
    margin-left: -200px;

    .center-calendar {
      height: 1430px;
      margin-top: 25px;
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      padding-left: 178px;
    }
  }
}

.liaisonStation-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
}

.con-bottom-box {
  height: 1300px;
  padding: 40px 80px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 履职活动信息样式 */
.proposal-container {
  flex: 1;
  overflow: hidden;
  color: #fff;

  .proposal-header {
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
    margin-bottom: 30px;

    h2 {
      margin: 0;
      font-size: 36px;
      color: #fff;
      font-weight: 500;
    }

    .status-tag {
      margin-left: 40px;

      .el-tag {
        font-size: 28px;
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
      }
    }
  }

  .basic-info {
    margin-bottom: 30px;

    ::v-deep .el-descriptions {
      background: rgba(0, 0, 0, 0.2);

      .el-descriptions__header {
        display: none;
      }

      .el-descriptions-item__label {
        color: #7edcfb;
        font-size: 32px;
        height: 150px;
      }

      .el-descriptions-item__content {
        color: #fff;
        font-size: 32px;
      }

      .el-descriptions__body {
        background: transparent;
      }

      .el-descriptions__table {
        border: 1px solid rgba(126, 220, 251, 0.3);
      }

      .el-descriptions__table td,
      .el-descriptions__table th {
        border-bottom: 1px solid rgba(126, 220, 251, 0.3);
        border-right: 1px solid rgba(126, 220, 251, 0.3);
      }
    }
  }

  .attachment-area {
    margin-bottom: 30px;

    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 32px;
      color: #fff;
    }

    ::v-deep .el-table {
      background: transparent;
      color: #fff;
      font-size: 26px;

      th {
        background: rgba(90, 211, 251, 0.3) !important;
        color: #fff;
        font-size: 28px;
      }

      tr {
        background: rgba(0, 0, 0, 0.2) !important;
      }

      td {
        border-bottom: 1px solid rgba(126, 220, 251, 0.3);
      }

      .el-table__empty-block {
        background: rgba(0, 0, 0, 0.2);
      }

      .el-link {
        font-size: 26px;
      }

      .el-button {
        font-size: 24px;
        padding: 8px 12px;
      }
    }
  }
}

.basic-info {
  ::v-deep .el-descriptions {
    .el-descriptions-item__label {
      background: rgba(90, 211, 251, 0.2);
      color: #7edcfb;
      font-weight: bold;
      text-align: center;
      font-size: 36px; 
      padding: 24px 32px; // 增加内边距
    }

    .el-descriptions-item__content {
      background: rgba(16, 42, 66, 0.4);
      color: #fff;
      padding-left: 15px;
    }

    .el-descriptions__body {
      background: rgba(16, 42, 66, 0.6);
      font-size: 36px; 
      padding: 24px 32px; // 增加内边距
      line-height: 1.8; // 增加行高
    }

    .el-descriptions__table {
      border: 1px solid rgba(126, 220, 251, 0.5);
    }

    td,
    th {
      border-color: rgba(126, 220, 251, 0.3) !important;
      border-width: 2px; // 加粗边框
      padding: 32px; // 单元格内边距增大
      min-width: 400px; // 最小列宽增加
    }

    .el-descriptions__body {
      font-size: 40px;  // 整体字号增大
      padding: 30px 40px;  // 容器内边距增大
    }
  }
}
</style>
