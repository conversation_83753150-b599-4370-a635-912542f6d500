<template>
    <el-pagination class="scmp-pagination" background layout="total, prev, pager, next,jumper" :total="total"
      :current-page="innerCurrentPage" :page-size="innerPageSize" @current-change="handleCurrentChange"
      @size-change="handleSizeChange">
    </el-pagination>
</template>

<script>
export default {
  name: 'ScmpPagination',
  props: {
    total: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 10
    },
    currentPage: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      innerCurrentPage: this.currentPage,
      innerPageSize: this.pageSize
    }
  },
  watch: {
    currentPage: {
      handler(val) {
        this.innerCurrentPage = val
      },
      immediate: true
    },
    pageSize: {
      handler(val) {
        this.innerPageSize = val
      },
      immediate: true
    }
  },
  methods: {
    handleCurrentChange(val) {
      console.log(val)
      this.innerCurrentPage = val
      this.$emit('current-change', val)
      this.$emit('update:currentPage', val)
    },
    handleSizeChange(val) {
      console.log(val)
      this.innerPageSize = val
      this.$emit('size-change', val)
      this.$emit('update:pageSize', val)
    }
  }
}
</script>

<style lang="less">
.scmp-pagination.el-pagination {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin: 20px 15px;
  font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
  /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/


  .el-pagination__total {
    font-size: 36px; // 增大总条数字体
    color: rgb(55, 147, 175);
    font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
    /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/


    margin-right: 20px;

    span {
      font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
      /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/



      font-weight: 300;
    }
  }

  .el-pagination__jump {
    font-size: 36px; // 增大跳转字体
    color: #fff;
    display: flex;
    align-items: center;
    font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
    /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/

    margin-left: 50px;

    .el-pagination__goto {
      font-size: 36px; // 增大跳转输入框字体
      color: #fff;
      margin-right: 12px;
    }

    .el-input {
      height: 48px;
      margin: 0 12px;
      width: 80px;
      vertical-align: middle;

      .el-input__inner {
        background: rgba(9, 31, 51, 0.8);
        border: 1px solid rgba(0, 147, 255, 0.6);
        color: #fff;
        height: 48px;
        font-size: 36px; // 增大输入框字体
        text-align: center;
        box-shadow: none;
        font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
        /*font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;*/


        font-weight: 500;
        padding: 0 15px;
      }
    }
  }

  button {
    background: rgba(9, 31, 51, 0.8);
    border: 1px solid rgba(0, 147, 255, 0.95);
    color: #fff;
    font-size: 28px; // 增大按钮字体

    &:hover {
      color: #fff;
      background: rgba(9, 31, 51, 0.8);
    }
  }

  .btn-prev,
  .btn-next {
    height: 48px;
    width: 48px;
    background: rgba(9, 31, 51, 0.8);
    border: 1px solid rgba(0, 147, 255, 0.6);
    margin: 0 12px;
    box-shadow: none;

    .el-icon {
      font-size: 30px;
      color: #fff;
    }

    &:disabled {
      background: rgba(9, 31, 51, 0.4);

      .el-icon {
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }

  .el-pager {
    display: flex;
    align-items: center;
    background: transparent;

    li {
      font-size: 36px; // 增大页码字体
      height: 48px;
      min-width: 48px;
      line-height: 48px;
      background: rgba(9, 31, 51, 0.8);
      border: 1px solid rgba(0, 147, 255, 0.95);
      border-bottom-width: 2px;
      color: #fff;
      margin: 0 12px;
      padding: 0 15px;
      font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
      font-weight: 500;
      box-shadow: none;

      &:hover {
        color: #fff;
      }

      &.active {
        background: linear-gradient(91deg, rgb(58, 205, 250) 0%, rgb(58, 205, 250) 49%, rgb(58, 205, 250) 100%);
        color: #fff;
        border: 1px solid rgb(58, 205, 250);
        border-bottom-width: 2px;
        box-shadow: none;
        text-shadow: none;
      }

      &.more {
        font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
        font-weight: 500;

        &:hover {
          color: #fff;
        }
      }

      &.btn-quicknext,
      &.btn-quickprev {
        position: relative;
        font-size: 22px;
        height: 48px;
        line-height: 48px;
        background: rgba(9, 31, 51, 0.8);
        border: 1px solid rgba(0, 147, 255, 0.95);
        border-bottom-width: 2px;
        color: #fff;
        margin: 0 12px;
        padding: 0 15px;
        box-shadow: none;
        font-family: MoMenZhengDaoBiaoTiTi, "MoMen ZhengDao BiaoTi";
        font-weight: 500;
        cursor: default;
        transition: all 0.3s;
        overflow: hidden;

        &:hover {
          color: #fff;
          background: rgba(9, 31, 51, 0.8);
          border-color: rgba(0, 147, 255, 0.95);
        }

        &::before {
          content: "...";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          line-height: 48px;
          text-align: center;
        }
      }
    }
  }
}
</style> 