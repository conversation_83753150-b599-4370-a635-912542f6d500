<template>
  <div class="motion-page">
    <div class="motion-main_left">
      <ScmpCard cardName="建议涉及分类">
        <div slot="main" class="motion-main_left_warp">
          <div class="main_left_main">
            <div
              class="item"
              v-for="(item, index) in progressBarList"
              :key="index"
            >
              <div class="label">{{ item.label }}</div>
              <div class="progress-bar">
                <div class="bar" :style="`width:${item.number}%`"></div>
                <div
                  class="cursor"
                  :style="`left:calc(${item.number}% - 21px)`"
                >
                  <div class="sub"></div>
                </div>
              </div>
              <div class="number">{{ item.number }}</div>
            </div>
          </div>
          <div class="wordCloud-warp">
            <div class="wordCloud-warp_bg"></div>
            <wordCloud class="wordCloud-warp_main" :data="wordCloud">
            </wordCloud>
          </div>
        </div>
      </ScmpCard>
    </div>

    <div class="motion-main_center">
      <div style="width:100%;height:1800px">
         <MapChart style="width:100%;height:1700px;"></MapChart>
      </div>
     
    </div>

    <div class="motion-main_right">
      <ScmpCard cardName="重点督办建议" pictureMode="2">
        <div slot="main">
          <ScmpTable
            :tableData="deptDictList"
            :tableColumn="deptDictTableColumn"
            :rowNum="11"
            :indicesGrouping="true"
          ></ScmpTable>
        </div>
      </ScmpCard>
      <ScmpCard
        cardName="分办单位承办情况"
        pictureMode="2"
        style="margin-top: 44px"
      >
        <div slot="main" class="four-cards">
          <div
            class="single-card"
            v-for="(item, index) in singleCardList"
            :key="index"
          >
            <div class="title">{{ item.name }}</div>
            <div class="rate">
              <span>{{ item.rate }}</span
              >%
            </div>
          </div>
        </div>
      </ScmpCard>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "motion-index",
  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    ScmpTable: () => import("@/comps/scmp-table"),
    wordCloud: () => import("@/components/motion/wordCloud.vue"),
    MapChart: () => import("@/components/dome/map2D.vue"),
  },
  data() {
    return {
      // Table配置
      deptDictList: [],
      deptDictTableColumn: [
        { prop: "sort", label: "序号", align: "center" },
        { prop: "deptName", label: "建议号" },
        { prop: "catalogNum", label: "标题" },
        { prop: "mountRate", label: "状态" },
      ],
      // 词云图数据
      wordCloud: [
        {
          name: "城建环资类(75)",
          value: 75,
        },
        {
          name: "法制类(4)",
          value: 4,
        },
        {
          name: "其他类(4)",
          value: 4,
        },
        {
          name: "预算类(32)",
          value: 32,
        },
        {
          name: "农村农业类(2)",
          value: 2,
        },
        {
          name: "社会建设类(35)",
          value: 35,
        },
        {
          name: "监察司法类(75)",
          value: 75,
        },
        {
          name: "经济类(4)",
          value: 4,
        },
        {
          name: "预算类(35)",
          value: 35,
        },
        {
          name: "经济类(2)",
          value: 2,
        },
        {
          name: "教科文卫类(2)",
          value: 2,
        },
        {
          name: "体育类(4)",
          value: 4,
        },
        {
          name: "经济类(32)",
          value: 32,
        },
        {
          name: "经济类(55)",
          value: 55,
        },
        {
          name: "监察司法类(55)",
          value: 55,
        },
        {
          name: "城建环资类(75)",
          value: 75,
        },
        {
          name: "教科文卫类(92)",
          value: 92,
        },
        {
          name: "其他类(35)",
          value: 35,
        },
        {
          name: "侨务类(4)",
          value: 4,
        },
      ],
      // 进度条列表
      progressBarList: [
        { label: "法制", number: 97 },
        { label: "监察司法", number: 36 },
        { label: "预算", number: 54 },
        { label: "经济", number: 65 },
        { label: "城建环资撒", number: 84 },
        { label: "农村农业", number: 16 },
        { label: "教科文卫", number: 37 },
        { label: "侨务", number: 66 },
        { label: "社会建设", number: 72 },
        { label: "其他", number: 36 },
      ],
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [{ name: "菜单项1" }, { name: "菜单项2" }],
      // 中间内容
      tabActive: "1",
      // 三卡位
      threeCardList: [
        {
          name: "议案数",
          number: 1,
        },
        {
          name: "建议数",
          number: 101,
        },
        {
          name: "供参考建议",
          number: 56,
        },
      ],
      // 左一右四-四卡
      fourCalorieList: [
        {
          name: "待校核",
          number: 8,
        },
        {
          name: "待分类",
          number: 17,
        },
        {
          name: "待初审",
          number: 17,
        },
        {
          name: "待复审",
          number: 17,
        },
        {
          name: "待分办",
          number: 17,
        },
      ],
      // 步骤数据
      stepsList: [
        {
          name: "张三领衔提出“关于增加养老福利的建议”",
          stepObj: {
            active: 4,
            titleList: [
              "预提交",
              "核校",
              "分类",
              "初审",
              "复审",
              "分办",
              "签收",
              "答复",
              "评价",
              "已办结",
            ],
          },
        },
        {
          name: "张三领衔提出“关于增加养老福利的建议”",
          stepObj: {
            active: 5,
            titleList: [
              "预提交",
              "核校",
              "分类",
              "初审",
              "复审",
              "分办",
              "签收",
              "答复",
              "评价",
              "再答复",
              "再评价",
              "已办结",
            ],
          },
        },
        {
          name: "张三领衔提出“关于增加养老福利的建议”",
          stepObj: {
            active: 10,
            titleList: [
              "预提交",
              "核校",
              "分类",
              "初审",
              "复审",
              "分办",
              "签收",
              "答复",
              "评价",
              "已办结",
            ],
          },
        },
      ],
      // 右下方四卡位
      singleCardList: [
        {
          name: "已分办",
          rate: 100,
        },
        {
          name: "已签收",
          rate: 100,
        },
        {
          name: "已答复",
          rate: 100,
        },
        {
          name: "已评价",
          rate: 100,
        },
      ],
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    checkedItem(obj) {
      console.log("选中", obj);
    },
    /**
     * @description tab切换
     */
    tabSwitching(str) {
      this.tabActive = str;
    },
    getList(value) {
      console.log("查询数据", value);
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
.motion-page {
  width: calc(100% - 140px);
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;
  .motion-main_left {
    width: 853px;
    margin-top: 54px;
    margin-bottom: 25px;
    .motion-main_left_warp {
      padding: 21px 35px 0px 48px;

      .main_left_main {
        height: 1045px;
        background: url("@/assets/image/bar_bottom_bg.png") no-repeat;
        background-size: contain; /* 背景图覆盖整个元素区域 */
        background-position: center bottom;
        background-attachment: fixed;
        padding-bottom: 72px;
        margin-top: 46px;
        .item {
          height: 106px;
          border-radius: 0px 0px 0px 0px;
          display: flex;
          flex-direction: row;
          //   justify-content: space-between;
          align-items: center;
          .label {
            width: 120px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 30px;
            color: #ffffff;
            line-height: 35px;
            font-style: normal;
            text-transform: none;
            padding-left: 32px;
          }
          .progress-bar {
            width: 479px;
            height: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0px 0px 0px 0px;
            margin: 0 30px 0 6px;
            position: relative;
            .bar {
              height: 100%;
              width: 0;
              background: linear-gradient(90deg, #5a3fff 0%, #1ed6ff 100%);
              border-radius: 0px 0px 0px 0px;
            }
            .cursor {
              position: absolute;
              top: -5px;
              width: 42px;
              height: 42px;
              background: rgba(137, 164, 255, 0.5);
              box-shadow: 0px 0px 6px 4px rgba(108, 176, 255, 0.65);
              border-radius: 21px;
              line-height: 42px;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
              .sub {
                width: 26px;
                height: 26px;
                background: #b8eaff;
                box-shadow: 0px 0px 5px 1px rgba(255, 255, 255, 0.85);
                border-radius: 13px;
              }
            }
          }
          .number {
            font-family: DIN-BlackItalic, DIN-BlackItalic;
            font-weight: 400;
            font-size: 40px;
            color: #ffe062;
            line-height: 47px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }
      .wordCloud-warp {
        position: relative;
        height: 542px;
        .wordCloud-warp_bg {
          position: absolute;
          width: 114%;
          margin-left: -62px;
          top: 78px;
          height: 400px;
          background: url("@/assets/image/wordCloud-warp_bg.png") no-repeat;
          background-size: 100% auto;
        }
        .wordCloud-warp_main {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .motion-main_center {
    width: 1992px;
    position: relative;

  
   
  }
  .motion-main_right {
    width: 839px;
    margin-bottom: 25px;
    margin-top: 54px;
    .four-cards {
      padding: 24px 25px 68px 40px;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start; // 替代原先的space-between布局方式
      .single-card {
        position: relative;
        flex: 1;
        height: 209px;
        margin: 30px 5px 5px 0; // 间隙为5px

        background: url("@/assets/image/single_card_background.png") no-repeat;
        background-size: contain;
        width: calc(
          (100% - 10px) / 2
        ); // 这里的10px = (分布个数2-1)*间隙5px, 可以根据实际的分布个数和间隙区调整
        min-width: calc(
          (100% - 10px) / 2
        ); // 加入这两个后每个item的宽度就生效了
        max-width: calc(
          (100% - 10px) / 2
        ); // 加入这两个后每个item的宽度就生效了
        &:nth-child(2n) {
          // 去除第2n个的margin-right
          margin-right: 0;
        }
        .title {
          position: absolute;
          left: 188px;
          top: 28px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 50px;
          color: #ffffff;
          line-height: 59px;
          text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .rate {
          position: absolute;
          left: 188px;
          top: 94px;
          font-family: DIN, DIN;
          font-weight: bold;
          font-size: 50px;
          color: #ffffff;
          line-height: 59px;
          text-align: right;
          font-style: normal;
          text-transform: none;
          span {
            font-family: DIN-BoldItalic, DIN-BoldItalic;
            font-weight: 400;
            margin-right: 10px;
            color: #00d46a;
            font-size: 50px;
            line-height: 59px;
            text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #17a4f3 100%),
              linear-gradient(90deg, #ffffff 0%, #00d46a 100%);
            -webkit-background-clip: text; /* WebKit 浏览器专用属性，使背景剪切至文本 */
            background-clip: text; /* 标准属性 */
          }
        }
      }
    }
  }
}
.motion-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6; /* 设置透明度 */
}
</style>
