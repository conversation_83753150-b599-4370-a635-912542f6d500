import Vue from "vue";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
// import './plugins/echarts-wordcloud.min.js'  // 根据实际地址引入
import * as echarts from 'echarts';
import 'echarts-wordcloud'
import "echarts-gl";
import App from "./App.vue";

import configs from "./configs";
// 路由
import router from "./routers";
// 请求
import service from "./services";

// 按需引入UI库
import "./plugins/ui.plugin";
import global_ from "../src/global";
// API配置文件
export const API_CONFIG = {
  // PDF文件查看服务前缀
  PDF_VIEW_PREFIX: 'https://rdtest.rd.gz.cn/ilzss', //测试环境
};
Vue.prototype.GLOBAL = global_; //// 大新 全局变量
store.dispatch("navigation/GLOBALs", global_.basePath_1);
console.log(global_);
// 应用配置
import { sync } from "vuex-router-sync";
import { checkToken } from '../src/utils/checkToken';  
// 在 Vue 的原型上挂载 getToken 方法
Vue.prototype.$checkToken = checkToken;

Vue.use(configs);
Vue.use(service);
Vue.use(ElementUI);


Vue.prototype.$echarts = echarts;
// 状态
import store from "./stores";
Vue.config.productionTip = false;

sync(store, router, {
  moduleName: "routeStore",
});

Vue.config.productionTip = false;


// vm 外部引用
window.scmp_vm = {};
window.scmp_vm.root = new Vue({
  render: (h) => h(App),
  store,
  router,
}).$mount("#app");
