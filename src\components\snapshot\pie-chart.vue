<template>
  <div ref="chart" class="chart"></div>
</template>
<script>
export default {
  name: "barChart",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    xAxis: {
      type: Array,
      default: () => [],
    },
    chartData: {
      type: Array,
      default: () => [
        { name: "满意", value: 0 },
        { name: "基本满意", value: 0 },
        { name: "不满意", value: 0 }
      ]
    }
  },
  mounted() {
    // 初始化视图
    this.getIint();
  },
  watch: {
    chartData: {
      handler() {
        this.getIint();
      },
      deep: true,
    },
  },
  methods: {
    getIint() {
      let myChart = this.$echarts.init(this.$refs.chart);
      let data = [];
      let currentIndex = 0;
      let oldIndex;
      let barChartList = this.chartData;
      
      //   var sum = barChartList.reduce((per, cur) => per + cur.value, 0);
      var color = [
        "rgba(88, 215, 100, 1)",
        "rgba(251, 233, 71, 1)",
        "rgba(255, 90, 90, 1)",
      ];
      let total = Math.max(...barChartList.map((item) => parseInt(item.value)));
      let gap = total * (6 / 360);
      for (var i = 0; i < barChartList.length; i++) {
        data.push(
          {
            value: barChartList[i].value,
            name: barChartList[i].name,
            itemStyle: {
              normal: {
                borderColor: color[i],
                shadowColor: color[i],
                color: color[i],
              },
            },
          },
          {
            value: gap,
            name: "",
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                color: "rgba(0, 0, 0, 0)",
                borderColor: "rgba(0, 0, 0, 0)",
                borderWidth: 0,
              },
            },
            emphasis: {
              label: {
                show: false,
              },
            },
          }
        );
      }
      var seriesOption = [
        {
          name: "pie1",
          type: "pie",
          radius: [70, 100],
          center: ["50%", "45%"],
          grid: {
            containLabel: true,
          },
          label: {
            show: true,
            position: "center",
            formatter: function() {
              const total = barChartList.reduce((sum, item) => sum + item.value, 0);
              const satisfied = barChartList[0].value + barChartList[1].value;
              const rate = total === 0 ? 0 : Math.round((satisfied / total) * 100);
              return `{b_style|满意率}\n{c_style|${rate}%}`;
            },
            rich: {
              b_style: {
                padding: [0, 0, 10, 0],
                fontSize: 28,
                fontWeight: 400,
                color: "#fff",
              },
              c_style: {
                fontSize: 40,
                fontWeight: "bold",
                color: "#94f59d",
                padding: [10, 0, 0, 0]
              },
            },
          },
          emphasis: {
            label: {
              show: true,
              fontSize: "14",
              fontWeight: "normal",
            },
          },
          labelLine: {
            show: false,
          },
          data: data,
        },
      ];
      var optionRich = {
        a: {
          fontSize: 30,
          fontWeight: 400,
          color: "#fff",
          lineHeight: 20,
          padding: [1, 0, 0, 0],
          verticalAlign: 'middle'
        },
      };

      barChartList.forEach((ele, i) => {
        optionRich[i] = {
          width: 16,
          fontSize: 16,
          fontWeight: 400,
          lineHeight: 20,
          color: "#fff",
          align: "center",
          padding: [1, 0, 0, 8],
          verticalAlign: 'middle'
        };
      });
      //   console.log(optionRich);
      let option = {
        backgroundColor: "transparent",
        color: color,
        tooltip: {
          show: true,
          formatter: function(params) {
          const value = params.value;
          const percent = value === 0 ? 0 : Math.round(params.percent);
          return `${params.name}: ${value} (${percent}%)`;
        },
          textStyle: {
            fontSize: 32,
            fontWeight: "bold",
          },
        },
        legend: {
          type: "scroll",
          orient: "horizontal",
          align: 'left',
          bottom: "5%",
          icon: "rect",
          itemHeight: 20,
          itemWidth: 20,
          itemGap: 10,
          height: 300,
          data: barChartList,
          formatter: function (name) {
            return `{a|${name}}`;
          },
          textStyle: {
            color: "#FFF",
            rich: optionRich,
            verticalAlign: 'middle'
          },
          itemStyle: {
            borderWidth: 0
          },
          symbolKeepAspect: true,
        
        },
        series: seriesOption,
      };

      myChart.on("mouseover", (params) => {
        oldIndex = currentIndex;
        currentIndex = params.dataIndex;
        highlightPie(currentIndex, oldIndex);
      });
      function highlightPie(currentIndex, oldIndex) {
        myChart.dispatchAction({
          type: "downplay",
          seriesIndex: 0,
          dataIndex: oldIndex,
        });
        myChart.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: currentIndex,
        });
      }
      setTimeout(() => {
        highlightPie(0, 1);
      }, 50);

      myChart.setOption(option);
    },
  },
};
</script>
<style lang="less" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
