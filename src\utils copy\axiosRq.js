import Vue from 'vue'
import axios from "axios";
const instance_1 = axios.create({
    baseURL: Vue.prototype.GLOBAL.basePath_1,
    timeout: 10000,
    headers: {
        "Content-type": "application/json;charset=UTF-8",
         "flag" : false,
      },
  });
  const instance_2 = axios.create({
    baseURL: Vue.prototype.GLOBAL.basePath_2,
    timeout: 10000,
    headers: {
        "Content-type": "application/json;charset=UTF-8",
        "flag" : false,
      },
  });
  export default {
    instance_1,
    instance_2
  }