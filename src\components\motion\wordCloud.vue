<template>
  <div ref="loginTimes" class="chart"></div>
</template>
<script>
export default {
  name: "wordCloud",
  props: {
    // 标题
    data: {
      type: Array,
      default:()=> [],
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      window.addEventListener('resize', this.handleResize);
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    initChart() {
      if (this.chart) {
        this.chart.dispose();
      }
      
      const chartDom = this.$refs.loginTimes;
      if (!chartDom) return;
      
      this.chart = this.$echarts.init(chartDom);
      
      const option = {
        backgroundColor: "transparent",
        series: [
          {
            name: "搜索指数",
            type: "wordCloud",
            //size: ['9%', '99%'],
            sizeRange: [50, 80],
            //textRotation: [0, 45, 90, -45],
            rotationRange: [-45, 90],
            // shape: 'circle',
            left: "center",
            top: "center",
            width: "100%",
            height: "100%",
            right: null,
            bottom: null,
            rotationStep: 13,
            maskImage: "",
            textPadding: 0,
            autoSize: {
              enable: true,
              minSize: 6,
            },
            textStyle: {
              fontFamily: "sans-serif",
              // 颜色可以用一个函数来返回字符串
              color: function () {
                // 随机颜色
                return (
                  "rgb(" +
                  [
                    Math.round(Math.random() * 160),
                    Math.round(Math.random() * 160),
                    Math.round(Math.random() * 160),
                  ].join(",") +
                  1+")"
                );
              },

              emphasis: {
                shadowBlur: 10,
                shadowColor: "#333",
              },
            },
            data: this.data,
          },
        ],
      };
      // echarts参数设置
      this.chart.setOption(option);
    },
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true
    }
  }
};
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
