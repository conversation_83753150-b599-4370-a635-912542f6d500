import request from "@/utils/requestTemp";
import qs from "qs";

// ⺠意收集情况⼯作视窗-⺠意关键词热度
export async function getOnlineLiaisonStationHotKey(data, token) {
  return request({
    url: "cockpit/getOnlineLiaisonStationHotKey",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
  });
}

// ⺠意收集情况⼯作视窗-全市⺠意处理情况
export async function getOnlineLiaisonStationHub(data, token) {
  return request({
    url: "cockpit/getOnlineLiaisonStationHub",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
  });
}

// ⺠意收集情况⼯作视窗-民意提交统计情况
export async function getCountMonthOnlineLiaisonStation(data, token) {
  return request({
    url: "cockpit/getCountMonthOnlineLiaisonStation",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
  });
}

// ⺠意收集情况⼯作视窗-⺠意处理情况详情
export async function getOnlineLiaisonStationList(params,data ) {
  return request({
    url: "cockpit/getOnlineLiaisonStationList",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    params: params,  // 请求数据
  });
}

// ⺠意收集情况⼯作视窗-⺠意解决⽅式
export async function getOnlineLiaisonStationTransType(data, token) {
  return request({
    url: "cockpit/getOnlineLiaisonStationTransType",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
  });
}

// ⺠意收集情况⼯作视窗-本周全市⺠意反映情况
export async function getOnlineLiaisonStationWeek(data, token) {
  return request({
    url: "cockpit/getOnlineLiaisonStationWeek",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
  });
}