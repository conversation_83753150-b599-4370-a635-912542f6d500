import Vue from 'vue';
import Router from 'vue-router';

/* ---start: 顶部进度条加载 --- */
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
NProgress.configure({
  easing: 'ease',
  speed: 500,
  showSpinner: false,
  trickleSpeed: 200,
  minimum: 0.3,
});

/* ---end --- */



// hack router push callback
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject);
  }
  return originalPush.call(this, location).catch(err => err);
};

/* ---end --- */
Vue.use(Router);

// 默认首页改到了getDefaultRoutersByPlatform方法动态设置
let routerModule = [
  // 配置入口 route, 用来控制 / 自动跳转，也可以不配置。
  {
    path: '/',
    redirect:'/region'
  },
];

export const constantRoutes = [
  {
    path: "/region",
    component: () => import("@/views/region/index"),
  },
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/motion",
    component: () => import("@/views/motion/index"),
  },
  {
    path: "/snapshot",
    component: () => import("@/views/snapshot/index"),
  },
  {
    path: "/junior",
    component: () => import("@/views/junior/index"),
  },
  {
    path: "/liaisonStation",
    component: () => import("@/views/liaisonStation/index"),
  },
  {
    path: "/reprInfo",
    component: () => import("@/views/reprInfo/index"),
  },
  {
    path: "/perform",
    component: () => import("@/views/perform/index"),
    meta: {
      loading: true
    }
  },
  {
    path: "/personnel",
    component: () => import("@/views/personnel/index"),
  },
  {
    path: "/popularWill",
    component: () => import("@/views/popularWill/index"),
    meta: {
      loading: true
    }
  },
  {
    path: "/liaisonDetail",
    component: () => import("@/views/liaisonDetail/index"),
    children: [
      {
        path: "basicInformation",
        component: () => import("@/views/liaisonDetail/basicInformation/index"),
      },
      {
        path: "videoConnection",
        component: () => import("@/views/liaisonDetail/videoConnection/index"),
      },
      {
        path: "eventAnnouncement",
        component: () => import("@/views/liaisonDetail/eventAnnouncement/index"),
      },
      {
        path: "solicitationOpinions",
        component: () => import("@/views/liaisonDetail/solicitationOpinions/index"),
      },
      {
        path: "stationRepresentative",
        component: () => import("@/views/liaisonDetail/stationRepresentative/index"),
        children: [
          {
            path: "stationDetails",
            component: () => import("@/views/liaisonDetail/stationRepresentative/stationDetails"),
          },
        ],
      },
      {
        path: "handlingOpinion",
        component: () => import("@/views/liaisonDetail/handlingOpinion/index"),
      },
    ],
  },
  {
    path: "/dome",
    component: () => import("@/views/dome/index"),
  },
];



const requireRouters = require.context('./', true, /^((?!index|\.unit\.).)*\.js$/);
requireRouters.keys().forEach(r => {
  routerModule = routerModule.concat(...requireRouters(r).default);
});

/* ---ebd: --- */
const router = new Router({
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { x: 0, y: 0 };
    }
  },
  routes: [...routerModule],
});

export function resetRouter () {
  router.matcher = new Router({
    mode: "history",
    base: process.env.BASE_URL,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  }).matcher;
}

router.beforeEach((to, from, next) => {
  NProgress.start();
  // 配置路由标题
  if (to.meta && to.meta.title) {
    document.title = to.meta.title; // 设置浏览器的标题
  }
  // TODO: 权限验证（结合 menu store）
  return next();
});

router.afterEach(() => {
  NProgress.done();
});

// 注册$scmp_router，方便在js中引用
Object.defineProperty(Vue.prototype, '$scmp_router', {
  enumerable: true,
  get() {
    return router;
  },
});

export default router;

/*
 * ============ 主模块路由配置规则：==========================
 * 1. 路由跳转不能放到子模块：
 *
 * 2. 主模块中配置路由跳转，redirect 所在路由不能配置 component(会造成App组件多次刷新)
 *
    {
            path: '/sxpz',
            name: 'sxpz',
            // component: bed, // 跳转处，不能加 component
            redirect: '/sxpz/sxpz-list',
            meta: {
                ...routerMeta,
                name: '事项配置',
                title: '事项管理-事项配置',
                icon: 'fa fa-home',
            }
    }
 *
 *
 * */
