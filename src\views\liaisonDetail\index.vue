<template>
  <div class="motion-page">
    <div class="liaison-header">
      <!-- 修改后的标题样式 -->
      <div class="liaison-text" style="word-break: break-word; white-space: normal; max-width: 55%;">
        {{ areaName }}
      </div>
      <div class="liaison-menu">
        <div class="customTab" @click="handleSelect(index, item)" v-for="(item, index) in tabList"
          :class="`customTabact${activeIndex == index ? '1' : '2'}`" :key="index">
          {{ item.name }}
        </div>
      </div>
    </div>

    <!-- 修正样式属性的冒号 -->
    <div style="height: 1512px; padding: 160px 0 0 0">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "motion-index",
  components: {},
  data() {
    return {
      activeIndex: 5,
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [],
      tabList: [
        {
          name: "活动公告",
          path: "/liaisonDetail/eventAnnouncement",
        },
        {
          name: "意见征集",
          path: "/liaisonDetail/solicitationOpinions",
        },
        {
          name: "进站代表",
          path: "/liaisonDetail/stationRepresentative",
        },
        {
          name: "问题处理",
          path: "/liaisonDetail/handlingOpinion",
        },
        {
          name: "视频连线",
          path: "/liaisonDetail/videoConnection",
        },
        {
          name: "基础信息",
          path: "/liaisonDetail/basicInformation",
        },
      ],
      selectPath: ''
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    this.areaName = this.$route.query.areaName;
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.streetTownId = this.$route.query.streetTownId;
    this.selectPath = this.$route.path
    console.log('selectPath',  this.selectPath)
    if(this.$route.path == '/liaisonDetail/videoConnection') {
        this.activeIndex = 4
    } else if(this.$route.path == '/liaisonDetail/handlingOpinion') {
        this.activeIndex = 3
    } else if(this.$route.path == '/liaisonDetail/stationRepresentative') {
        this.activeIndex = 2
    } else if(this.$route.path == '/liaisonDetail/solicitationOpinions') {
        this.activeIndex = 1
    } else if(this.$route.path == '/liaisonDetail/eventAnnouncement') {
        this.activeIndex = 0
    }
  },
  mounted() { },
  watch: {
    '$route.path': {
      immediate: true,
      handler(newPath) {
        this.selectPath = newPath;
      }
    }
  },
  methods: {
    handleSelect(key, item) {
      // 如果是驻站代表详情页，禁止所有跳转
      if (this.selectPath.includes('/liaisonDetail/stationDetails')) {
        console.log('当前是驻站代表详情页，禁止所有跳转');
        this.$router.go(-1)
        return;
      }
      console.log('key',key)
      this.activeIndex = key;
      console.log('当前选中索引:', key);
      
      // 添加100ms延迟确保DOM更新
      this.$nextTick(() => {
        setTimeout(() => {
          this.$router.push({
            path: item.path,
            query: {
              ...this.$route.query,
            }
          });
        }, 100);
      });
    },
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
.motion-page {
  // width: calc(100% - 178px);
  height: 1817px;
  display: flex;
  flex-direction: column;
  padding: 90px 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;
  overflow: auto;

  /* Webkit浏览器滚动条样式 */
  &::-webkit-scrollbar {
    height: 8px; // 滚动条高度
    background: rgba(0, 25, 63, 0.2); // 轨道背景
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg,
        rgba(0, 25, 63, 0) 0%,
        rgba(10, 108, 222, 0.8) 50%,
        rgba(0, 25, 63, 0) 100%);
    border-radius: 4px;
    border: 1px solid rgba(10, 108, 222, 0.5); // 添加边框增强科技感
    /*box-shadow: 0 0 5px rgba(78, 153, 248, 0.5); // 发光效果*/
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg,
        rgba(0, 25, 63, 0) 0%,
        rgba(10, 150, 255, 0.9) 50%,
        rgba(0, 25, 63, 0) 100%);
  }

  .liaison-header {
    display: flex;
    flex-direction: row;
    position: absolute;
    z-index: 6;

    .liaison-text {
      width: 1728px;
      height: 580px;
      padding: 300px 0 0 0px;
      margin-top: -270px;
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;

      background: url("@/assets/image/flare.png") no-repeat;
      background-size: 1728px 580px;
      background-position: -132px 104px;
      font-weight: 400;
      font-size: 56px;
      color: #ffffff;
      line-height: 55px;
      letter-spacing: 7px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .liaison-menu {
      display: flex;
      flex-direction: row;
      margin-left: -310px;

      .customTab {
        // margin-top: 20px;
        // padding: 40px 103px 60px 111px;
        width: 376px;
        height: 124px;
        font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
        font-weight: 500;
        font-size: 40px;
        color: #ffffff;
        line-height: 100px;
        letter-spacing: 1px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        cursor: pointer;
        /* 添加鼠标手样式 */
      }

      .customTabact1 {
        background: url("@/assets/image/customTabact1.png") no-repeat;
        background-size: contain;
      }

      .customTabact2 {
        background: url("@/assets/image/customTabact2.png") no-repeat;
        background-size: contain;
      }

      // div {
      //   margin: 0 12px;
      // }
    }
  }
}

.motion-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/image/motion_bg.png") no-repeat;
  background-position: center;
  background-size: 104% 109%;
  opacity: 0.6;
  /* 设置透明度 */
}
</style>
