import request from "@/utils/requestYAJY";
// import { instance_yajy } from "@/api/axiosRq";
import qs from "qs";
// 分类统计建议数
export async function getContentTypeCount(data) {
  return request({
    url: "proposal/openApi/getContentTypeCount",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded", 
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// 单位办理情况百分比
export async function getHandleStatus(data) {
  return request({
    url: "proposal/openApi/getHandleStatus",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}

// 统计各类待处理的建议数
export async function getPendingCount(data) {
  return request({
    url: "proposal/openApi/getPendingCount",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}

// 统计各类待处理的建议数 区镇
export async function getPendingCountQz(data) {
  return request({
    url: "proposal/openApi/getPendingCountQz",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}

// 根据建议类型统计建议数
export async function getProposalCount(data) {
  return request({
    url: "proposal/openApi/getProposalCount",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}

// 根据建议类型统计建议数 区镇
export async function getProposalCountQz(data) {
  return request({
    url: "proposal/openApi/getProposalCountQz",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}


// 获取重点督办建议列表
export async function getRecommendList(data) {
  return request({
    url: "proposal/openApi/getRecommendList",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}

// 今日新增建议数
export async function getTodayInsert(data) {
  return request({
    url: "proposal/openApi/getTodayInsert",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}

// 议案动态
export async function findPage(data) {
  return request({
    url: "proposal/suggestion/findPage",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
  });
}

// 议案动态详情
export async function getById(data) {
  return request({
    url: "proposal/suggestion/getById",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
  });
}

// 议案动态详情 区级
export async function getByQuId(data) {
  return request({
    url: "proposal/openApi/getById",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
  });
}

// 满意率
export async function getFeedBackRate(data) {
  return request({
    url: "proposal/openApi/getFeedBackRate",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
  });
}

// 答复率
export async function getReplyRate(data) {
  return request({
    url: "proposal/openApi/getReplyRate",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
  });
}

// 届次数据
export async function findBySession(data) {
  return request({
    url: "system/meetingMgr/findBySession",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
  });
}

// 区镇
export async function getContentTypeCountQz(data) {
  return request({
    url: "proposal/openApi/getContentTypeCountQz",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
  });
}


// 议案动态
export async function findQzByPage(data) {
  return request({
    url: "proposal/openApi/findQzByPage",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json;charset=UTF-8",  // 保持内容类型为表单
    },
  });
}

// 今日新增建议数
export async function getQZTodayInsert(data) {
  return request({
    url: "proposal/openApi/getQZTodayInsert",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}
// 获取重点督办建议列表
export async function getQZRecommendList(data) {
  return request({
    url: "proposal/openApi/getQZRecommendList",  // 更新为目标接口路径
    method: "get",  // 如果是 GET 请求，请修改成 'get'
    params: data,  // 请求数据
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",  // 保持内容类型为表单
    },
  });
}