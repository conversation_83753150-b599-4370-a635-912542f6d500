import Vue from 'vue';
// import { config } from '../../../configs/index';
const vm = new Vue();

const intialState = {
  userInfo: {
    user: {},
    userDetail: {},
  },
};

export default {
  state: {
    userInfo: intialState.userInfo,
  },
  getters: {
    // eslint-disable-next-line arrow-parens
    userNameGetter: (state) => state.userInfo.name,
    // eslint-disable-next-line arrow-parens
    userTypeGetter: (state) => state.userInfo.roleStr,
    // eslint-disable-next-line arrow-parens
    userLoginNameGetter: (state) => state.userInfo.user.loginName,
    // eslint-disable-next-line arrow-parens
    userMobilePhoneGetter: (state) => state.userInfo.userDetail.mobilePhone,
    // eslint-disable-next-line arrow-parens
    userPhotoGetter: (state) => state.userInfo.userPhoto,
    // eslint-disable-next-line arrow-parens
    userIdGetter:(state) => state.userInfo.id,
  },
  actions: {
    async ACT_GetUserInfo({ commit }) {
      try {
        // const response = await vm.$scmp_api.auth.getUserInfo();
        const userReaponse = await vm.$scmp_api.auth.getUserInfoData();
        // const data = response.data;
        const userData = userReaponse.data.data;
        // const user = data.data;
        // 提取角色信息
        // const roleStr = user
        //   .map((item) => {
        //     return item.code;
        //   })
        //   .join(',');
        // const userInfo = { ...userData, ...{ roleStr } };
        const userInfo = userData;
        commit('MUT_SetUser', userInfo);
        return userInfo;
      } catch {
        return { error: '连接网络超时' };
      }
    },

    ACT_Logout({ commit }) {
      commit('MUT_LogoutUser');
    },
  },
  mutations: {
    MUT_SetUser(state, userInfo) {
      state.userInfo = {
        ...state.userInfo,
        ...userInfo,
      };
    },
    MUT_LogoutUser(state) {
      state.userInfo = intialState.userInfo;
      window.location.href = '/logout';
    },
  },
};
