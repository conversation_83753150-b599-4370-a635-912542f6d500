import request from "@/utils/requestTemp";
import qs from "qs";

// ⼈⼤代表⼯作视窗-代表分布（代表总数与五级代表数量）
export async function getDbNumCount(data, token) {
  return request({
    url: "cockpit/getDbNumCount",  // 更新为目标接口路径
    method: "post",  // 如果是 GET 请求，请修改成 'get'
    data: data,  // 请求数据
    headers: {
      "Content-Type": "application/json",  // 保持内容类型为表单
      // "Authorization": `Bearer ${token}`,  // 添加 Authorization 头部携带 Token
    },
    // transformRequest: [
    //   function (data) {
    //     return qs.stringify(data);  // 格式化请求参数
    //   },
    // ],
  });
}

// ⼈⼤代表⼯作视窗-本年度代表履职情况总览
export async function getDutyCountAndTypeForYear(data, token) {
  return request({
    url: "cockpit/getDutyCountAndTypeForYear",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}

// ⼈⼤代表⼯作视窗-本年度市本级代表培训总览
export async function getDutyStudyForYear(data, token) {
  return request({
    url: "cockpit/getDutyStudyForYear",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}

// ⼈⼤代表⼯作视窗-各区联络站信息统计浮窗
export async function getLiaisonInfoForDistrict(data, token) {
  return request({
    url: "cockpit/getLiaisonInfoForDistrict",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}

// ⼈⼤代表⼯作视窗-联络站统计(联络站总数与各区统计数)
export async function getLiaisonStationNumCount(data, token) {
  return request({
    url: "cockpit/getLiaisonStationNumCount",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}


// ⼈⼤代表⼯作视窗-联络站统计(镇街联络站总数与各区统计数)
export async function getLiaisonStationNumCountByStreet(data, token) {
  return request({
    url: "cockpit/getLiaisonStationNumCountByStreet",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      // "Content-Type": "application/x-www-form-urlencoded",  // 保持为表单格式
      "Content-Type": "application/json",
    },
  });
}

// ⼈⼤代表⼯作视窗-⺠意收集情况统计
export async function getOnlineStationCount(data, token) {
  return request({
    url: "cockpit/getOnlineStationCount",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}

// ⼈⼤代表⼯作视窗-本年度市本级⼈事任免情况总览
export async function personnelAppoint(data, token) {
  return request({
    url: "cockpit/personnelAppoint/statistics",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}
// ⼈⼤代表⼯作视窗-本年度区本级⼈事任免情况总览
export async function getpersonnelQuStatistics(data, token) {
  return request({
    url: "cockpit/personnelAppoint/quStatistics",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}

// ⼈⼤代表⼯作视窗-本年度市本级议案建议总览
export async function getYajy(data, token) {
  return request({
    url: "cockpit/getYajy",  // 更新为目标接口路径
    method: "post",  // 如果接口是 GET 请求，可以改为 'get'
    data: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}


// 民意处理活动详情
export async function getInfo(data, token) {
  return request({
    url: "onlineLiaisonMember/getInfo",  // 更新为目标接口路径
    method: "get",  // 如果接口是 GET 请求，可以改为 'get'
    params: data,  // 请求的数据
    headers: {
      "Content-Type": "application/json",  // 保持为表单格式
    },
  });
}