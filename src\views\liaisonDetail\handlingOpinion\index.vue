<template>
  <div class="basicInformation-page">
    <div class="basicInformation-main_left">
      <ScmpCard cardName="问题处理情况总览" pictureMode="2">
        <div slot="main" class="four-cards">
          <div class="single-card" v-for="(item, index) in onlineLiaisonStationHub" :key="index"
            :class="{ 'clock1': index === 2, 'edit1': index === 3 }"
            @click="handleCardClick(item, index)">
            <div class="title">{{ item.name }}</div>
            <div class="rate">
              <span>{{ item.rate }}</span>
              <span v-if="index == 2"></span>
            </div>
          </div>
        </div>
      </ScmpCard>
      <ScmpCard cardName="问题提交统计情况" style="margin-top:30px">
        <div slot="main" class="snapshot-main_left_warp">
          <div class="main_left_main_top">
            <div class="main_content_main">
              <div class="chart-box" ref="chartBox">
                <BarChartTop :data="barChartDataTop" :xAxis="xAxisListTop" ref="barChart" />
                <div class="chart-num">
                  <div v-for="(item, index) in barChartDataTop" :key="index"
                       class="num-item"
                       :style="getNumPosition(index)">
                    {{ item }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScmpCard>

      <div class="bottom_qr">
        <div class="qrcode">
          <canvas ref="qrCanvas"></canvas>
        </div>
        <div class="mywz">
          <div style="margin: 138px 0 0 62px">问题码上说</div>
          <div style="margin: 19px 0 0 200px">实事马上办</div>
        </div>
      </div>

      <!-- <img
        class="bottom"
        :src="require('@/assets/image/bottom_201.png')"
        alt=""
      /> -->
    </div>
    <div class="basicInformation-main_right">
      <ScmpCard cardName="问题处理情况列表" backgroundImage="card_bg2" :rightPicture="false">
        <div slot="main" class="con-bottom-box">
          <ScmpTable :tableData="deptDictList" :tableColumn="deptDictTableColumn" :rowNum="11" :indicesGrouping="true">
          </ScmpTable>
          <ScmpPagination :total="listTotal" :current-page.sync="currentPage" :page-size.sync="pageSize"
            @current-change="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </ScmpCard>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import QRCode from "qrcode";
import {
  getOnlineLiaisonStationList,
  getOnlineLiaisonStationHub,
  getCountMonthOnlineLiaisonStation
} from "@/api/dataPageApi/popularWill";
import {
  getMemberCommentCommentCategory,
} from "@/api/dataPageApi/snapshot";
export default {
  name: "basicInformation-index",

  components: {
    ScmpCard: () => import("@/comps/scmp-card"),
    ScmpTable: () => import("@/comps/scmp-tablePopularWill"),
    BarChartTop: () => import("@/components/snapshot/bar-chart-top2.vue"),
    ScmpPagination: () => import("@/components/pagination/index.vue"),
  }, data() {
    return {
      year: String(new Date().getFullYear()),
      currentPage: 1,
      pageSize: 10,
      // 下拉菜单配置
      spotDownColumn: {
        text: "name",
      },
      dropDownList: [{ name: "菜单项1" }, { name: "菜单项2" }],
      qrText: "努力赚钱",
      // Table配置
      deptDictList: [
      ],
      deptDictTableColumn: [
        { prop: "issueNum", label: "编号", align: "center", width: "300" },
        // { prop: "liaisonStationName", label: "联络站名称", align: "center", width: "510" },
        { prop: "title", label: "群众反映主题", align: "center", width: "550" },
        // { prop: "dbComment", label: "代表意见" , align: "center" },
        { prop: "liaisonReplyComment", label: "人大代表意见", align: "center", width: "550" },
        { prop: "statusName", label: "状态", align: "center", width: "200" },
        { prop: "createTime", label: "反映时间", align: "center" },
      ],
      // 左上方
      singleCardList: [
        // {
        //   name: "收集民意数量",
        //   rate: 66,
        // },
        // {
        //   name: "已办结民意数",
        //   rate: 52,
        // },
        // {
        //   name: "平均办理周期",
        //   rate: 7,
        // },
        // {
        //   name: "办结率",
        //   rate: 32,
        // },
      ],
      // 顶部数据
      // barChartDataTop: [76, 65, 55, 45, 50, 60, 30, 49, 47, 49],
      barChartDataTop: [],
      xAxisListTop: [
        // "市容城管 ",
        // "交通管理",
        // "城市建设",
        // "交通运输",
        // "其他事件",
        // "环境保护",
        // "治安消防",
        // "房产管理",
        // "文教卫体",
        // "三农问题",
      ],
      listTotal: 0,
      currentPage: 1,
      pageSize: 10,
      onlineLiaisonStationHub: [],
      currentFilterStatus: '' // 当前筛选状态
    };
  },
  computed: {
    ...mapGetters("auth/user", ["userTypeGetter"]),
  },
  created() {
    this.areaName = this.$route.query.areaName;
    this.administrativeAreaId = this.$route.query.administrativeAreaId;
    this.streetTownId = this.$route.query.streetTownId;
    this.getMemberCommentCommentCategory(this.administrativeAreaId, this.streetTownId, this.year, this.areaName)
    this.getOnlineLiaisonStationHub(this.year, this.administrativeAreaId, this.streetTownId, this.$route.query.liaisonStationId)
  },
  mounted() {
    this.generateQRCode();
    // 默认展示收集民意数量的列表（所有数据）
    this.$nextTick(() => {
      this.filterTableByStatus('all');
    });
  },
  watch: {},
  methods: {
    getOnlineLiaisonStationHub(year, administrativeAreaId, streetTownId, liaisonStationId) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        year: year,
        administrativeAreaId: administrativeAreaId,
        streetTownId: streetTownId,
        liaisonStationId: liaisonStationId,
      }
      getOnlineLiaisonStationHub(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          this.onlineLiaisonStationHub = [
            {
              name: "收集民意数量",
              rate: res.data.collectedOpinionsCount,
            },
            {
              name: "已办结民意数",
              rate: res.data.resolvedOpinionsCount,
            },
            {
              name: "未办结民意数",
              rate: res.data.notResolvedOpinionsCount,
            },
            {
              name: "办结率",
              rate: res.data.resolutionRate,
            },
          ]

        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    getMemberCommentCommentCategory(administrativeAreaId, streetTownId, year, areaName) {
      const params = {
        streetTownId: streetTownId,
        administrativeAreaId: administrativeAreaId,
        liaisonStationId: this.$route.query.liaisonStationId,
      }
      getCountMonthOnlineLiaisonStation(params)
        .then(res => {
          console.log(res);  // 处理返回的数据
          // 按 num 从大到小排序
          const sortedData = res.data.sort((a, b) => a.month - b.month);
          sortedData.forEach(item => {
            this.barChartDataTop.push(item.num);
            this.xAxisListTop.push(item.month);
          });
        })
        .catch(error => {
          console.error('请求失败', error);  // 处理错误
        });
    },

    async getOnlineLiaisonStationList(data) {
      // const token = 'e0cd6ec9d3394d8095fe40334904c9a4'
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      }
      const data1 = {
        administrativeAreaId: data,
      }
      await getOnlineLiaisonStationList(params, data1)
        .then(res => {
          console.log(res);
          // 格式化时间为YYYY-MM-DD HH:mm格式
          res.rows.forEach(item => {
            item.createTime = item.createTime.substring(0, 19);
          });
          this.deptDictList = res.rows;
          console.log(this.deptDictList)
          this.deptDictListTotal = res.total;
        })
        .catch(error => {
          console.error('请求失败', error);
        });
    },
    checkedItem(obj) {
      console.log("选中", obj);
    },
    generateQRCode() {
      QRCode.toCanvas(this.$refs.qrCanvas, this.qrText, (error) => {
        if (error) console.error(error);
        console.log("QR code generated!");
      });
    },
    handleCurrentChange(val) {
      console.log('当前页:', val);
      // 这里处理页码改变的逻辑
      this.currentPage = val
      this.deptDictList = []
      // 使用当前筛选状态重新请求数据
      this.getFilteredOpinionsList(this.currentFilterStatus)
    }, 
    handleSizeChange(val) {
      console.log('每页条数:', val);
      // 这里处理每页显示条数改变的逻辑
      this.pageSize = val
      this.deptDictList = []
      this.currentPage = 1 // 条数改变时重置到第一页
      // 使用当前筛选状态重新请求数据
      this.getFilteredOpinionsList(this.currentFilterStatus)
    },
    // 获取数字位置
    // 通过箭头按钮滚动图表

    scrollChart(direction) {
      if (direction < 0 && this.chartStartIndex > 0) {
        this.chartStartIndex = Math.max(this.chartStartIndex - 1, 0);
      } else if (direction > 0 && this.chartStartIndex < this.maxChartIndex) {
        this.chartStartIndex = Math.min(this.chartStartIndex + 1, this.maxChartIndex);
      }
    },    // 获取数字位置

    getNumPosition(index) {
      // 计算每个数字应该显示的位置
      const chartWidth = this.$refs.chartBox ? this.$refs.chartBox.clientWidth : 0;
      const itemWidth = chartWidth / this.barChartDataTop.length; // 使用所有数据的长度
      // 计算左侧位置，确保数字居中于柱状图
      const left = (index * itemWidth) + (itemWidth / 2);

      return {
        left: `${left}px`,
        transform: 'translateX(-50%)', // 居中显示
        top: '-40px' // 调整到柱状图上方更高的位置
      };
    },
    // 处理卡片点击事件
    handleCardClick(item, index) {
      console.log('点击了卡片:', item, '索引:', index);
      
      // 根据不同的卡片类型处理不同的逻辑
      switch(index) {
        case 0: // 收集民意数量
          this.showCollectedOpinionsDetail(item);
          break;
        case 1: // 已办结民意数
          this.showResolvedOpinionsDetail(item);
          break;
        case 2: // 未办结民意数
          this.showUnresolvedOpinionsDetail(item);
          break;
        default:
          console.log('未知卡片类型');
      }
    },
    
    // 显示收集民意数量详情
    showCollectedOpinionsDetail(item) {
      console.log('显示收集民意数量详情:', item);
     
      // 如果需要筛选表格数据，可以这样做
      this.filterTableByStatus('all'); // 显示所有数据
    },
    
    // 显示已办结民意数详情
    showResolvedOpinionsDetail(item) {
      console.log('显示已办结民意数详情:', item);
      // this.$message({
      //   type: 'success',
      //   message: `已办结民意数: ${item.rate}条`
      // });
      
      // 筛选已办结的数据
      this.filterTableByStatus('resolved'); // 只显示已办结数据
    },
    
    // 显示未办结民意数详情
    showUnresolvedOpinionsDetail(item) {
      console.log('显示未办结民意数详情:', item);
      // this.$message({
      //   type: 'warning',
      //   message: `未办结民意数: ${item.rate}条`
      // });
      
      // 筛选未办结的数据
      this.filterTableByStatus('unresolved'); // 只显示未办结数据
    },
    
  
    
    // 根据状态筛选表格数据
    filterTableByStatus(status) {
      console.log('筛选数据状态:', status);
      
      // 可以根据状态重新请求数据
      let filterStatus = '';
      switch(status) {
        case 'resolved':
          filterStatus = '5,6'; // 已办结
          break;
        case 'unresolved':
          filterStatus = '1,2,3,4'; // 未办结的各种状态
          break;
        case 'all':
        default:
          filterStatus = ''; // 所有状态
          break;
      }
      
      // 保存当前筛选状态
      this.currentFilterStatus = filterStatus;
      // 切换筛选条件时重置到第一页
      this.currentPage = 1;
      
      // 重新请求数据
      this.getFilteredOpinionsList(filterStatus);
    },
    
    // 获取筛选后的民意列表
    async getFilteredOpinionsList(status) {
      const params = {
        pageNum: this.currentPage, // 使用当前页码
        pageSize: this.pageSize,
        
      }
      const data = {
        administrativeAreaId: this.administrativeAreaId,
        liaisonStationName: this.areaName,
        streetTownId: this.streetTownId,
        liaisonStationId: this.$route.query.liaisonStationId,
        year: this.year,
        status: status, // 添加状态筛选
      }
      
      const statusMap = {
         1: "未审核",
        2: "已转线下办理中",
        3: "审核不通过",
        4: "已转12345热线办理中",
        5: "已办结",
        6: "已归档",
      };
      
      try {
        const res = await getOnlineLiaisonStationList(params, data);
        console.log('筛选后的数据:', res);
        
        res.rows.forEach(item => {
          item.createTime = item.createTime.substring(0, 19);
        });
        
        this.listTotal = res.total;
        // 只在筛选状态改变时重置到第一页，分页时保持当前页
        this.deptDictList = res.rows.map((item) => {
          return {
            ...item,
            statusName: statusMap[item.status] || "未知状态",
          };
        });
        
      } catch (error) {
        console.error('获取筛选数据失败', error);
      }
    },
    
  },
};
</script>

<!-- 在需要用到sass的地方添加lang="less" -->
<style lang="less" scoped>
/* 民意码上说 实事马上办 字体样式*/
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/fonts/YouSheBiaoTiHei.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.basicInformation-page {
  width: calc(100%);
  height: calc(100vh - 500px);
  display: flex;
  flex-direction: row;
  justify-content: start;
  // padding: 0 60px 0 80px;
  color: #fff;
  font-size: 60px;
  position: relative;
  z-index: 9;

  .basicInformation-main_left {
    width: 873px;
    padding-top: 16px;

    .four-cards {
      padding: 10px 25px 12px 40px;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;

      .single-card {
        position: relative;
        flex: 1;
        height: 209px;
        margin: 30px 5px 5px 0;
        background: url("@/assets/image/single_card_background.png") no-repeat;
        background-size: contain;
        width: calc((100% - 10px) / 2);
        min-width: calc((100% - 10px) / 2);
        max-width: calc((100% - 10px) / 2);
        cursor: pointer;
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 212, 106, 0.3);
        }

        &:nth-child(2n) {
          margin-right: 0;
        }

        &.clock1 {
          background: url("@/assets/image/clock1.png") no-repeat -40px center;
          background-size: calc(100% + 40px) 100%;
        }

        &.edit1 {
          background: url("@/assets/image/edit1.png") no-repeat -40px center;
          background-size: calc(100% + 40px) 100%;
        }

        .title {
          position: absolute;
          left: 188px;
          top: 28px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 32px;
          color: #ffffff;
          line-height: 59px;
          text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .rate {
          position: absolute;
          left: 188px;
          top: 94px;
          font-family: DIN, DIN;
          font-weight: bold;
          font-size: 50px;
          color: #ffffff;
          line-height: 59px;
          text-align: right;
          font-style: normal;
          text-transform: none;

          span {
            font-family: DIN-BoldItalic, DIN-BoldItalic;
            font-weight: 400;
            margin-right: 10px;
            color: #00d46a;
            font-size: 40px;
            line-height: 59px;
            text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #17a4f3 100%),
              linear-gradient(90deg, #ffffff 0%, #00d46a 100%);
            -webkit-background-clip: text;
            background-clip: text;
          }
        }
      }
    }

    .snapshot-main_left_warp {
      padding: 21px 35px 0px 48px;
      margin-top: 44px;

      .main_left_main_top {
        height: 355px;

        .main_content_main {
          height: 100%;
          box-sizing: border-box;

          .chart-box {
            height: calc(100% - 20px);
            width: 100%;
            position: relative;

            .chart-num {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              pointer-events: none;
              z-index: 10;

              .num-item {
                position: absolute;
                font-size: 28px;
                font-weight: bold;
                color: #eceff1;
                text-shadow: 0 0 2px rgba(255, 255, 255, 0.5);
                text-align: center;
              }
            }
          }
        }
      }

      .main_left_main_center {
        height: 535px;
      }

      .main_left_main_bottom {
        height: 495px;

        .main_content_main {
          height: 100%;
          box-sizing: border-box;

          .chart-box {
            height: 100%;
            width: 100%;

            .chart-num {
              display: flex;
              justify-content: space-between;
              padding: 0 25px;
              font-size: 20px;
            }
          }
        }
      }
    }

    .bottom_qr {
      display: flex;
      flex-direction: row;

      .mywz {
        font-family: YouSheBiaoTiHei;
        font-size: 60px;
        color: #ffffff;
        text-shadow: 4px 4px 4px rgba(68, 142, 254, 0.71);

        div {
          font-family: YouSheBiaoTiHei;
        }
      }
    }

    .qrcode {
      width: 215px;
      height: 215px;
      background: url("@/assets/image/qrcode.png") no-repeat;
      background-size: 100% 100%;
      padding: 60px;
      // margin: 0 auto;
      margin-top: 60px;

      canvas {
        width: 215px !important;
        height: 215px !important;
        border-radius: 0px 0px 0px 0px;
      }
    }

    .bottom {
      width: 793px;
      height: 223px;
      margin-top: 40px;
    }
  }

  .basicInformation-main_right {
    margin-top: 20px;
    width: 2669.5px;
    height: 1546px;
    margin-left: 77px;

    .con-bottom-box {
      height: 1420px;
      padding: 75px 60px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}
</style>
