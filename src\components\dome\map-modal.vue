<template>
  <el-dialog :visible="dialogVisible" width="500px" :show-close="false" :before-close="handleClose" :append-to-body="true"
    class="map-modal" :modal="false" >
    <!-- 鼠标移出浮窗和点击浮窗 关闭浮窗 -->
    <div class="modal-content" @mouseleave="handleClose" @click="handleClose">
      <div class="title"  @click.stop="handleTitleClick">
        <img src="@/assets/image/left_2x.png" class="title-left-img" alt="" />
        <span class="title-text">{{ districtName }}</span>
        <img src="@/assets/image/right_2x.png" class="title-right-img" alt="" />
      </div>
      <div class="content-box">
        <!-- 联络站数量 -->
        <div class="station-count">
          <div class="section-title">
            <img src="@/assets/image/region-left-botton-2.png" class="title-bg" />
            <span class="text">{{ districtName }}联络站数量</span>
          </div>
          <div class="table-wrapper">
            <div class="table-con table-item">
              <span class="td2">中心联络站</span>
              <span class="td3">{{ modalForm.centerNum }}<span class="unit">个</span></span>
            </div>
            <div class="table-con table-item">
              <span class="td2">片区联络站</span>
              <span class="td3">{{ modalForm.areaNum }}<span class="unit">个</span></span>
            </div>
            <div class="table-con table-item">
              <span class="td2">其中：优秀联络站</span>
              <span class="td3">{{ modalForm.excellentNum }}<span class="unit">个</span></span>
            </div>
          </div>
          <div class="icon-list">
            <div class="icon-item" v-for="(item, index) in modalForm.excellentList" :key="index">
              <img src="@/assets/image/house_2x.png" class="house-icon" alt="" />
              <span class="td2">{{ item.name }}</span>
            </div>
          </div>
          <div class="bottom-lxy"
            style="border-bottom:1px solid #4e99f8;height:20px;position: absolute;width: 269px;left:-1px;bottom:-26px;">
            <img src="@/assets/image/region-left-botton-bg.png" class="title-bg" style="width: 100%;height: 100%;" />
          </div>
        </div>

        <!-- 五级代表驻站分布情况 -->
        <div class="rep-distribution">
          <div class="section-title">
            <img src="@/assets/image/region-left-botton-2.png" class="title-bg" />
            <span class="text">五级代表驻站分布情况</span>
          </div>
          <div class="distribution-list">
            <div class="list-item">
              <span class="item-name">全国代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.country }}<span class="unit">人</span></span>
            </div>
            <div class="list-item">
              <span class="item-name">省级代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.province }}<span class="unit">人</span></span>
            </div>
            <div class="list-item">
              <span class="item-name">市级代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.city }}<span class="unit">人</span></span>
            </div>
            <div class="list-item">
              <span class="item-name">区级代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.administrativeArea }}<span class="unit">人</span></span>
            </div>
            <div class="list-item">
              <span class="item-name">镇级代表</span>
              <span class="item-value">{{ modalForm.dbNumCount.streetTown }}<span class="unit">人</span></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { number } from 'echarts';
export default {
  name: "mapModal",
  props: {
    modalForm: {
      type: Object,
      default: () => null,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    districtName: {
      type: String,
      default: () => null,
    },
    administrativeAreaId: {
      type: Number,
      default: () => 0,
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      normalStationList: [
        { name: '中心联络站', value: 6 },
        { name: '片区联络站', value: 14 },
        { name: '其中：优秀联络站', value: 2 }
      ],
      iconStationList: [
        { name: '广州市花都区太平镇中心联络站' },
        { name: '广州市花都区吕田镇中心联络站' }
      ],
      representativeList: [
        { name: '全国代表', value: 1 },
        { name: '省级代表', value: 46 },
        { name: '市级代表', value: 46 },
        { name: '区级代表', value: 46 },
        { name: '镇级代表', value: 46 }
      ]
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    },
  },
  methods: {
    handleTitleClick() {
      if (this.administrativeAreaId) {
        this.$router.push({
          path: '/junior',
          query: {
            administrativeAreaId: this.administrativeAreaId,
            areaName: this.districtName
          }
        });
      }
    },

    handleClose() {
      this.$emit("update:visible", false);

      console.log(this.modalForm);
    },
    modalCancel() {
      this.$emit("update:visible", false);
      this.$emit("cancel", this.modalForm);
    },
    modalOk() {
      this.$emit("update:visible", false);
      this.$emit("confirm", this.modalForm);
    },
  },
};
</script>

<style lang="less" scoped>
/*.el-dialog__wrapper {
  position: fixed;
  top: 40px;
  right: 0;
  bottom: 0;
  left: 500px;
  overflow: auto;
  margin: 0;
}*/
.map-modal {
  ::v-deep .el-dialog {
    background: transparent;
    border-radius: 8px;
    width: 310px !important;


    .el-dialog__header {
      display: none;
    }

    .el-dialog__body {
      background: rgba(3, 26, 68, 0.9);
      padding: 0;
      position: relative;

      .modal-content {
        padding: 20px;

        .title {
          cursor: pointer;
          position: relative;
          color: #fff;
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 20px;
          margin-top: 0px;
          padding: 5px 0;
          letter-spacing: 8px;
          text-shadow: 0 0 10px rgba(41, 241, 250, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;

          .title-left-img,
          .title-right-img {
            width: 18px;
            height: 18px;
          }

          .title-text {
            margin: 0 20px;
            line-height: 1;
          }

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            height: 80%;
            background: linear-gradient(90deg,
                rgba(41, 241, 250, 0) 0%,
                rgba(41, 241, 250, 0.1) 50%,
                rgba(41, 241, 250, 0) 100%);
            filter: blur(5px);
            z-index: -1;
          }
        }

        .content-box {
          .section-title {
            position: relative;
            height: 50px;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-left: 55px;

            .title-bg {
              position: absolute;
              width: 100%;
              height: 100%;
              left: 0;
              top: 0;
            }

            .text {
              color: #fff;
              font-size: 14px;
              font-style: italic;
              position: relative;
              z-index: 1;
              line-height: 50px;
              margin-top: 9px;
              // 字间距为1px
              letter-spacing: 1px;

            }
          }

          .station-count {
            position: relative;
            margin-bottom: 45px;

            .table-wrapper {
              padding: 0 5px;

              .table-con {
                position: relative;
                display: flex;
                align-items: center;
                height: 25px;
                margin-bottom: 8px;
              }

              .table-item {
                background: transparent;
                margin: 5px 0;
                padding: 1px 1px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: relative;
                border-left: 2px solid #1b4d71;
                border-bottom: 1px solid rgba(113, 161, 255, 0.11);

                .td2 {
                  color: #8cc8f4;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                  font-size: 14px;
                  letter-spacing: 2px;
                  padding-left: 15px;
                  flex: 1;
                }

                .td3 {
                  color: #fff;
                  font-size: 16px;
                  font-weight: bold;
                  width: 80px;
                  text-align: right;
                  padding-right: 10px;

                  .unit {
                    color: #2c7bb3;
                    font-size: 14px;
                    text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                    margin-left: 4px;
                  }
                }
              }
            }

            .icon-list {
              padding: 0 8px;

              .icon-item {
                background: transparent;
                margin: 15px 0;
                padding: 1px 1px;
                display: flex;
                align-items: center;
                position: relative;
                height: 35px;


                .house-icon {
                  position: absolute;
                  left: -18px;
                  top: -7px;
                  width: 55px;
                  height: 45px;
                }

                .td2 {
                  border-top: 1px solid rgba(113, 161, 255, 0.11);
                  border-bottom: 1px solid rgba(113, 161, 255, 0.11);
                  color: #fff;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                  font-size: 13px;
                  letter-spacing: 1px;
                  margin-left: 40px;
                  padding: 10px 0;
                  flex: 1;
                }
              }
            }
          }

          .distribution-list {
            padding: 0 5px;

            .list-item {
              position: relative;
              display: flex;
              align-items: center;
              height: 25px;
              margin-bottom: 8px;
              background: transparent;
              border-left: 2px solid #1b4d71;
              border-bottom: 1px solid rgba(113, 161, 255, 0.11);
              margin: 5px 0;
              padding: 1px 1px;

              .item-name {
                flex: 1;
                padding-left: 15px;
                color: #8cc8f4;
                text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                font-size: 14px;
                letter-spacing: 2px;
              }

              .item-value {
                width: 80px;
                text-align: right;
                padding-right: 10px;
                color: #fff;
                font-size: 16px;
                font-weight: bold;

                .unit {
                  color: #2c7bb3;
                  font-size: 14px;
                  margin-left: 4px;
                  text-shadow: 0px 0px 7px rgba(68, 142, 254, 0.71);
                }
              }
            }
          }
        }

        .close-btn {
          display: none;
        }
      }
    }
  }
}
</style>
